import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  CreditCard,
  FileText,
  Upload,
  CheckCircle,
  AlertCircle,
  Save,
  RefreshCw,
  X,
  Bot
} from 'lucide-react';
import AdminSidebar from './AdminSidebar';
import individualService from '../../../services/individualService';


const IndividualForm = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    registrationType: 'individual',
    fullName: '',
    gender: '',
    dateOfBirth: '',
    email: '',
    contactNumber: '',
    alternativeContactNumber: '',
    address: '',
    panNumber: '',
    aadharNumber: '',
    stateCity: '',
    allocatedDrones: 0,
    idProof: null,
    kyc: null
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState({
    idProof: null,
    kyc: null
  });

  // Validation functions
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone) => {
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  const validatePAN = (pan) => {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(pan);
  };

  const validateAadhar = (aadhar) => {
    const aadharRegex = /^\d{12}$/;
    return aadharRegex.test(aadhar);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!formData.gender) newErrors.gender = 'Gender is required';
    if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    if (!formData.contactNumber.trim()) {
      newErrors.contactNumber = 'Contact number is required';
    } else if (!validatePhone(formData.contactNumber)) {
      newErrors.contactNumber = 'Please enter a valid 10-digit mobile number';
    }
    if (formData.alternativeContactNumber && !validatePhone(formData.alternativeContactNumber)) {
      newErrors.alternativeContactNumber = 'Please enter a valid 10-digit mobile number';
    }
    if (!formData.address.trim()) newErrors.address = 'Address is required';
    if (!formData.panNumber.trim()) {
      newErrors.panNumber = 'PAN number is required';
    } else if (!validatePAN(formData.panNumber.toUpperCase())) {
      newErrors.panNumber = 'Please enter a valid PAN number (e.g., **********)';
    }
    if (!formData.aadharNumber.trim()) {
      newErrors.aadharNumber = 'Aadhar number is required';
    } else if (!validateAadhar(formData.aadharNumber)) {
      newErrors.aadharNumber = 'Please enter a valid 12-digit Aadhar number';
    }
    if (!formData.stateCity.trim()) newErrors.stateCity = 'State/City is required';

    // Allocated drones validation
    const allocatedDrones = parseInt(formData.allocatedDrones);
    if (isNaN(allocatedDrones) || allocatedDrones < 0) {
      newErrors.allocatedDrones = 'Allocated drones must be a non-negative number';
    } else if (allocatedDrones > 100) {
      newErrors.allocatedDrones = 'Allocated drones cannot exceed 100';
    }

    if (!uploadedFiles.idProof) newErrors.idProof = 'ID proof is required';
    if (!uploadedFiles.kyc) newErrors.kyc = 'KYC document is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === 'registrationType' && value === 'organization') {
      navigate('/organizationform');
      return;
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Format PAN number to uppercase
    const formattedValue = name === 'panNumber' ? value.toUpperCase() : value;

    setFormData(prev => ({
      ...prev,
      [name]: formattedValue
    }));
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    const file = files[0];

    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({
          ...prev,
          [name]: 'File size should be less than 5MB'
        }));
        return;
      }

      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          [name]: 'Only PDF, JPG, JPEG, and PNG files are allowed'
        }));
        return;
      }

      // Clear error and set file
      if (errors[name]) {
        setErrors(prev => ({
          ...prev,
          [name]: ''
        }));
      }

      setUploadedFiles(prev => ({
        ...prev,
        [name]: file
      }));

      setFormData(prev => ({
        ...prev,
        [name]: file
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API - following the same pattern as OrganizationForm
      const individualApiData = {
        fullName: formData.fullName.trim(),
        gender: formData.gender,
        dateOfBirth: formData.dateOfBirth,
        contact: {
          primaryEmail: formData.email.trim().toLowerCase(),
          phone: formData.contactNumber.trim(),
          alternativePhone: formData.alternativeContactNumber?.trim() || undefined
        },
        address: {
          street: formData.address.trim(),
          city: formData.stateCity.split(',')[1]?.trim() || formData.stateCity.trim(),
          state: formData.stateCity.split(',')[0]?.trim() || 'Delhi',
          country: 'India',
          postalCode: '110001' // Default postal code - you can add this field to form later
        },
        documents: {
          panNumber: formData.panNumber.trim().toUpperCase(),
          aadharNumber: formData.aadharNumber.trim(),
          idProofPath: uploadedFiles.idProof?.name || 'id-proof-uploaded.pdf',
          kycDocumentPath: uploadedFiles.kyc?.name || undefined
        },
        allocatedDrones: parseInt(formData.allocatedDrones) || 0,
        status: 'pending'
      };

      // Remove undefined and empty values - same as OrganizationForm
      const cleanData = JSON.parse(JSON.stringify(individualApiData, (key, value) => {
        if (value === undefined || value === null || value === '') {
          return undefined;
        }
        return value;
      }));

      // Call the API
      const response = await individualService.createIndividual(cleanData);

      if (response.success) {
        setShowSuccess(true);

        // Reset form after successful submission - same as OrganizationForm
        setTimeout(() => {
          setShowSuccess(false);
          // Navigate to individuals page (you can create this later)
          navigate('/admin/individuals');
        }, 2000); // Same 2 seconds as OrganizationForm
      }

    } catch (error) {
      setErrors({ submit: error.message || 'Failed to submit form. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    setFormData({
      registrationType: 'individual',
      fullName: '',
      gender: '',
      dateOfBirth: '',
      email: '',
      contactNumber: '',
      alternativeContactNumber: '',
      address: '',
      panNumber: '',
      aadharNumber: '',
      stateCity: '',
      idProof: null,
      kyc: null
    });
    setUploadedFiles({
      idProof: null,
      kyc: null
    });
    setErrors({});
  };

  return (
    <div className="min-h-screen bg-gray-50 text-black">
      <AdminSidebar />
      <div className="pl-[250px] w-full">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4 text-black sticky top-0 z-10">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <User className="text-blue-600" />
                Individual Registration
              </h2>
              <p className="text-gray-600 mt-1">
                Register as an individual drone operator
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                type="button"
                onClick={handleReset}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                Reset Form
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-4 md:p-6">
          {/* Success Message */}
          {showSuccess && (
            <div className="w-full mb-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h3 className="text-green-800 font-medium">Registration Successful!</h3>
                  <p className="text-green-700 text-sm">Your individual registration has been submitted successfully.</p>
                </div>
              </div>
            </div>
          )}

          <div className="w-full bg-white rounded-xl shadow-sm border border-gray-200">
            <form onSubmit={handleSubmit} className="h-full">
              {/* Registration Type Selector */}
              <div className="p-4 md:p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Registration Type</h3>
                <div className="flex flex-wrap items-center gap-6">
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="registrationType"
                      value="individual"
                      checked={formData.registrationType === 'individual'}
                      onChange={handleInputChange}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-900">Individual</span>
                  </label>
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="registrationType"
                      value="organization"
                      checked={formData.registrationType === 'organization'}
                      onChange={handleInputChange}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-900">Organization</span>
                  </label>
                </div>
              </div>

              {/* Personal Details */}
              <div className="p-4 md:p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <User className="w-5 h-5 text-blue-600" />
                  Personal Details
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
                  {/* Full Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      placeholder="Enter your full name"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.fullName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.fullName && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.fullName}
                      </p>
                    )}
                  </div>

                  {/* Gender */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Gender *
                    </label>
                    <select
                      name="gender"
                      value={formData.gender}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.gender ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select Gender</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                      <option value="other">Other</option>
                    </select>
                    {errors.gender && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.gender}
                      </p>
                    )}
                  </div>

                  {/* Date of Birth */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Date of Birth *
                    </label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="date"
                        name="dateOfBirth"
                        value={formData.dateOfBirth}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.dateOfBirth ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.dateOfBirth && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.dateOfBirth}
                      </p>
                    )}
                  </div>

                  {/* Email */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="Enter your email address"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.email}
                      </p>
                    )}
                  </div>

                  {/* Contact Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contact Number *
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="tel"
                        name="contactNumber"
                        value={formData.contactNumber}
                        onChange={handleInputChange}
                        placeholder="Enter 10-digit mobile number"
                        maxLength="10"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.contactNumber ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.contactNumber && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.contactNumber}
                      </p>
                    )}
                  </div>

                  {/* Alternative Contact Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Alternative Contact Number
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="tel"
                        name="alternativeContactNumber"
                        value={formData.alternativeContactNumber}
                        onChange={handleInputChange}
                        placeholder="Enter alternative mobile number"
                        maxLength="10"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.alternativeContactNumber ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.alternativeContactNumber && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.alternativeContactNumber}
                      </p>
                    )}
                  </div>

                  {/* Address - Full Width */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Address *
                    </label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                      <textarea
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        placeholder="Enter your complete address"
                        rows="3"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                          errors.address ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.address && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.address}
                      </p>
                    )}
                  </div>

                  {/* State/City */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      State/City *
                    </label>
                    <input
                      type="text"
                      name="stateCity"
                      value={formData.stateCity}
                      onChange={handleInputChange}
                      placeholder="Enter state and city"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.stateCity ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {errors.stateCity && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.stateCity}
                      </p>
                    )}
                  </div>

                  {/* PAN Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      PAN Number *
                    </label>
                    <div className="relative">
                      <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        name="panNumber"
                        value={formData.panNumber}
                        onChange={handleInputChange}
                        placeholder="**********"
                        maxLength="10"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors uppercase ${
                          errors.panNumber ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.panNumber && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.panNumber}
                      </p>
                    )}
                  </div>

                  {/* Aadhar Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Aadhar Number *
                    </label>
                    <div className="relative">
                      <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        name="aadharNumber"
                        value={formData.aadharNumber}
                        onChange={handleInputChange}
                        placeholder="Enter 12-digit Aadhar number"
                        maxLength="12"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.aadharNumber ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.aadharNumber && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.aadharNumber}
                      </p>
                    )}
                  </div>

                  {/* Allocated Drones */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Allocated Drones *
                    </label>
                    <div className="relative">
                      <Bot className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="number"
                        name="allocatedDrones"
                        value={formData.allocatedDrones}
                        onChange={handleInputChange}
                        placeholder="Enter number of drones to allocate"
                        min="0"
                        max="100"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors.allocatedDrones ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.allocatedDrones && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.allocatedDrones}
                      </p>
                    )}
                    <p className="mt-1 text-sm text-gray-500">
                      Number of drones to be allocated to this individual (0-100)
                    </p>
                  </div>
                </div>
              </div>

              {/* KYC Documents */}
              <div className="p-4 md:p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <FileText className="w-5 h-5 text-blue-600" />
                  KYC Documents
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
                  {/* ID Proof Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ID Proof (Aadhar/Passport) *
                    </label>
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                      errors.idProof ? 'border-red-300 bg-red-50' :
                      uploadedFiles.idProof ? 'border-green-300 bg-green-50' :
                      'border-gray-300 hover:border-gray-400'
                    }`}>
                      {uploadedFiles.idProof ? (
                        <div className="flex items-center justify-center gap-3">
                          <CheckCircle className="w-8 h-8 text-green-600" />
                          <div>
                            <p className="text-sm font-medium text-green-800">{uploadedFiles.idProof.name}</p>
                            <p className="text-xs text-green-600">
                              {(uploadedFiles.idProof.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              setUploadedFiles(prev => ({ ...prev, idProof: null }));
                              setFormData(prev => ({ ...prev, idProof: null }));
                            }}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="w-5 h-5" />
                          </button>
                        </div>
                      ) : (
                        <>
                          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                          <p className="text-sm text-gray-600 mb-2">Drop your file here or</p>
                          <label className="cursor-pointer">
                            <span className="text-blue-600 hover:text-blue-700 underline font-medium">browse</span>
                            <input
                              type="file"
                              name="idProof"
                              onChange={handleFileChange}
                              className="hidden"
                              accept=".pdf,.jpg,.jpeg,.png"
                            />
                          </label>
                          <p className="text-xs text-gray-500 mt-2">PDF, JPG, JPEG, PNG (Max 5MB)</p>
                        </>
                      )}
                    </div>
                    {errors.idProof && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.idProof}
                      </p>
                    )}
                  </div>

                  {/* KYC Document Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      KYC Document *
                    </label>
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                      errors.kyc ? 'border-red-300 bg-red-50' :
                      uploadedFiles.kyc ? 'border-green-300 bg-green-50' :
                      'border-gray-300 hover:border-gray-400'
                    }`}>
                      {uploadedFiles.kyc ? (
                        <div className="flex items-center justify-center gap-3">
                          <CheckCircle className="w-8 h-8 text-green-600" />
                          <div>
                            <p className="text-sm font-medium text-green-800">{uploadedFiles.kyc.name}</p>
                            <p className="text-xs text-green-600">
                              {(uploadedFiles.kyc.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              setUploadedFiles(prev => ({ ...prev, kyc: null }));
                              setFormData(prev => ({ ...prev, kyc: null }));
                            }}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="w-5 h-5" />
                          </button>
                        </div>
                      ) : (
                        <>
                          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                          <p className="text-sm text-gray-600 mb-2">Drop your file here or</p>
                          <label className="cursor-pointer">
                            <span className="text-blue-600 hover:text-blue-700 underline font-medium">browse</span>
                            <input
                              type="file"
                              name="kyc"
                              onChange={handleFileChange}
                              className="hidden"
                              accept=".pdf,.jpg,.jpeg,.png"
                            />
                          </label>
                          <p className="text-xs text-gray-500 mt-2">PDF, JPG, JPEG, PNG (Max 5MB)</p>
                        </>
                      )}
                    </div>
                    {errors.kyc && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.kyc}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Submit Section */}
              <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4 md:p-6">
                {errors.submit && (
                  <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                    <AlertCircle className="w-5 h-5 text-red-600" />
                    <p className="text-red-800">{errors.submit}</p>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                  <button
                    type="button"
                    onClick={handleReset}
                    className="w-full sm:w-auto flex items-center justify-center gap-2 px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Reset Form
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full sm:w-auto flex items-center justify-center gap-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                  >
                    {isSubmitting ? (
                      <>
                        <RefreshCw className="w-4 h-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        Submit Registration
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IndividualForm;










