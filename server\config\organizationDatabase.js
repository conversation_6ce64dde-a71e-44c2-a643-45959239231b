const mongoose = require('mongoose');

// Separate database connection for organization data
let orgConnection = null;

const connectOrgDB = async () => {
  try {
    // Use a separate database for organization data
    const orgDbUri = process.env.ORG_MONGODB_URI || 
      process.env.MONGODB_URI?.replace('/shakti_drone_management', '/shakti_organization_data') ||
      'mongodb://localhost:27017/shakti_organization_data';

    console.log('🔗 Connecting to Organization Database...');
    
    orgConnection = await mongoose.createConnection(orgDbUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log(`✅ Organization Database Connected: ${orgConnection.host}`);
    
    // Handle connection events
    orgConnection.on('error', (err) => {
      console.error('❌ Organization DB connection error:', err);
    });

    orgConnection.on('disconnected', () => {
      console.log('⚠️ Organization Database disconnected');
    });

    return orgConnection;

  } catch (error) {
    console.error('❌ Organization Database connection failed:', error.message);
    throw error;
  }
};

const getOrgConnection = () => {
  if (!orgConnection) {
    throw new Error('Organization database not connected. Call connectOrgDB() first.');
  }
  return orgConnection;
};

// Graceful shutdown
process.on('SIGINT', async () => {
  if (orgConnection) {
    await orgConnection.close();
    console.log('🔒 Organization Database connection closed through app termination');
  }
});

module.exports = {
  connectOrgDB,
  getOrgConnection
};
