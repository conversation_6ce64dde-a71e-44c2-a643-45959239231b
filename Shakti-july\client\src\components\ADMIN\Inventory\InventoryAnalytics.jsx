import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import {
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Battery,
  Settings,
  Download,
  RefreshCw,
  Calendar,
  Filter,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon
} from 'lucide-react';
import {
  FaPlane,
  FaBatteryFull,
  FaBatteryHalf,
  FaBatteryQuarter,
  FaExclamationTriangle,
  FaCheckCircle,
  FaCog,
  FaChartLine,
  FaDownload
} from 'react-icons/fa';

import AdminSidebar from '../common/AdminSidebar';

const InventoryAnalytics = () => {
  const navigate = useNavigate();
  const [selectedTimeRange, setSelectedTimeRange] = useState('30d');
  const [selectedMetric, setSelectedMetric] = useState('all');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Mock data for analytics
  const droneStatusData = [
    { name: 'Active', value: 45, color: '#10b981' },
    { name: 'Maintenance', value: 12, color: '#f59e0b' },
    { name: 'Inactive', value: 8, color: '#6b7280' },
    { name: 'Crashed', value: 3, color: '#ef4444' }
  ];

  const utilizationData = [
    { month: 'Jan', utilization: 85, efficiency: 92 },
    { month: 'Feb', utilization: 78, efficiency: 88 },
    { month: 'Mar', utilization: 92, efficiency: 95 },
    { month: 'Apr', utilization: 88, efficiency: 91 },
    { month: 'May', utilization: 95, efficiency: 97 },
    { month: 'Jun', utilization: 89, efficiency: 93 }
  ];

  const maintenanceData = [
    { week: 'Week 1', scheduled: 15, completed: 14, pending: 1 },
    { week: 'Week 2', scheduled: 18, completed: 16, pending: 2 },
    { week: 'Week 3', scheduled: 12, completed: 12, pending: 0 },
    { week: 'Week 4', scheduled: 20, completed: 18, pending: 2 }
  ];

  const batteryHealthData = [
    { range: '90-100%', count: 28, color: '#10b981' },
    { range: '70-89%', count: 22, color: '#f59e0b' },
    { range: '50-69%', count: 12, color: '#ef4444' },
    { range: '<50%', count: 6, color: '#dc2626' }
  ];

  const flightHoursData = [
    { month: 'Jan', hours: 1250 },
    { month: 'Feb', hours: 1180 },
    { month: 'Mar', hours: 1420 },
    { month: 'Apr', hours: 1350 },
    { month: 'May', hours: 1580 },
    { month: 'Jun', hours: 1480 }
  ];

  // Calculate key metrics
  const totalDrones = droneStatusData.reduce((sum, item) => sum + item.value, 0);
  const activeRate = ((droneStatusData[0].value / totalDrones) * 100).toFixed(1);
  const maintenanceRate = ((droneStatusData[1].value / totalDrones) * 100).toFixed(1);
  const avgUtilization = utilizationData.reduce((sum, item) => sum + item.utilization, 0) / utilizationData.length;
  const totalFlightHours = flightHoursData.reduce((sum, item) => sum + item.hours, 0);

  const handleRefresh = () => {
    setIsRefreshing(true);
    setTimeout(() => {
      setIsRefreshing(false);
    }, 2000);
  };

  const handleExport = () => {
    console.log('Exporting analytics data...');
    // Create a simple CSV export
    const csvData = [
      ['Metric', 'Value'],
      ['Total Drones', totalDrones],
      ['Active Rate', `${activeRate}%`],
      ['Maintenance Rate', `${maintenanceRate}%`],
      ['Average Utilization', `${avgUtilization.toFixed(1)}%`],
      ['Total Flight Hours', totalFlightHours]
    ];
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'inventory-analytics.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
      <AdminSidebar />
      
      {/* Main Content */}
      <div className="lg:pl-[250px] w-full">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/inventory')}
                className="flex items-center gap-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <ArrowLeft size={16} />
                <span className="hidden sm:inline">Back to Inventory</span>
              </button>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <FaChartLine className="text-blue-600" />
                  Inventory Analytics
                </h2>
                <p className="text-gray-600 mt-1">Comprehensive drone inventory insights and reports</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <select
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
              
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <RefreshCw size={18} className={isRefreshing ? 'animate-spin' : ''} />
                <span className="hidden sm:inline">Refresh</span>
              </button>
              
              <button
                onClick={handleExport}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download size={18} />
                <span className="hidden sm:inline">Export</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 lg:p-6 space-y-6">
          {/* Key Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Drones</p>
                  <p className="text-2xl font-bold text-gray-900">{totalDrones}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <FaPlane className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+5.2% from last month</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{activeRate}%</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+2.1% from last month</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Utilization</p>
                  <p className="text-2xl font-bold text-gray-900">{avgUtilization.toFixed(1)}%</p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                  <Activity className="w-6 h-6 text-orange-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                <span className="text-sm text-red-600">-1.3% from last month</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Flight Hours</p>
                  <p className="text-2xl font-bold text-gray-900">{totalFlightHours.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <Clock className="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">+8.7% from last month</span>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Drone Status Distribution */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <PieChartIcon className="w-5 h-5 text-blue-600" />
                  Drone Status Distribution
                </h3>
                <button className="text-gray-400 hover:text-gray-600">
                  <Settings size={16} />
                </button>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={droneStatusData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {droneStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="grid grid-cols-2 gap-4 mt-4">
                {droneStatusData.map((item, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: item.color }}
                    ></div>
                    <span className="text-sm text-gray-600">{item.name}: {item.value}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Utilization Trend */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <BarChart3 className="w-5 h-5 text-green-600" />
                  Utilization & Efficiency Trend
                </h3>
                <button className="text-gray-400 hover:text-gray-600">
                  <Settings size={16} />
                </button>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={utilizationData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="utilization"
                      stroke="#10b981"
                      strokeWidth={2}
                      name="Utilization %"
                    />
                    <Line
                      type="monotone"
                      dataKey="efficiency"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      name="Efficiency %"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Additional Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Maintenance Overview */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <FaCog className="text-orange-600" />
                  Maintenance Overview
                </h3>
                <button className="text-gray-400 hover:text-gray-600">
                  <Settings size={16} />
                </button>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={maintenanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="week" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="completed" fill="#10b981" name="Completed" />
                    <Bar dataKey="pending" fill="#f59e0b" name="Pending" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Battery Health Distribution */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Battery className="w-5 h-5 text-yellow-600" />
                  Battery Health Distribution
                </h3>
                <button className="text-gray-400 hover:text-gray-600">
                  <Settings size={16} />
                </button>
              </div>
              <div className="space-y-4">
                {batteryHealthData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: item.color }}
                      ></div>
                      <span className="text-sm font-medium text-gray-700">{item.range}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 rounded-full"
                          style={{
                            backgroundColor: item.color,
                            width: `${(item.count / totalDrones) * 100}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600 w-8">{item.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Flight Hours Trend */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Clock className="w-5 h-5 text-purple-600" />
                Flight Hours Trend
              </h3>
              <div className="flex items-center gap-2">
                <button className="text-gray-400 hover:text-gray-600">
                  <Filter size={16} />
                </button>
                <button className="text-gray-400 hover:text-gray-600">
                  <Settings size={16} />
                </button>
              </div>
            </div>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={flightHoursData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Area
                    type="monotone"
                    dataKey="hours"
                    stroke="#8b5cf6"
                    fill="#8b5cf6"
                    fillOpacity={0.3}
                    name="Flight Hours"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InventoryAnalytics;
