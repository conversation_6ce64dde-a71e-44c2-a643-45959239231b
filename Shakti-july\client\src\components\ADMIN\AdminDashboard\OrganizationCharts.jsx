import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
  PieChart,
  Pie
} from 'recharts';
import { Building2, Loader2, AlertCircle } from 'lucide-react';

const OrganizationCharts = () => {
  const [monthlyData, setMonthlyData] = useState([]);
  const [statusData, setStatusData] = useState([]);
  const [stats, setStats] = useState({
    totalRegistered: 0,
    approved: 0,
    pending: 0,
    avgPerMonth: 0
  });
  const [viewType, setViewType] = useState('monthly');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch organization analytics data
  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('http://localhost:5000/api/organizations/analytics');
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const result = await response.json();
      if (result.success) {
        setMonthlyData(result.data.monthlyData);
        setStatusData(result.data.statusData);
        setStats(result.data.stats);
      } else {
        throw new Error(result.message || 'Failed to fetch analytics data');
      }
    } catch (err) {
      console.error('Error fetching analytics data:', err);
      setError(err.message);
      // Set fallback data
      setMonthlyData([]);
      setStatusData([]);
      setStats({
        totalRegistered: 0,
        approved: 0,
        pending: 0,
        avgPerMonth: 0
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();

    // Set up periodic refresh every 30 seconds
    const interval = setInterval(fetchAnalyticsData, 30000);
    return () => clearInterval(interval);
  }, []);

  const colors = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444',
    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316',
    '#ec4899', '#6366f1', '#14b8a6', '#eab308',
  ];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800 mb-2">{`Month: ${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const PieTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800">{data.name}</p>
          <p style={{ color: data.payload.color }} className="text-sm">
            {`Count: ${data.value}`}
          </p>
          <p className="text-xs text-gray-500">
            {`${((data.value / statusData.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}%`}
          </p>
        </div>
      );
    }
    return null;
  };

  const totalOrgs = stats.totalRegistered;
  const avgPerMonth = stats.avgPerMonth;

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <Building2 className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-800">
                Organization Analytics
              </h2>
              <p className="text-sm text-gray-500">
                Registration trends and status overview
              </p>
            </div>
          </div>
        </div>
        <div className="p-6 flex items-center justify-center h-[400px]">
          <div className="flex items-center gap-3 text-gray-500">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Loading analytics data...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <Building2 className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-800">
                Organization Analytics
              </h2>
              <p className="text-sm text-gray-500">
                Registration trends and status overview
              </p>
            </div>
          </div>
        </div>
        <div className="p-6 flex items-center justify-center h-[400px]">
          <div className="flex flex-col items-center gap-3 text-gray-500">
            <AlertCircle className="w-8 h-8 text-red-500" />
            <span className="text-center">
              Failed to load analytics data
              <br />
              <button
                onClick={fetchAnalyticsData}
                className="text-blue-600 hover:text-blue-700 underline mt-2"
              >
                Try again
              </button>
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="p-6 border-b border-gray-100">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <Building2 className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-800">
                Organization Analytics
              </h2>
              <p className="text-sm text-gray-500">
                Registration trends and status overview
              </p>
            </div>
          </div>

          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewType('monthly')}
              className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                viewType === 'monthly'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setViewType('status')}
              className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                viewType === 'status'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Status
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {viewType === 'monthly' ? (
          <div className="w-full h-[350px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                layout="vertical"
                data={monthlyData}
                margin={{ top: 20, right: 30, left: 40, bottom: 20 }}
                barSize={20}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                <XAxis type="number" stroke="#64748b" fontSize={12} />
                <YAxis
                  type="category"
                  dataKey="month"
                  stroke="#64748b"
                  fontSize={12}
                  width={50}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="orgs" radius={[0, 4, 4, 0]}>
                  {monthlyData.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <div className="flex flex-col lg:flex-row items-center gap-8">
            <div className="w-full lg:w-1/2 h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={120}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {statusData.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={statusData[index].color} />
                    ))}
                  </Pie>
                  <Tooltip content={<PieTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>

            <div className="w-full lg:w-1/2 space-y-4">
              {statusData.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: item.color }}
                    ></div>
                    <span className="font-medium text-gray-700">{item.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-gray-800">{item.value}</div>
                    <div className="text-xs text-gray-500">
                      {((item.value / statusData.reduce((sum, i) => sum + i.value, 0)) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="px-6 pb-6">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-xl font-bold text-blue-600">{totalOrgs}</div>
            <div className="text-xs text-blue-600 font-medium">Total Registered</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-xl font-bold text-green-600">{stats.approved}</div>
            <div className="text-xs text-green-600 font-medium">Approved</div>
          </div>
          <div className="text-center p-3 bg-amber-50 rounded-lg">
            <div className="text-xl font-bold text-amber-600">{stats.pending}</div>
            <div className="text-xs text-amber-600 font-medium">Pending</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-xl font-bold text-gray-600">{avgPerMonth}</div>
            <div className="text-xs text-gray-600 font-medium">Avg/Month</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrganizationCharts;
