import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  BarChart3,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Eye,
  Edit,
  Trash2,
  TrendingUp,
  TrendingDown,
  Activity,
  Zap,
  Battery,
  Gauge,
  Target,
  Download
} from 'lucide-react';

const PerformanceAnalytics = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterPeriod, setFilterPeriod] = useState('all');
  const [filterDrone, setFilterDrone] = useState('all');
  const [filterMetric, setFilterMetric] = useState('all');
  const [analytics, setAnalytics] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);

  // Form state for new analysis
  const [newAnalysis, setNewAnalysis] = useState({
    droneId: '',
    droneName: '',
    analyst: '',
    date: '',
    time: '',
    period: 'Weekly',
    reportType: 'Comprehensive Performance',
    notes: ''
  });

  // Initialize with sample performance analytics data
  React.useEffect(() => {
    if (analytics.length === 0) {
      setAnalytics([
        {
          id: 'PA-2024-001',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          analyst: 'John Smith',
          date: '2024-01-15',
          time: '16:30',
          period: 'Weekly',
          reportType: 'Comprehensive Performance',
          overallScore: 92,
          trend: 'Improving',
          metrics: {
            flightEfficiency: { score: 94, trend: 'up', change: '+3%', benchmark: 90 },
            batteryPerformance: { score: 89, trend: 'stable', change: '0%', benchmark: 85 },
            systemReliability: { score: 96, trend: 'up', change: '+2%', benchmark: 95 },
            missionSuccess: { score: 98, trend: 'up', change: '+1%', benchmark: 95 },
            dataQuality: { score: 91, trend: 'down', change: '-1%', benchmark: 90 },
            operationalCost: { score: 87, trend: 'up', change: '+4%', benchmark: 80 }
          },
          flightStats: {
            totalFlights: 45,
            totalHours: 67.5,
            avgFlightTime: '1.5 hours',
            successRate: '98%',
            avgSpeed: '15 km/h',
            maxAltitude: '120m'
          },
          issues: ['Minor data quality degradation in windy conditions'],
          recommendations: ['Optimize flight patterns for wind resistance', 'Update sensor calibration'],
          keyInsights: [
            'Flight efficiency improved by 3% this week',
            'System reliability exceeds benchmark by 1%',
            'Mission success rate remains excellent at 98%'
          ],
          nextAnalysis: '2024-01-22'
        },
        {
          id: 'PA-2024-002',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          analyst: 'Sarah Johnson',
          date: '2024-01-14',
          time: '14:15',
          period: 'Monthly',
          reportType: 'Battery Performance Analysis',
          overallScore: 78,
          trend: 'Declining',
          metrics: {
            flightEfficiency: { score: 82, trend: 'down', change: '-5%', benchmark: 90 },
            batteryPerformance: { score: 71, trend: 'down', change: '-8%', benchmark: 85 },
            systemReliability: { score: 85, trend: 'stable', change: '0%', benchmark: 95 },
            missionSuccess: { score: 89, trend: 'down', change: '-3%', benchmark: 95 },
            dataQuality: { score: 88, trend: 'stable', change: '0%', benchmark: 90 },
            operationalCost: { score: 65, trend: 'down', change: '-12%', benchmark: 80 }
          },
          flightStats: {
            totalFlights: 38,
            totalHours: 52.3,
            avgFlightTime: '1.4 hours',
            successRate: '89%',
            avgSpeed: '13 km/h',
            maxAltitude: '115m'
          },
          issues: ['Battery degradation affecting flight time', 'Increased operational costs', 'Reduced flight efficiency'],
          recommendations: ['Replace battery pack immediately', 'Conduct comprehensive system check', 'Review maintenance schedule'],
          keyInsights: [
            'Battery performance declined 8% this month',
            'Operational costs increased due to frequent charging',
            'Flight efficiency below benchmark by 8%'
          ],
          nextAnalysis: '2024-02-14'
        },
        {
          id: 'PA-2024-003',
          droneId: 'DRN-005',
          droneName: 'Mapper Gamma',
          analyst: 'Mike Wilson',
          date: '2024-01-13',
          time: '11:45',
          period: 'Daily',
          reportType: 'Mission Performance Review',
          overallScore: 95,
          trend: 'Stable',
          metrics: {
            flightEfficiency: { score: 97, trend: 'up', change: '+1%', benchmark: 90 },
            batteryPerformance: { score: 94, trend: 'stable', change: '0%', benchmark: 85 },
            systemReliability: { score: 98, trend: 'stable', change: '0%', benchmark: 95 },
            missionSuccess: { score: 100, trend: 'stable', change: '0%', benchmark: 95 },
            dataQuality: { score: 96, trend: 'up', change: '+2%', benchmark: 90 },
            operationalCost: { score: 91, trend: 'up', change: '+3%', benchmark: 80 }
          },
          flightStats: {
            totalFlights: 12,
            totalHours: 18.7,
            avgFlightTime: '1.6 hours',
            successRate: '100%',
            avgSpeed: '16 km/h',
            maxAltitude: '125m'
          },
          issues: [],
          recommendations: ['Continue current operational procedures', 'Monitor for consistency'],
          keyInsights: [
            'Perfect mission success rate achieved',
            'Data quality improved by 2%',
            'All metrics exceed benchmarks'
          ],
          nextAnalysis: '2024-01-14'
        }
      ]);
    }
  }, [analytics.length]);

  // Add new analysis function
  const handleAddAnalysis = (e) => {
    e.preventDefault();

    // Generate mock performance data
    const mockMetrics = {
      flightEfficiency: { score: Math.floor(Math.random() * 20) + 80, trend: 'stable', change: '0%', benchmark: 90 },
      batteryPerformance: { score: Math.floor(Math.random() * 20) + 80, trend: 'stable', change: '0%', benchmark: 85 },
      systemReliability: { score: Math.floor(Math.random() * 20) + 80, trend: 'stable', change: '0%', benchmark: 95 },
      missionSuccess: { score: Math.floor(Math.random() * 20) + 80, trend: 'stable', change: '0%', benchmark: 95 },
      dataQuality: { score: Math.floor(Math.random() * 20) + 80, trend: 'stable', change: '0%', benchmark: 90 },
      operationalCost: { score: Math.floor(Math.random() * 20) + 80, trend: 'stable', change: '0%', benchmark: 80 }
    };

    const scores = Object.values(mockMetrics).map(m => m.score);
    const overallScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);

    let trend = 'Stable';
    if (overallScore > 90) trend = 'Improving';
    else if (overallScore < 80) trend = 'Declining';

    const newAnalysisRecord = {
      id: `PA-2024-${String(analytics.length + 1).padStart(3, '0')}`,
      ...newAnalysis,
      overallScore,
      trend,
      metrics: mockMetrics,
      keyInsights: ['New analysis generated with baseline metrics'],
      recommendations: ['Monitor performance trends', 'Schedule regular reviews'],
      nextAnalysis: new Date(Date.now() + 7*24*60*60*1000).toISOString().split('T')[0]
    };

    setAnalytics([...analytics, newAnalysisRecord]);
    setNewAnalysis({
      droneId: '',
      droneName: '',
      analyst: '',
      date: '',
      time: '',
      period: 'Weekly',
      reportType: 'Comprehensive Performance',
      notes: ''
    });
    setShowAddModal(false);
  };

  // Delete analytics function
  const handleDeleteAnalytics = (id) => {
    setAnalytics(analytics.filter(item => item.id !== id));
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendColor = (trend) => {
    switch (trend.toLowerCase()) {
      case 'improving':
      case 'up': return 'text-green-600';
      case 'declining':
      case 'down': return 'text-red-600';
      case 'stable': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getTrendIcon = (trend) => {
    switch (trend.toLowerCase()) {
      case 'improving':
      case 'up': return <TrendingUp className="w-3 h-3" />;
      case 'declining':
      case 'down': return <TrendingDown className="w-3 h-3" />;
      case 'stable': return <Activity className="w-3 h-3" />;
      default: return <Activity className="w-3 h-3" />;
    }
  };

  const getOverallTrendColor = (trend) => {
    switch (trend) {
      case 'Improving': return 'bg-green-100 text-green-700 border-green-200';
      case 'Declining': return 'bg-red-100 text-red-700 border-red-200';
      case 'Stable': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const filteredAnalytics = analytics.filter(item => {
    const matchesSearch = item.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.droneName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.reportType.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesPeriod = filterPeriod === 'all' || item.period.toLowerCase() === filterPeriod.toLowerCase();
    const matchesDrone = filterDrone === 'all' || item.droneId === filterDrone;
    const matchesMetric = filterMetric === 'all' || true; // Can be expanded for specific metrics
    return matchesSearch && matchesPeriod && matchesDrone && matchesMetric;
  });

  const headerActions = (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search analytics..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
          onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
          onBlur={(e) => e.target.style.boxShadow = 'none'}
        />
      </div>
      
      <select
        value={filterPeriod}
        onChange={(e) => setFilterPeriod(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Periods</option>
        <option value="daily">Daily</option>
        <option value="weekly">Weekly</option>
        <option value="monthly">Monthly</option>
        <option value="quarterly">Quarterly</option>
      </select>

      <button
        className="px-4 py-2 text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors flex items-center gap-2"
      >
        <Download className="w-4 h-4" />
        Export Report
      </button>

      <button
        onClick={() => setShowAddModal(true)}
        className="px-4 py-2 text-white rounded-lg transition-all duration-150 hover:shadow-lg hover:-translate-y-0.5 flex items-center gap-2"
        style={{backgroundColor: '#e0e7ff'}}
        onMouseEnter={(e) => e.target.style.backgroundColor = '#c7d2fe'}
        onMouseLeave={(e) => e.target.style.backgroundColor = '#e0e7ff'}
      >
        <Plus className="w-4 h-4 text-blue-600" />
        <span className="text-blue-700 font-medium">New Analysis</span>
      </button>
    </div>
  );

  return (
    <QCLayout
      title="Performance Analytics"
      subtitle="Analyze and monitor drone performance metrics and trends"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold text-gray-900">{filteredAnalytics.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <BarChart3 className="w-3 h-3" />
                  Generated
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <BarChart3 className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Performance</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredAnalytics.length > 0 
                    ? Math.round(filteredAnalytics.reduce((sum, a) => sum + a.overallScore, 0) / filteredAnalytics.length)
                    : 0
                  }%
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <TrendingUp className="w-3 h-3" />
                  Above target
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <Gauge className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Improving</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredAnalytics.filter(a => a.trend === 'Improving').length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <TrendingUp className="w-3 h-3" />
                  Positive trend
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <TrendingUp className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Needs Attention</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredAnalytics.filter(a => a.trend === 'Declining' || a.overallScore < 80).length}
                </p>
                <p className="text-sm text-red-600 flex items-center gap-1 mt-1">
                  <TrendingDown className="w-3 h-3" />
                  Action required
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fef2f2'}}>
                <AlertTriangle className="w-6 h-6 text-red-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Performance Analytics Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Performance Analysis Reports</h3>
                <p className="text-sm text-gray-600 mt-1">Comprehensive performance metrics and trend analysis</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Analysis Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Overall Performance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Key Metrics
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAnalytics.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50 transition-colors">
                    {/* Analysis Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <BarChart3 className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{item.id}</h4>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                              {item.period}
                            </span>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-900">{item.droneId} - {item.droneName}</p>
                            <p className="text-xs text-gray-600">{item.reportType}</p>
                            <div className="flex items-center gap-3 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {item.analyst}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {item.date} at {item.time}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Overall Performance */}
                    <td className="px-6 py-4">
                      <div className="text-center">
                        <div className={`text-2xl font-bold ${getScoreColor(item.overallScore)}`}>
                          {item.overallScore}%
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div
                            className={`h-2 rounded-full ${item.overallScore >= 90 ? 'bg-green-500' : item.overallScore >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}
                            style={{width: `${item.overallScore}%`}}
                          ></div>
                        </div>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border mt-2 ${getOverallTrendColor(item.trend)}`}>
                          {getTrendIcon(item.trend)}
                          <span className="ml-1">{item.trend}</span>
                        </span>
                        <p className="text-xs text-gray-500 mt-1">Next: {item.nextAnalysis}</p>
                      </div>
                    </td>

                    {/* Key Metrics */}
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Target className="w-3 h-3" />
                            Flight Efficiency
                          </span>
                          <div className="flex items-center gap-1">
                            <span className={`text-xs font-medium ${getScoreColor(item.metrics.flightEfficiency.score)}`}>
                              {item.metrics.flightEfficiency.score}%
                            </span>
                            <span className={`text-xs ${getTrendColor(item.metrics.flightEfficiency.trend)}`}>
                              {getTrendIcon(item.metrics.flightEfficiency.trend)}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Battery className="w-3 h-3" />
                            Battery Performance
                          </span>
                          <div className="flex items-center gap-1">
                            <span className={`text-xs font-medium ${getScoreColor(item.metrics.batteryPerformance.score)}`}>
                              {item.metrics.batteryPerformance.score}%
                            </span>
                            <span className={`text-xs ${getTrendColor(item.metrics.batteryPerformance.trend)}`}>
                              {getTrendIcon(item.metrics.batteryPerformance.trend)}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-600 flex items-center gap-1">
                            <Activity className="w-3 h-3" />
                            System Reliability
                          </span>
                          <div className="flex items-center gap-1">
                            <span className={`text-xs font-medium ${getScoreColor(item.metrics.systemReliability.score)}`}>
                              {item.metrics.systemReliability.score}%
                            </span>
                            <span className={`text-xs ${getTrendColor(item.metrics.systemReliability.trend)}`}>
                              {getTrendIcon(item.metrics.systemReliability.trend)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                          onClick={() => navigate(`/qc-diagnostics/performance-analytics/${item.id}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          className="p-2 text-gray-400 hover:text-green-600 transition-colors rounded-lg hover:bg-green-50"
                          title="Edit Analysis"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteAnalytics(item.id)}
                          title="Delete Analysis"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add Analysis Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">New Performance Analysis</h3>
                <p className="text-sm text-gray-600 mt-1">Create a new performance analysis report</p>
              </div>

              <form onSubmit={handleAddAnalysis} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone ID</label>
                    <input
                      type="text"
                      value={newAnalysis.droneId}
                      onChange={(e) => setNewAnalysis({...newAnalysis, droneId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., DRN-001"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone Name</label>
                    <input
                      type="text"
                      value={newAnalysis.droneName}
                      onChange={(e) => setNewAnalysis({...newAnalysis, droneName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Surveyor Alpha"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Analyst</label>
                    <input
                      type="text"
                      value={newAnalysis.analyst}
                      onChange={(e) => setNewAnalysis({...newAnalysis, analyst: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., John Smith"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Period</label>
                    <select
                      value={newAnalysis.period}
                      onChange={(e) => setNewAnalysis({...newAnalysis, period: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="Daily">Daily</option>
                      <option value="Weekly">Weekly</option>
                      <option value="Monthly">Monthly</option>
                      <option value="Quarterly">Quarterly</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                    <input
                      type="date"
                      value={newAnalysis.date}
                      onChange={(e) => setNewAnalysis({...newAnalysis, date: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Time</label>
                    <input
                      type="time"
                      value={newAnalysis.time}
                      onChange={(e) => setNewAnalysis({...newAnalysis, time: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                  <select
                    value={newAnalysis.reportType}
                    onChange={(e) => setNewAnalysis({...newAnalysis, reportType: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="Comprehensive Performance">Comprehensive Performance</option>
                    <option value="Battery Performance Analysis">Battery Performance Analysis</option>
                    <option value="Flight Efficiency Report">Flight Efficiency Report</option>
                    <option value="System Reliability Analysis">System Reliability Analysis</option>
                    <option value="Mission Success Review">Mission Success Review</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                  <textarea
                    value={newAnalysis.notes}
                    onChange={(e) => setNewAnalysis({...newAnalysis, notes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Additional notes about the analysis..."
                  />
                </div>

                <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Analysis
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </QCLayout>
  );
};

export default PerformanceAnalytics;
