import React from 'react';
import {
  MapPin,
  Activity,
  CheckCircle,
  Calendar,
  SortAsc,
  Filter,
  X
} from 'lucide-react';

const DroneFilters = ({
  selectedRegion,
  setSelectedRegion,
  selectedStatus,
  setSelectedStatus,
  selectedDeployment,
  setSelectedDeployment,
  sortOrder,
  setSortOrder
}) => {
  const regions = [
    'Maharashtra',
    'Karnataka', 
    'Gujarat',
    'Punjab',
    'Haryana',
    'Rajasthan',
    'Uttar Pradesh',
    'Tamil Nadu'
  ];

  const statuses = [
    'Active',
    'Inactive',
    'Maintenance',
    'Crashed',
    'Approval Pending'
  ];

  const deploymentOptions = [
    { value: 'Deployed', label: 'Deployed' },
    { value: 'Not Deployed', label: 'Not Deployed' }
  ];

  const sortOptions = [
    { value: 'latest', label: 'Latest Deployed' },
    { value: 'oldest', label: 'Oldest Deployed' },
    { value: 'name', label: 'Name (A-Z)' },
    { value: 'efficiency', label: 'Efficiency (High to Low)' },
    { value: 'battery', label: 'Battery Level (High to Low)' }
  ];

  const clearAllFilters = () => {
    setSelectedRegion('');
    setSelectedStatus('');
    setSelectedDeployment('');
    setSortOrder('');
  };

  const hasActiveFilters = selectedRegion || selectedStatus || selectedDeployment || sortOrder;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-slideInDown">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-800">Advanced Filters</h3>
        </div>
        
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="flex items-center gap-2 px-3 py-1.5 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200"
          >
            <X className="w-4 h-4" />
            Clear All
          </button>
        )}
      </div>

      <div className="drone-filter-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        {/* Region Filter */}
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
            <MapPin className="w-4 h-4 text-gray-500" />
            Region
          </label>
          <select
            value={selectedRegion}
            onChange={(e) => setSelectedRegion(e.target.value)}
            className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
          >
            <option value="">All Regions</option>
            {regions.map((region) => (
              <option key={region} value={region}>
                {region}
              </option>
            ))}
          </select>
          {selectedRegion && (
            <div className="flex items-center gap-1 text-xs text-blue-600">
              <CheckCircle className="w-3 h-3" />
              Filter applied
            </div>
          )}
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
            <Activity className="w-4 h-4 text-gray-500" />
            Status
          </label>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
          >
            <option value="">All Status</option>
            {statuses.map((status) => (
              <option key={status} value={status}>
                {status}
              </option>
            ))}
          </select>
          {selectedStatus && (
            <div className="flex items-center gap-1 text-xs text-blue-600">
              <CheckCircle className="w-3 h-3" />
              Filter applied
            </div>
          )}
        </div>

        {/* Deployment Filter */}
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
            <Calendar className="w-4 h-4 text-gray-500" />
            Deployment
          </label>
          <select
            value={selectedDeployment}
            onChange={(e) => setSelectedDeployment(e.target.value)}
            className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
          >
            <option value="">All Deployment</option>
            {deploymentOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {selectedDeployment && (
            <div className="flex items-center gap-1 text-xs text-blue-600">
              <CheckCircle className="w-3 h-3" />
              Filter applied
            </div>
          )}
        </div>

        {/* Sort Filter */}
        <div className="space-y-2">
          <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
            <SortAsc className="w-4 h-4 text-gray-500" />
            Sort By
          </label>
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value)}
            className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
          >
            <option value="">Default Order</option>
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {sortOrder && (
            <div className="flex items-center gap-1 text-xs text-blue-600">
              <CheckCircle className="w-3 h-3" />
              Sort applied
            </div>
          )}
        </div>
      </div>

      {/* Filter Summary */}
      {hasActiveFilters && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span className="font-medium">Active filters:</span>
            <div className="flex flex-wrap gap-2">
              {selectedRegion && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                  <MapPin className="w-3 h-3" />
                  {selectedRegion}
                  <button
                    onClick={() => setSelectedRegion('')}
                    className="ml-1 hover:text-blue-900"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              )}
              {selectedStatus && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                  <Activity className="w-3 h-3" />
                  {selectedStatus}
                  <button
                    onClick={() => setSelectedStatus('')}
                    className="ml-1 hover:text-green-900"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              )}
              {selectedDeployment && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                  <Calendar className="w-3 h-3" />
                  {selectedDeployment}
                  <button
                    onClick={() => setSelectedDeployment('')}
                    className="ml-1 hover:text-purple-900"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              )}
              {sortOrder && (
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs">
                  <SortAsc className="w-3 h-3" />
                  {sortOptions.find(opt => opt.value === sortOrder)?.label}
                  <button
                    onClick={() => setSortOrder('')}
                    className="ml-1 hover:text-orange-900"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DroneFilters;
