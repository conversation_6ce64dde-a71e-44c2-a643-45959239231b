# 🚁 SHAKTI Drone Management Admin Portal - Complete Documentation

## 📋 Table of Contents
1. [System Overview](#system-overview)
2. [Frontend Architecture](#frontend-architecture)
3. [Backend API Documentation](#backend-api-documentation)
4. [Database Schema](#database-schema)
5. [Authentication & Authorization](#authentication--authorization)
6. [Admin Portal Components](#admin-portal-components)
7. [API Endpoints Reference](#api-endpoints-reference)
8. [Database Models](#database-models)

---

## 🎯 System Overview

The SHAKTI Drone Management Admin Portal is a comprehensive web application for managing drone operations, organizations, individuals, and system notifications. Built with React.js frontend and Node.js/Express backend with MongoDB database.

### Technology Stack
- **Frontend**: React.js, Tailwind CSS, Lucide Icons
- **Backend**: Node.js, Express.js, MongoDB, Mongoose
- **Authentication**: JWT (JSON Web Tokens)
- **Real-time**: WebSocket connections for live updates
- **File Storage**: Local file system with path references

---

## 🎨 Frontend Architecture

### Main Application Structure
```
client/src/
├── components/
│   ├── ADMIN/
│   │   ├── AdminDashboard/
│   │   ├── AdminNotification/
│   │   ├── OrganizationPages/
│   │   ├── IndividualPages/
│   │   ├── DronePage/
│   │   ├── AdminMap/
│   │   ├── Inventory/
│   │   ├── Deployment/
│   │   └── common/
│   ├── ORG/
│   └── QC/
├── services/
├── context/
└── routes/
```

### Route Configuration
```javascript
// Admin Routes (Protected)
/adminsidebar          - Admin Sidebar Navigation
/admindashboard        - Main Admin Dashboard
/organizationpage      - Organization Management
/organizationform      - Add/Edit Organization
/individualpage        - Individual Management
/individualform        - Add/Edit Individual
/dronepage            - Drone Management
/adddrone             - Add New Drone
/dronelogs            - Drone Activity Logs
/map                  - Interactive Map View
/orgstatsmap          - Organization Statistics Map
/admin-notification   - Notification Center
/inventory            - Inventory Management
/inventory-analytics  - Inventory Analytics
/admindeployment      - Deployment Management
```

---

## 🔧 Backend API Documentation

### Base Configuration
- **Base URL**: `http://localhost:5000/api`
- **Authentication**: Bearer Token (JWT)
- **Content-Type**: `application/json`

### API Response Format
```javascript
// Success Response
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ },
  "timestamp": "2025-08-03T11:00:00.000Z"
}

// Error Response
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information",
  "timestamp": "2025-08-03T11:00:00.000Z"
}
```

---

## 🔐 Authentication & Authorization

### User Roles
1. **Admin** (`admin`)
   - Full system access
   - Manage all organizations, individuals, drones
   - Access to all admin features
   - User management capabilities

2. **Organization** (`org`)
   - Limited to organization-specific data
   - Manage own drones and users
   - View organization analytics

3. **Maintenance** (`maintenance`)
   - Quality control and maintenance features
   - Drone diagnostics and reports

### Authentication Flow
```javascript
// Login Request
POST /api/auth/login
{
  "username": "admin",
  "password": "password123",
  "role": "admin" // optional
}

// Login Response
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin"
    },
    "token": "jwt_token_here",
    "expiresIn": "7d"
  }
}
```

---

## 🏗️ Admin Portal Components

### 1. AdminSidebar Component
**Location**: `components/ADMIN/common/AdminSidebar.jsx`

**Navigation Items**:
- 🏠 **Dashboard** (`/admindashboard`) - Main admin dashboard
- 🗺️ **Map** (`/map`) - View organizations and drones on map
- 🏢 **Organizations** (`/organizationpage`) - Manage organizations
- 👤 **Individuals** (`/individualpage`) - Manage individual registrations
- 📦 **Inventory** (`/inventory`) - Manage drone inventory
- 🚚 **Deployment** (`/admindeployment`) - Manage drone deployments
- 🚁 **Drones** (`/dronepage`) - Drone management
- 🔔 **Notifications** (`/admin-notification`) - Notification center

**Features**:
- Active route highlighting
- Responsive design
- User profile dropdown
- Settings modal
- Logout functionality

### 2. AdminDashboard Component
**Location**: `components/ADMIN/AdminDashboard/AdminDashboard.jsx`

**Key Features**:
- **Statistics Cards**: 
  - Drones in Inventory
  - Registered Organizations  
  - Deployed Drones
  - Maintenance Alerts
- **Interactive Map**: Real-time drone and organization locations
- **Charts & Analytics**: Drone and organization performance metrics
- **Activity Feed**: Recent system activities
- **Quick Actions**: 
  - Add Organization button
  - Add Drone button
- **Search Functionality**: Global search across entities
- **Real-time Updates**: Live data refresh

**State Management**:
```javascript
const [dashboardData, setDashboardData] = useState({
  dronesInInventory: 0,
  registeredOrgs: 0,
  deployedDrones: 0,
  maintenanceDrones: 0
});
```

### 3. AdminNotification Component
**Location**: `components/ADMIN/AdminNotification/AdminNotification.jsx`

**Sub-components**:
- **NotificationStats**: Statistics overview
- **NotificationFilters**: Advanced filtering options
- **NotificationList**: Paginated notification list
- **NotificationModal**: Detailed notification view

**Features**:
- **Filtering Options**:
  - Priority (Critical, High, Medium, Low)
  - Category (System, User Management, Drone Operations)
  - Status (All, Read, Unread, Archived)
  - Date Range (Today, Yesterday, Week, Month)
  - Organization filter
- **Bulk Actions**:
  - Mark all as read
  - Archive selected
  - Delete selected
- **Real-time Updates**: Live notification feed
- **Pagination**: Server-side pagination for performance

### 4. OrganizationPage Component
**Location**: `components/ADMIN/OrganizationPages/OrganizationPage.jsx`

**Features**:
- **Organization List**: Paginated table with search
- **CRUD Operations**: Create, Read, Update, Delete
- **Status Management**: Active, Inactive, Suspended, Pending
- **Verification System**: Admin verification workflow
- **Soft Delete**: 48-hour grace period with restore option
- **Drone Allocation**: Assign drones to organizations
- **Analytics Integration**: Organization performance metrics

### 5. IndividualPage Component  
**Location**: `components/ADMIN/IndividualPages/IndividualPage.jsx`

**Features**:
- **Individual Registration Management**
- **Document Verification**: PAN, Aadhar validation
- **Status Workflow**: Pending → Approved/Rejected
- **KYC Document Management**
- **Drone Allocation Tracking**
- **Search & Filter**: By name, email, status, documents

### 6. DronePage Component
**Location**: `components/ADMIN/DronePage/DronePage.jsx`

**Features**:
- **Drone Inventory Management**
- **Status Tracking**: Available, Deployed, Maintenance, Retired
- **Assignment Management**: Assign to organizations/individuals
- **Maintenance Scheduling**
- **Flight Logs**: Historical flight data
- **Real-time Monitoring**: Live drone status updates

---

## 📊 API Endpoints Reference

### Authentication Endpoints
```javascript
POST   /api/auth/login           - User login
POST   /api/auth/register        - Register new user (Admin only)
GET    /api/auth/me              - Get current user profile
PUT    /api/auth/profile         - Update user profile
PUT    /api/auth/change-password - Change password
POST   /api/auth/logout          - User logout
```

### Organization Endpoints
```javascript
GET    /api/organizations        - Get all organizations
GET    /api/organizations/stats  - Get organization statistics
GET    /api/organizations/analytics - Get analytics data
GET    /api/organizations/:id    - Get organization by ID
POST   /api/organizations        - Create new organization
PUT    /api/organizations/:id    - Update organization
PUT    /api/organizations/:id/status - Update organization status
PUT    /api/organizations/:id/verify - Verify organization
DELETE /api/organizations/:id    - Soft delete organization
POST   /api/organizations/:id/restore - Restore deleted organization
GET    /api/organizations/deleted - Get deleted organizations
```

### Individual Endpoints
```javascript
GET    /api/individuals          - Get all individuals
GET    /api/individuals/stats    - Get individual statistics
GET    /api/individuals/:id      - Get individual by ID
POST   /api/individuals          - Create new individual
PUT    /api/individuals/:id      - Update individual
PUT    /api/individuals/:id/status - Update individual status
DELETE /api/individuals/:id      - Delete individual
```

### Drone Endpoints
```javascript
GET    /api/drones               - Get all drones
GET    /api/drones/stats         - Get drone statistics
GET    /api/drones/analytics     - Get drone analytics
GET    /api/drones/:id           - Get drone by ID
POST   /api/drones               - Create new drone
PUT    /api/drones/:id           - Update drone
PUT    /api/drones/:id/status    - Update drone status
DELETE /api/drones/:id           - Delete drone
POST   /api/drones/:id/assign    - Assign drone
POST   /api/drones/:id/maintenance - Schedule maintenance
```

### Notification Endpoints
```javascript
GET    /api/notifications        - Get all notifications
GET    /api/notifications/stats  - Get notification statistics
PUT    /api/notifications/:id/read - Mark notification as read
PUT    /api/notifications/read-all - Mark all as read
PUT    /api/notifications/:id/archive - Archive notification
DELETE /api/notifications/:id    - Delete notification
```

---

## 🗄️ Database Models

### User Model
```javascript
{
  username: String (required, unique, 3-30 chars),
  email: String (required, unique, valid email),
  password: String (required, min 6 chars, hashed),
  role: String (enum: ['admin', 'org', 'maintenance']),
  isActive: Boolean (default: true),
  lastLogin: Date,
  profile: {
    adminLevel: String (enum: ['super', 'standard']),
    organizationId: ObjectId (ref: 'Organization'),
    organizationName: String,
    department: String,
    certifications: [String]
  },
  timestamps: true
}
```

### Organization Model
```javascript
{
  name: String (required, 2-100 chars),
  displayName: String (max 100 chars),
  description: String (max 500 chars),
  type: String (enum: ['government', 'private', 'ngo', 'research', 'military', 'other']),
  registration: {
    registrationNumber: String (required, unique),
    registrationDate: Date,
    expiryDate: Date,
    issuingAuthority: String,
    documentPath: String
  },
  contact: {
    primaryEmail: String (required, unique),
    secondaryEmail: String,
    phone: String (required),
    alternativePhone: String,
    website: String
  },
  address: {
    street: String (required),
    city: String (required),
    state: String (required),
    country: String (required, default: 'India'),
    postalCode: String (required),
    coordinates: {
      type: 'Point',
      coordinates: [Number] // [longitude, latitude]
    }
  },
  status: String (enum: ['active', 'inactive', 'suspended', 'pending'], default: 'pending'),
  isVerified: Boolean (default: false),
  verificationDate: Date,
  verifiedBy: ObjectId (ref: 'User'),
  allocatedDrones: Number (default: 0, min: 0, max: 1000),
  subscription: {
    plan: String (enum: ['basic', 'standard', 'premium', 'enterprise']),
    startDate: Date,
    endDate: Date,
    maxDrones: Number (default: 5),
    maxUsers: Number (default: 3)
  },
  // Soft delete fields
  isDeleted: Boolean (default: false),
  deletedAt: Date,
  deletedBy: ObjectId (ref: 'User'),
  permanentDeleteAt: Date,
  // Audit fields
  createdBy: ObjectId (ref: 'User', required),
  lastModifiedBy: ObjectId (ref: 'User'),
  timestamps: true
}
```

### Individual Model
```javascript
{
  registrationType: String (enum: ['individual'], default: 'individual'),
  fullName: String (required, 2-100 chars),
  gender: String (enum: ['male', 'female', 'other'], required),
  dateOfBirth: Date (required, age 18-100),
  contact: {
    primaryEmail: String (required, unique),
    phone: String (required),
    alternativePhone: String
  },
  address: {
    street: String (required),
    city: String (required),
    state: String (required),
    country: String (default: 'India'),
    postalCode: String (required),
    coordinates: {
      type: 'Point',
      coordinates: [Number]
    }
  },
  documents: {
    panNumber: String (required, unique, format: **********),
    aadharNumber: String (required, unique, 12 digits),
    idProofPath: String (required),
    kycDocumentPath: String
  },
  status: String (enum: ['pending', 'approved', 'rejected', 'suspended'], default: 'pending'),
  isVerified: Boolean (default: false),
  verificationDate: Date,
  verifiedBy: ObjectId (ref: 'User'),
  allocatedDrones: Number (default: 0, min: 0, max: 100),
  createdBy: ObjectId (ref: 'User', required),
  lastModifiedBy: ObjectId (ref: 'User', required),
  timestamps: true
}
```

### Drone Model
```javascript
{
  serialNumber: String (required, unique),
  model: String (required),
  manufacturer: String (required),
  type: String (enum: ['quadcopter', 'hexacopter', 'octocopter', 'fixed-wing', 'hybrid']),
  specifications: {
    maxFlightTime: Number (minutes),
    maxRange: Number (kilometers),
    maxPayload: Number (kilograms),
    maxAltitude: Number (meters),
    camera: {
      resolution: String,
      hasGimbal: Boolean,
      hasNightVision: Boolean
    },
    gps: Boolean,
    sensors: [String]
  },
  status: String (enum: ['available', 'deployed', 'maintenance', 'retired'], default: 'available'),
  condition: String (enum: ['excellent', 'good', 'fair', 'poor'], default: 'excellent'),
  organizationId: ObjectId (ref: 'Organization'),
  assignedTo: ObjectId (ref: 'User'),
  location: {
    type: 'Point',
    coordinates: [Number]
  },
  lastMaintenance: Date,
  nextMaintenance: Date,
  flightHours: Number (default: 0),
  batteryLevel: Number (0-100),
  createdBy: ObjectId (ref: 'User', required),
  lastModifiedBy: ObjectId (ref: 'User'),
  timestamps: true
}
```

### Notification Model
```javascript
{
  title: String (required, max 200 chars),
  message: String (required, max 1000 chars),
  type: String (enum: [
    'organization_created', 'organization_updated', 'organization_deleted',
    'drone_created', 'drone_updated', 'drone_deleted', 'drone_status_changed',
    'individual_created', 'individual_updated', 'individual_deleted',
    'user_created', 'user_updated', 'system_alert', 'maintenance_due',
    'battery_low', 'mission_completed', 'geofence_breach', 'weather_alert'
  ], required),
  category: String (enum: ['system', 'user_management', 'drone_operations', 'maintenance', 'security'], required),
  priority: String (enum: ['low', 'medium', 'high', 'critical'], default: 'medium'),
  status: String (enum: ['unread', 'read', 'archived'], default: 'unread'),
  triggeredBy: {
    userId: ObjectId (ref: 'User'),
    username: String,
    role: String
  },
  relatedEntity: {
    entityType: String (enum: ['organization', 'drone', 'individual', 'user']),
    entityId: ObjectId,
    entityName: String
  },
  metadata: Object, // Additional context data
  readAt: Date,
  archivedAt: Date,
  timestamps: true
}
```

---

## 🎛️ Admin Portal UI Components Detailed

### Dashboard Statistics Cards
```javascript
// StatCard Component Props
{
  title: String,        // "Drones In Inventory"
  value: Number,        // 150
  color: String,        // "green", "blue", "purple", "amber"
  percentage: String,   // "+24%"
  icon: ReactElement    // <Boxes className="text-green-600" />
}
```

### Quick Action Buttons
```javascript
// Dashboard Quick Actions
<button onClick={() => navigate('/organizationform')}>
  <Plus size={16} /> Add Organization
</button>
<button onClick={() => navigate('/adddrone')}>
  <Plus size={16} /> Add Drone
</button>
<button onClick={() => navigate('/individualform')}>
  <Plus size={16} /> Add Individual
</button>
```

### Search Functionality
```javascript
// Global Search Component
const [searchQuery, setSearchQuery] = useState('');
const [searchResults, setSearchResults] = useState([]);

// Search across organizations, individuals, drones
const handleSearch = async (query) => {
  const results = await api.get(`/search?q=${query}`);
  setSearchResults(results.data);
};
```

### Filter Components
```javascript
// Organization Filters
{
  status: ['all', 'active', 'inactive', 'suspended', 'pending'],
  type: ['all', 'government', 'private', 'ngo', 'research', 'military', 'other'],
  verified: ['all', 'verified', 'unverified'],
  dateRange: ['all', 'today', 'week', 'month', 'year']
}

// Individual Filters
{
  status: ['all', 'pending', 'approved', 'rejected', 'suspended'],
  verified: ['all', 'verified', 'unverified'],
  gender: ['all', 'male', 'female', 'other'],
  ageRange: ['all', '18-25', '26-35', '36-50', '50+']
}

// Drone Filters
{
  status: ['all', 'available', 'deployed', 'maintenance', 'retired'],
  type: ['all', 'quadcopter', 'hexacopter', 'octocopter', 'fixed-wing', 'hybrid'],
  condition: ['all', 'excellent', 'good', 'fair', 'poor'],
  organization: ['all', ...organizationList]
}
```

---

## 🔄 Real-time Features & WebSocket Events

### Dashboard Real-time Updates
```javascript
// WebSocket event listeners
socket.on('stats-updated', (data) => {
  setDashboardData(data);
});

socket.on('drone-status-changed', (drone) => {
  updateDroneStatus(drone.id, drone.status);
});

socket.on('notification-received', (notification) => {
  addNotification(notification);
  showToast(notification.title);
});
```

### Map Real-time Updates
```javascript
// Live drone tracking
socket.on('drone-location-updated', (data) => {
  updateDroneMarker(data.droneId, data.coordinates);
});

socket.on('geofence-breach', (alert) => {
  showGeofenceAlert(alert);
  highlightDrone(alert.droneId);
});
```

---

## 🎨 UI/UX Design Patterns

### Color Scheme
```css
/* Primary Colors */
--primary-blue: #3B82F6;
--primary-green: #10B981;
--primary-purple: #8B5CF6;
--primary-amber: #F59E0B;
--primary-red: #EF4444;

/* Status Colors */
--status-active: #10B981;
--status-inactive: #6B7280;
--status-pending: #F59E0B;
--status-suspended: #EF4444;

/* Background Colors */
--bg-primary: #F9FAFB;
--bg-secondary: #FFFFFF;
--bg-sidebar: #1F2937;
```

### Typography
```css
/* Font Sizes */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */

/* Font Weights */
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### Component Styling
```javascript
// Button Variants
const buttonStyles = {
  primary: "bg-blue-600 text-white hover:bg-blue-700",
  secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300",
  success: "bg-green-600 text-white hover:bg-green-700",
  danger: "bg-red-600 text-white hover:bg-red-700",
  warning: "bg-amber-600 text-white hover:bg-amber-700"
};

// Status Badges
const statusBadges = {
  active: "bg-green-100 text-green-800",
  inactive: "bg-gray-100 text-gray-800",
  pending: "bg-yellow-100 text-yellow-800",
  suspended: "bg-red-100 text-red-800"
};
```

---

## 📊 Data Visualization Components

### Chart Types
```javascript
// Dashboard Charts
1. Bar Charts - Organization statistics by type
2. Line Charts - Drone deployment trends over time
3. Pie Charts - Status distribution (active/inactive/maintenance)
4. Area Charts - Flight hours and usage patterns
5. Donut Charts - Organization verification status
6. Scatter Plots - Drone performance metrics
```

### Chart Libraries
```javascript
// Chart.js configuration
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Chart components
- DroneCharts.jsx - Drone-related visualizations
- OrganizationCharts.jsx - Organization analytics
- PerformanceCharts.jsx - System performance metrics
```

---

## 🔐 Security Implementation Details

### Input Validation Rules
```javascript
// Organization validation
const organizationValidation = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z0-9\s\-\.]+$/
  },
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    unique: true
  },
  phone: {
    required: true,
    pattern: /^\+?[1-9]\d{1,14}$/
  },
  registrationNumber: {
    required: true,
    unique: true,
    pattern: /^[A-Z0-9]{6,20}$/
  }
};

// Individual validation
const individualValidation = {
  fullName: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z\s]+$/
  },
  panNumber: {
    required: true,
    pattern: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
    unique: true
  },
  aadharNumber: {
    required: true,
    pattern: /^\d{12}$/,
    unique: true
  }
};
```

### File Upload Security
```javascript
// File upload configuration
const uploadConfig = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ],
  uploadPath: './uploads/',
  virusScan: true,
  encryption: true
};

// File validation middleware
const validateFile = (req, res, next) => {
  const file = req.file;

  if (!file) {
    return res.status(400).json({ error: 'No file uploaded' });
  }

  if (file.size > uploadConfig.maxFileSize) {
    return res.status(400).json({ error: 'File too large' });
  }

  if (!uploadConfig.allowedTypes.includes(file.mimetype)) {
    return res.status(400).json({ error: 'Invalid file type' });
  }

  next();
};
```

---

## 📱 Responsive Design

### Breakpoints
```css
/* Mobile First Approach */
/* xs: 0px - 639px */
/* sm: 640px - 767px */
/* md: 768px - 1023px */
/* lg: 1024px - 1279px */
/* xl: 1280px - 1535px */
/* 2xl: 1536px+ */

/* Responsive Grid */
.dashboard-grid {
  @apply grid grid-cols-1 gap-4;
  @apply sm:grid-cols-2;
  @apply lg:grid-cols-3;
  @apply xl:grid-cols-4;
}

/* Responsive Sidebar */
.sidebar {
  @apply hidden lg:block lg:w-64;
  @apply fixed inset-y-0 left-0;
}

.mobile-sidebar {
  @apply lg:hidden;
  @apply fixed inset-0 z-50;
}
```

### Mobile Navigation
```javascript
// Mobile menu component
const MobileMenu = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="lg:hidden">
      <button onClick={() => setIsOpen(true)}>
        <Menu size={24} />
      </button>

      {isOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50">
          <div className="fixed inset-y-0 left-0 w-64 bg-white">
            {/* Mobile navigation items */}
          </div>
        </div>
      )}
    </div>
  );
};
```

---

## 🧪 Testing Strategy

### Unit Tests
```javascript
// Component testing with Jest & React Testing Library
describe('AdminDashboard', () => {
  test('renders dashboard statistics', () => {
    render(<AdminDashboard />);
    expect(screen.getByText('Drones In Inventory')).toBeInTheDocument();
  });

  test('handles refresh button click', () => {
    const mockRefresh = jest.fn();
    render(<AdminDashboard onRefresh={mockRefresh} />);
    fireEvent.click(screen.getByText('Refresh'));
    expect(mockRefresh).toHaveBeenCalled();
  });
});

// API testing
describe('Organization API', () => {
  test('creates organization successfully', async () => {
    const orgData = {
      name: 'Test Org',
      type: 'private',
      email: '<EMAIL>'
    };

    const response = await request(app)
      .post('/api/organizations')
      .send(orgData)
      .expect(201);

    expect(response.body.success).toBe(true);
  });
});
```

### Integration Tests
```javascript
// End-to-end testing with Cypress
describe('Admin Portal E2E', () => {
  beforeEach(() => {
    cy.login('admin', 'password');
  });

  it('should create new organization', () => {
    cy.visit('/organizationform');
    cy.get('[data-testid="org-name"]').type('Test Organization');
    cy.get('[data-testid="org-email"]').type('<EMAIL>');
    cy.get('[data-testid="submit-btn"]').click();
    cy.contains('Organization created successfully');
  });
});
```

---

## 📈 Performance Monitoring

### Metrics Tracking
```javascript
// Performance metrics
const performanceMetrics = {
  pageLoadTime: 'Time to first contentful paint',
  apiResponseTime: 'Average API response time',
  databaseQueryTime: 'Database query execution time',
  memoryUsage: 'Server memory consumption',
  cpuUsage: 'Server CPU utilization',
  activeUsers: 'Concurrent active users',
  errorRate: 'Application error percentage'
};

// Monitoring implementation
const trackPerformance = (metric, value) => {
  console.log(`Performance: ${metric} = ${value}ms`);
  // Send to monitoring service (e.g., New Relic, DataDog)
};
```

### Error Tracking
```javascript
// Error boundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    // Send error to tracking service
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }

    return this.props.children;
  }
}
```

---

This completes the comprehensive documentation for the SHAKTI Drone Management Admin Portal. The documentation covers every aspect from UI components to backend APIs, database schemas, security implementations, and performance considerations.
