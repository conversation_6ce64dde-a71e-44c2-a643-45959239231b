# Enhanced Admin Map Documentation

## Overview
The Enhanced Admin Map is a comprehensive mapping solution that allows administrators to view and manage both organizations and individuals along with their associated drone fleets in a unified interface.

## Features

### 1. Unified Entity Display
- **Organizations**: Display all registered organizations with their locations, status, and drone counts
- **Individuals**: Display all registered individuals with their locations, status, and allocated drones
- **Toggle Views**: Switch between Organizations only, Individuals only, or Both simultaneously

### 2. Interactive Map Interface
- **Dual Markers**: Different visual markers for organizations (building icons) and individuals (person icons)
- **Status Indicators**: Color-coded markers based on entity status (active/approved, pending, inactive/rejected)
- **Drone Count Badges**: Small badges showing active drone percentage for each entity

### 3. Entity Selection and Drone Details
- **Click to Select**: Click on any organization or individual to view their detailed drone information
- **Drone Details Panel**: Full-screen modal showing:
  - Entity information (contact details, location, status)
  - Complete drone fleet list with real-time status
  - Interactive map showing entity location and all associated drones
  - Drone statistics (active, inactive, maintenance, crashed)

### 4. Advanced Filtering and Search
- **Search Functionality**: Search across organization names, individual names, IDs, locations, and email addresses
- **Status Filtering**: Filter by entity status (all, active/approved, pending, inactive/rejected)
- **Real-time Updates**: Live data refresh with connection status indicator

### 5. Comprehensive Statistics
- **Organization Stats**: Total organizations, active organizations
- **Individual Stats**: Total individuals, approved individuals  
- **Drone Stats**: Total drones across all entities, active drones
- **Real-time Metrics**: Live updating statistics dashboard

## Components

### 1. EnhancedMap.jsx
Main container component that orchestrates the entire enhanced map experience.

**Key Features:**
- Statistics dashboard with 6 key metrics
- Search and filter controls
- View type toggle (Organizations/Both/Individuals)
- Integration with drone details panel

### 2. EnhancedMapView.jsx
Core map visualization component with entity display and selection.

**Key Features:**
- Tabbed interface for entity types
- Interactive map with custom markers
- Entity list sidebar with detailed information
- Click handlers for entity selection

### 3. DroneDetailsPanel.jsx
Full-screen modal for displaying detailed drone information.

**Key Features:**
- Entity information header
- Drone fleet statistics grid
- Detailed drone list with status indicators
- Interactive map showing entity and drone locations
- Real-time drone position updates

### 4. MapDataService.js (Enhanced)
Extended data service with support for individuals and their drones.

**New Methods:**
- `fetchIndividuals(filters)` - Fetch individual data with filtering
- `fetchIndividualDrones(individualId)` - Get drones for specific individual
- `searchIndividuals(query)` - Search functionality for individuals
- `getIndividualStats()` - Individual-specific statistics
- `getCombinedStats()` - Combined organization and individual statistics

## Data Structure

### Organization Data
```javascript
{
  id: 'ORG-001',
  name: "Organization Name",
  orgId: "ORG ID:-0002501",
  state: "State",
  district: "District", 
  totalDrones: 10,
  activeDrones: 7,
  position: [lat, lng],
  status: 'active', // active, pending, inactive
  registrationDate: '2023-01-15',
  contactPerson: 'Contact Name',
  phone: '+91-9876543210',
  email: '<EMAIL>'
}
```

### Individual Data
```javascript
{
  id: 'IND-001',
  fullName: "Individual Name",
  individualId: "IND-0001",
  gender: "male", // male, female, other
  age: 34,
  state: "State",
  district: "District",
  city: "City",
  totalDrones: 2,
  activeDrones: 1,
  position: [lat, lng],
  status: 'approved', // approved, pending, rejected, suspended
  registrationDate: '2023-06-15',
  phone: '+91-9876543220',
  email: '<EMAIL>',
  allocatedDrones: 2
}
```

### Drone Data
```javascript
{
  id: "DRONE001",
  status: "ACTIVE", // ACTIVE, FLYING, INACTIVE, MAINTENANCE, CRASHED
  lat: 19.851,
  lng: 75.895,
  battery: 85, // percentage
  altitude: 120, // meters
  lastUpdate: '2 min ago'
}
```

## Usage

### Accessing the Enhanced Map
1. Navigate to the Admin Portal
2. Click on "Enhanced Map" in the sidebar
3. The enhanced map will load with both organizations and individuals displayed

### Viewing Entity Details
1. Click on any organization or individual marker on the map
2. Or click on any entity in the sidebar list
3. The drone details panel will open showing:
   - Entity information
   - Complete drone fleet
   - Interactive map with drone locations

### Filtering and Search
1. Use the search bar to find specific entities
2. Use the status filter dropdown to filter by entity status
3. Use the view type toggle to switch between entity types

### Real-time Updates
- The map automatically refreshes data every few seconds
- Connection status is displayed in the bottom-right corner
- Drone positions update in real-time when viewing details

## Routes

- `/enhanced-map` - Main enhanced map interface
- `/map` - Original map (still available for backward compatibility)

## Technical Implementation

### State Management
- React hooks for local state management
- MapDataService for centralized data management
- Event subscription system for real-time updates

### Map Integration
- React Leaflet for map rendering
- Custom markers with dynamic styling
- Popup components for quick information display

### Responsive Design
- Mobile-friendly interface
- Adaptive layouts for different screen sizes
- Touch-friendly controls

## Future Enhancements

1. **Real-time Drone Tracking**: Live GPS tracking of active drones
2. **Geofencing**: Visual boundaries and alerts for restricted areas
3. **Flight Path Visualization**: Historical and planned flight routes
4. **Weather Integration**: Weather overlay and alerts
5. **Bulk Operations**: Multi-select for bulk drone operations
6. **Export Functionality**: Export maps and data to various formats
7. **Advanced Analytics**: Detailed performance metrics and reports

## Browser Compatibility

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance Considerations

- Efficient marker clustering for large datasets
- Lazy loading of drone details
- Optimized re-rendering with React.memo
- Debounced search functionality
- Connection pooling for API requests
