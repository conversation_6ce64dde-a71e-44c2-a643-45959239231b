import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  ArrowLeft,
  ClipboardCheck,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Battery,
  Wifi,
  Camera,
  Settings,
  Edit,
  Download,
  Printer
} from 'lucide-react';

const PreFlightInspectionDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [inspection, setInspection] = useState(null);
  const [loading, setLoading] = useState(true);

  // Sample inspection data - in real app, this would come from API
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const sampleInspection = {
        id: id || 'PFI-2024-001',
        droneId: 'DRN-001',
        droneName: 'Surveyor Alpha',
        inspector: '<PERSON>',
        date: '2024-01-15',
        time: '08:30',
        location: 'Hangar A',
        status: 'Passed',
        duration: '25 minutes',
        checklist: {
          battery: { status: 'Pass', voltage: '25.2V', percentage: '98%', temperature: '22°C', cycles: '145' },
          propellers: { status: 'Pass', condition: 'Excellent', balance: 'Good', wear: 'Minimal', damage: 'None' },
          camera: { status: 'Pass', focus: 'Sharp', gimbal: 'Stable', resolution: '4K', storage: '85%' },
          gps: { status: 'Pass', satellites: '12', accuracy: '1.2m', signal: 'Strong', drift: 'None' },
          sensors: { status: 'Pass', imu: 'Calibrated', compass: 'Normal', altimeter: 'Accurate', temperature: 'Normal' },
          communication: { status: 'Pass', signal: 'Strong', range: 'Full', latency: '12ms', interference: 'None' }
        },
        issues: [],
        notes: 'All systems operational. Ready for flight. Weather conditions optimal.',
        nextInspection: '2024-01-16',
        weather: {
          temperature: '18°C',
          humidity: '65%',
          windSpeed: '8 km/h',
          visibility: 'Excellent'
        },
        flightPlan: {
          mission: 'Aerial Survey - Construction Site',
          duration: '45 minutes',
          altitude: '120m',
          area: '2.5 km²'
        }
      };
      setInspection(sampleInspection);
      setLoading(false);
    }, 500);
  }, [id]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pass': return 'bg-green-100 text-green-700 border-green-200';
      case 'Fail': return 'bg-red-100 text-red-700 border-red-200';
      case 'Warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Pass': return <CheckCircle className="w-4 h-4" />;
      case 'Fail': return <XCircle className="w-4 h-4" />;
      case 'Warning': return <AlertTriangle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const headerActions = (
    <div className="flex items-center gap-3">
      <button
        onClick={() => navigate('/qc-inspections/pre-flight')}
        className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
      >
        <ArrowLeft className="w-4 h-4" />
        Back to List
      </button>
      <button className="px-4 py-2 text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors flex items-center gap-2">
        <Edit className="w-4 h-4" />
        Edit
      </button>
      <button className="px-4 py-2 text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors flex items-center gap-2">
        <Download className="w-4 h-4" />
        Export
      </button>
      <button className="px-4 py-2 text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors flex items-center gap-2">
        <Printer className="w-4 h-4" />
        Print
      </button>
    </div>
  );

  if (loading) {
    return (
      <QCLayout title="Loading..." subtitle="Please wait">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </QCLayout>
    );
  }

  if (!inspection) {
    return (
      <QCLayout title="Inspection Not Found" subtitle="The requested inspection could not be found">
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Inspection not found</p>
          <button
            onClick={() => navigate('/qc-inspections/pre-flight')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Inspections
          </button>
        </div>
      </QCLayout>
    );
  }

  return (
    <QCLayout
      title={`Pre-Flight Inspection - ${inspection.id}`}
      subtitle={`${inspection.droneName} (${inspection.droneId})`}
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Inspection Overview */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 rounded-xl flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <ClipboardCheck className="w-8 h-8 text-blue-500" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 mb-2">{inspection.id}</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Inspector:</span>
                    <span className="font-medium">{inspection.inspector}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Date:</span>
                    <span className="font-medium">{inspection.date} at {inspection.time}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Location:</span>
                    <span className="font-medium">{inspection.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Duration:</span>
                    <span className="font-medium">{inspection.duration}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-right">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(inspection.status)}`}>
                {getStatusIcon(inspection.status)}
                <span className="ml-2">{inspection.status}</span>
              </span>
              <p className="text-sm text-gray-500 mt-2">Next: {inspection.nextInspection}</p>
            </div>
          </div>
        </div>

        {/* Detailed Checklist */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Battery System */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Battery className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Battery System</h3>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(inspection.checklist.battery.status)}`}>
                  {getStatusIcon(inspection.checklist.battery.status)}
                  <span className="ml-1">{inspection.checklist.battery.status}</span>
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Voltage:</span>
                <span className="font-medium ml-2">{inspection.checklist.battery.voltage}</span>
              </div>
              <div>
                <span className="text-gray-600">Percentage:</span>
                <span className="font-medium ml-2">{inspection.checklist.battery.percentage}</span>
              </div>
              <div>
                <span className="text-gray-600">Temperature:</span>
                <span className="font-medium ml-2">{inspection.checklist.battery.temperature}</span>
              </div>
              <div>
                <span className="text-gray-600">Cycles:</span>
                <span className="font-medium ml-2">{inspection.checklist.battery.cycles}</span>
              </div>
            </div>
          </div>

          {/* Propellers */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Settings className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Propellers</h3>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(inspection.checklist.propellers.status)}`}>
                  {getStatusIcon(inspection.checklist.propellers.status)}
                  <span className="ml-1">{inspection.checklist.propellers.status}</span>
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Condition:</span>
                <span className="font-medium ml-2">{inspection.checklist.propellers.condition}</span>
              </div>
              <div>
                <span className="text-gray-600">Balance:</span>
                <span className="font-medium ml-2">{inspection.checklist.propellers.balance}</span>
              </div>
              <div>
                <span className="text-gray-600">Wear:</span>
                <span className="font-medium ml-2">{inspection.checklist.propellers.wear}</span>
              </div>
              <div>
                <span className="text-gray-600">Damage:</span>
                <span className="font-medium ml-2">{inspection.checklist.propellers.damage}</span>
              </div>
            </div>
          </div>

          {/* Camera System */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Camera className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Camera System</h3>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(inspection.checklist.camera.status)}`}>
                  {getStatusIcon(inspection.checklist.camera.status)}
                  <span className="ml-1">{inspection.checklist.camera.status}</span>
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Focus:</span>
                <span className="font-medium ml-2">{inspection.checklist.camera.focus}</span>
              </div>
              <div>
                <span className="text-gray-600">Gimbal:</span>
                <span className="font-medium ml-2">{inspection.checklist.camera.gimbal}</span>
              </div>
              <div>
                <span className="text-gray-600">Resolution:</span>
                <span className="font-medium ml-2">{inspection.checklist.camera.resolution}</span>
              </div>
              <div>
                <span className="text-gray-600">Storage:</span>
                <span className="font-medium ml-2">{inspection.checklist.camera.storage}</span>
              </div>
            </div>
          </div>

          {/* GPS System */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Wifi className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">GPS System</h3>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(inspection.checklist.gps.status)}`}>
                  {getStatusIcon(inspection.checklist.gps.status)}
                  <span className="ml-1">{inspection.checklist.gps.status}</span>
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Satellites:</span>
                <span className="font-medium ml-2">{inspection.checklist.gps.satellites}</span>
              </div>
              <div>
                <span className="text-gray-600">Accuracy:</span>
                <span className="font-medium ml-2">{inspection.checklist.gps.accuracy}</span>
              </div>
              <div>
                <span className="text-gray-600">Signal:</span>
                <span className="font-medium ml-2">{inspection.checklist.gps.signal}</span>
              </div>
              <div>
                <span className="text-gray-600">Drift:</span>
                <span className="font-medium ml-2">{inspection.checklist.gps.drift}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Weather Conditions */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Weather Conditions</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Temperature:</span>
                <span className="font-medium ml-2">{inspection.weather.temperature}</span>
              </div>
              <div>
                <span className="text-gray-600">Humidity:</span>
                <span className="font-medium ml-2">{inspection.weather.humidity}</span>
              </div>
              <div>
                <span className="text-gray-600">Wind Speed:</span>
                <span className="font-medium ml-2">{inspection.weather.windSpeed}</span>
              </div>
              <div>
                <span className="text-gray-600">Visibility:</span>
                <span className="font-medium ml-2">{inspection.weather.visibility}</span>
              </div>
            </div>
          </div>

          {/* Flight Plan */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Planned Flight</h3>
            <div className="space-y-3 text-sm">
              <div>
                <span className="text-gray-600">Mission:</span>
                <span className="font-medium ml-2">{inspection.flightPlan.mission}</span>
              </div>
              <div>
                <span className="text-gray-600">Duration:</span>
                <span className="font-medium ml-2">{inspection.flightPlan.duration}</span>
              </div>
              <div>
                <span className="text-gray-600">Altitude:</span>
                <span className="font-medium ml-2">{inspection.flightPlan.altitude}</span>
              </div>
              <div>
                <span className="text-gray-600">Area:</span>
                <span className="font-medium ml-2">{inspection.flightPlan.area}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Notes */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Inspector Notes</h3>
          <p className="text-gray-700 leading-relaxed">{inspection.notes}</p>
        </div>
      </div>
    </QCLayout>
  );
};

export default PreFlightInspectionDetail;
