const Notification = require('../models/Notification');
const { sendSuccess, sendError } = require('../utils/response');

// Get all notifications with filtering and pagination
const getAllNotifications = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      priority,
      isRead,
      isArchived,
      type,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (category && category !== 'all') {
      filter.category = category;
    }
    
    if (priority && priority !== 'all') {
      filter.priority = priority;
    }
    
    if (isRead !== undefined) {
      filter.isRead = isRead === 'true';
    }
    
    if (isArchived !== undefined) {
      filter.isArchived = isArchived === 'true';
    } else {
      // By default, don't show archived notifications
      filter.isArchived = false;
    }
    
    if (type && type !== 'all') {
      filter.type = type;
    }
    
    // Search functionality
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } },
        { 'relatedEntity.entityName': { $regex: search, $options: 'i' } },
        { 'triggeredBy.username': { $regex: search, $options: 'i' } }
      ];
    }

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Sort configuration
    const sortConfig = {};
    sortConfig[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query
    const [notifications, totalCount] = await Promise.all([
      Notification.find(filter)
        .sort(sortConfig)
        .skip(skip)
        .limit(limitNum)
        .populate('triggeredBy.userId', 'username email role')
        .lean(),
      Notification.countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    const pagination = {
      currentPage: pageNum,
      totalPages,
      totalCount,
      hasNextPage,
      hasPrevPage,
      limit: limitNum
    };

    console.log(`✅ Retrieved ${notifications.length} notifications (page ${pageNum}/${totalPages})`);

    return sendSuccess(res, {
      notifications,
      pagination
    }, 'Notifications retrieved successfully');

  } catch (error) {
    console.error('Error in getAllNotifications:', error);
    return sendError(res, 'Failed to retrieve notifications', 500);
  }
};

// Get notification statistics
const getNotificationStats = async (req, res) => {
  try {
    const [
      totalCount,
      unreadCount,
      priorityStats,
      categoryStats,
      typeStats,
      recentCount
    ] = await Promise.all([
      Notification.countDocuments({ isArchived: false }),
      Notification.countDocuments({ isRead: false, isArchived: false }),
      Notification.aggregate([
        { $match: { isArchived: false } },
        { $group: { _id: '$priority', count: { $sum: 1 } } }
      ]),
      Notification.aggregate([
        { $match: { isArchived: false } },
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ]),
      Notification.aggregate([
        { $match: { isArchived: false } },
        { $group: { _id: '$type', count: { $sum: 1 } } }
      ]),
      Notification.countDocuments({
        isArchived: false,
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      })
    ]);

    // Format stats
    const priority = {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    };
    priorityStats.forEach(stat => {
      priority[stat._id] = stat.count;
    });

    const byCategory = {};
    categoryStats.forEach(stat => {
      byCategory[stat._id] = stat.count;
    });

    const byType = {};
    typeStats.forEach(stat => {
      byType[stat._id] = stat.count;
    });

    const stats = {
      total: totalCount,
      unread: unreadCount,
      read: totalCount - unreadCount,
      recent24h: recentCount,
      priority,
      byCategory,
      byType,
      lastUpdated: new Date()
    };

    console.log('✅ Notification statistics retrieved successfully');
    return sendSuccess(res, stats, 'Notification statistics retrieved successfully');

  } catch (error) {
    console.error('Error in getNotificationStats:', error);
    return sendError(res, 'Failed to retrieve notification statistics', 500);
  }
};

// Mark notification as read
const markAsRead = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findById(id);
    if (!notification) {
      return sendError(res, 'Notification not found', 404);
    }

    await notification.markAsRead();

    console.log(`✅ Notification ${id} marked as read`);
    return sendSuccess(res, { notification }, 'Notification marked as read');

  } catch (error) {
    console.error('Error in markAsRead:', error);
    return sendError(res, 'Failed to mark notification as read', 500);
  }
};

// Mark all notifications as read
const markAllAsRead = async (req, res) => {
  try {
    const result = await Notification.markAllAsRead();

    console.log(`✅ Marked ${result.modifiedCount} notifications as read`);
    return sendSuccess(res, { 
      modifiedCount: result.modifiedCount 
    }, 'All notifications marked as read');

  } catch (error) {
    console.error('Error in markAllAsRead:', error);
    return sendError(res, 'Failed to mark all notifications as read', 500);
  }
};

// Archive notification
const archiveNotification = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findById(id);
    if (!notification) {
      return sendError(res, 'Notification not found', 404);
    }

    await notification.archive();

    console.log(`✅ Notification ${id} archived`);
    return sendSuccess(res, { notification }, 'Notification archived');

  } catch (error) {
    console.error('Error in archiveNotification:', error);
    return sendError(res, 'Failed to archive notification', 500);
  }
};

// Delete notification
const deleteNotification = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findByIdAndDelete(id);
    if (!notification) {
      return sendError(res, 'Notification not found', 404);
    }

    console.log(`✅ Notification ${id} deleted`);
    return sendSuccess(res, null, 'Notification deleted successfully');

  } catch (error) {
    console.error('Error in deleteNotification:', error);
    return sendError(res, 'Failed to delete notification', 500);
  }
};

// Bulk operations
const bulkMarkAsRead = async (req, res) => {
  try {
    const { notificationIds } = req.body;

    if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
      return sendError(res, 'Invalid notification IDs provided', 400);
    }

    const result = await Notification.updateMany(
      { _id: { $in: notificationIds }, isRead: false },
      { 
        isRead: true, 
        readAt: new Date() 
      }
    );

    console.log(`✅ Bulk marked ${result.modifiedCount} notifications as read`);
    return sendSuccess(res, { 
      modifiedCount: result.modifiedCount 
    }, 'Notifications marked as read');

  } catch (error) {
    console.error('Error in bulkMarkAsRead:', error);
    return sendError(res, 'Failed to mark notifications as read', 500);
  }
};

const bulkArchive = async (req, res) => {
  try {
    const { notificationIds } = req.body;

    if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
      return sendError(res, 'Invalid notification IDs provided', 400);
    }

    const result = await Notification.updateMany(
      { _id: { $in: notificationIds }, isArchived: false },
      { 
        isArchived: true, 
        archivedAt: new Date() 
      }
    );

    console.log(`✅ Bulk archived ${result.modifiedCount} notifications`);
    return sendSuccess(res, { 
      modifiedCount: result.modifiedCount 
    }, 'Notifications archived');

  } catch (error) {
    console.error('Error in bulkArchive:', error);
    return sendError(res, 'Failed to archive notifications', 500);
  }
};

const bulkDelete = async (req, res) => {
  try {
    const { notificationIds } = req.body;

    if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
      return sendError(res, 'Invalid notification IDs provided', 400);
    }

    const result = await Notification.deleteMany({
      _id: { $in: notificationIds }
    });

    console.log(`✅ Bulk deleted ${result.deletedCount} notifications`);
    return sendSuccess(res, { 
      deletedCount: result.deletedCount 
    }, 'Notifications deleted');

  } catch (error) {
    console.error('Error in bulkDelete:', error);
    return sendError(res, 'Failed to delete notifications', 500);
  }
};

module.exports = {
  getAllNotifications,
  getNotificationStats,
  markAsRead,
  markAllAsRead,
  archiveNotification,
  deleteNotification,
  bulkMarkAsRead,
  bulkArchive,
  bulkDelete
};
