// NotificationService.js - Professional notification data management service
class NotificationService {
  constructor() {
    this.notifications = [];
    this.subscribers = new Map();
    this.isInitialized = false;
    this.notificationTypes = [
      {
        id: 'drone_crash',
        type: 'Drone Crash',
        icon: 'AlertTriangle',
        color: 'red',
        priority: 'critical',
        category: 'safety'
      },
      {
        id: 'battery_low',
        type: 'Battery Low',
        icon: 'Battery',
        color: 'yellow',
        priority: 'high',
        category: 'maintenance'
      },
      {
        id: 'mission_completed',
        type: 'Mission Completed',
        icon: 'CheckCircle2',
        color: 'green',
        priority: 'low',
        category: 'operations'
      },
      {
        id: 'drone_connected',
        type: 'Drone Connected',
        icon: 'Wifi',
        color: 'blue',
        priority: 'medium',
        category: 'connectivity'
      },
      {
        id: 'maintenance_due',
        type: 'Maintenance Due',
        icon: 'Wrench',
        color: 'orange',
        priority: 'medium',
        category: 'maintenance'
      },
      {
        id: 'weather_alert',
        type: 'Weather Alert',
        icon: 'Cloud',
        color: 'purple',
        priority: 'high',
        category: 'weather'
      },
      {
        id: 'geofence_breach',
        type: 'Geofence Breach',
        icon: 'MapPin',
        color: 'red',
        priority: 'critical',
        category: 'safety'
      },
      {
        id: 'system_update',
        type: 'System Update',
        icon: 'Download',
        color: 'blue',
        priority: 'low',
        category: 'system'
      }
    ];
  }

  // Initialize service with mock data
  initialize() {
    if (this.isInitialized) return;
    
    this.generateMockNotifications();
    this.startRealTimeUpdates();
    this.isInitialized = true;
  }

  // Generate comprehensive mock notifications
  generateMockNotifications() {
    const mockNotifications = [];
    const droneNames = ['Arjuna MK-I', 'Bhima Pro', 'Nakula X1', 'Sahadeva Elite', 'Yudhishthira Max'];
    const organizations = ['Sky Drone Solutions', 'EcoDrone Solutions', 'SkyFarm Ltd.', 'AgriTech India', 'FarmVue Aerial'];
    const locations = ['Maharashtra', 'Karnataka', 'Gujarat', 'Tamil Nadu', 'Punjab'];

    // Generate notifications for the last 7 days
    for (let i = 0; i < 50; i++) {
      const typeIndex = Math.floor(Math.random() * this.notificationTypes.length);
      const notificationType = this.notificationTypes[typeIndex];
      const daysAgo = Math.floor(Math.random() * 7);
      const hoursAgo = Math.floor(Math.random() * 24);
      const minutesAgo = Math.floor(Math.random() * 60);
      
      const timestamp = new Date();
      timestamp.setDate(timestamp.getDate() - daysAgo);
      timestamp.setHours(timestamp.getHours() - hoursAgo);
      timestamp.setMinutes(timestamp.getMinutes() - minutesAgo);

      const notification = {
        id: `notif_${Date.now()}_${i}`,
        type: notificationType.type,
        typeId: notificationType.id,
        icon: notificationType.icon,
        color: notificationType.color,
        priority: notificationType.priority,
        category: notificationType.category,
        title: this.generateNotificationTitle(notificationType, droneNames, organizations),
        message: this.generateNotificationMessage(notificationType, droneNames, locations),
        timestamp: timestamp,
        isRead: Math.random() > 0.4, // 60% read, 40% unread
        isArchived: Math.random() > 0.9, // 10% archived
        droneId: `PRYMAA${Math.floor(Math.random() * 999999)}`,
        droneName: droneNames[Math.floor(Math.random() * droneNames.length)],
        organization: organizations[Math.floor(Math.random() * organizations.length)],
        location: locations[Math.floor(Math.random() * locations.length)],
        metadata: this.generateMetadata(notificationType)
      };

      mockNotifications.push(notification);
    }

    this.notifications = mockNotifications.sort((a, b) => b.timestamp - a.timestamp);
    this.notifySubscribers('notifications', this.notifications);
  }

  // Generate notification titles
  generateNotificationTitle(type, droneNames, organizations) {
    const drone = droneNames[Math.floor(Math.random() * droneNames.length)];
    const org = organizations[Math.floor(Math.random() * organizations.length)];

    const titles = {
      'drone_crash': `Emergency: ${drone} Crash Detected`,
      'battery_low': `Low Battery Alert: ${drone}`,
      'mission_completed': `Mission Success: ${drone}`,
      'drone_connected': `Connection Established: ${drone}`,
      'maintenance_due': `Maintenance Required: ${drone}`,
      'weather_alert': `Weather Warning for ${drone}`,
      'geofence_breach': `Boundary Violation: ${drone}`,
      'system_update': `System Update Available for ${drone}`
    };

    return titles[type.id] || `${type.type}: ${drone}`;
  }

  // Generate notification messages
  generateNotificationMessage(type, droneNames, locations) {
    const location = locations[Math.floor(Math.random() * locations.length)];
    
    const messages = {
      'drone_crash': `Drone has crashed in ${location}. Emergency response initiated. Last known coordinates: ${(Math.random() * 90).toFixed(4)}°N, ${(Math.random() * 180).toFixed(4)}°E`,
      'battery_low': `Battery level at ${Math.floor(Math.random() * 20 + 5)}%. Return to base recommended. Current location: ${location}`,
      'mission_completed': `Agricultural spraying mission completed successfully in ${location}. ${Math.floor(Math.random() * 50 + 10)} hectares covered.`,
      'drone_connected': `Drone successfully connected to control station. Signal strength: ${Math.floor(Math.random() * 30 + 70)}%. Location: ${location}`,
      'maintenance_due': `Scheduled maintenance overdue by ${Math.floor(Math.random() * 30 + 1)} days. Flight hours: ${Math.floor(Math.random() * 500 + 100)}`,
      'weather_alert': `Severe weather conditions detected in ${location}. Wind speed: ${Math.floor(Math.random() * 20 + 25)} km/h. Consider grounding.`,
      'geofence_breach': `Drone has exceeded authorized flight zone in ${location}. Automatic return-to-home activated.`,
      'system_update': `Firmware update v${Math.floor(Math.random() * 5 + 1)}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)} available. Includes performance improvements.`
    };

    return messages[type.id] || `${type.type} notification for drone in ${location}`;
  }

  // Generate metadata for notifications
  generateMetadata(type) {
    const baseMetadata = {
      source: 'SHAKTI_SYSTEM',
      severity: type.priority,
      autoGenerated: true
    };

    const specificMetadata = {
      'drone_crash': {
        impactRadius: `${Math.floor(Math.random() * 100 + 50)}m`,
        emergencyServices: 'Contacted',
        insuranceClaim: 'Pending'
      },
      'battery_low': {
        batteryLevel: `${Math.floor(Math.random() * 20 + 5)}%`,
        estimatedFlightTime: `${Math.floor(Math.random() * 10 + 2)} minutes`,
        chargingRequired: true
      },
      'mission_completed': {
        areaCovered: `${Math.floor(Math.random() * 50 + 10)} hectares`,
        flightDuration: `${Math.floor(Math.random() * 120 + 30)} minutes`,
        efficiency: `${Math.floor(Math.random() * 20 + 80)}%`
      }
    };

    return {
      ...baseMetadata,
      ...(specificMetadata[type.id] || {})
    };
  }

  // Start real-time notification updates
  startRealTimeUpdates() {
    setInterval(() => {
      if (Math.random() > 0.7) { // 30% chance every 10 seconds
        this.generateNewNotification();
      }
    }, 10000);
  }

  // Generate a new real-time notification
  generateNewNotification() {
    const typeIndex = Math.floor(Math.random() * this.notificationTypes.length);
    const notificationType = this.notificationTypes[typeIndex];
    const droneNames = ['Arjuna MK-I', 'Bhima Pro', 'Nakula X1', 'Sahadeva Elite', 'Yudhishthira Max'];
    const organizations = ['Sky Drone Solutions', 'EcoDrone Solutions', 'SkyFarm Ltd.', 'AgriTech India', 'FarmVue Aerial'];
    const locations = ['Maharashtra', 'Karnataka', 'Gujarat', 'Tamil Nadu', 'Punjab'];

    const newNotification = {
      id: `notif_${Date.now()}_${Math.random()}`,
      type: notificationType.type,
      typeId: notificationType.id,
      icon: notificationType.icon,
      color: notificationType.color,
      priority: notificationType.priority,
      category: notificationType.category,
      title: this.generateNotificationTitle(notificationType, droneNames, organizations),
      message: this.generateNotificationMessage(notificationType, droneNames, locations),
      timestamp: new Date(),
      isRead: false,
      isArchived: false,
      droneId: `PRYMAA${Math.floor(Math.random() * 999999)}`,
      droneName: droneNames[Math.floor(Math.random() * droneNames.length)],
      organization: organizations[Math.floor(Math.random() * organizations.length)],
      location: locations[Math.floor(Math.random() * locations.length)],
      metadata: this.generateMetadata(notificationType)
    };

    this.notifications.unshift(newNotification);
    this.notifications = this.notifications.slice(0, 100); // Keep only latest 100
    this.notifySubscribers('notifications', this.notifications);
    this.notifySubscribers('newNotification', newNotification);
  }

  // Subscribe to service updates
  subscribe(event, callback) {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, []);
    }
    this.subscribers.get(event).push(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.subscribers.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  // Notify subscribers
  notifySubscribers(event, data) {
    const callbacks = this.subscribers.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  // Get all notifications
  getNotifications() {
    return this.notifications;
  }

  // Get notification statistics
  getStatistics() {
    const total = this.notifications.length;
    const unread = this.notifications.filter(n => !n.isRead).length;
    const critical = this.notifications.filter(n => n.priority === 'critical').length;
    const high = this.notifications.filter(n => n.priority === 'high').length;
    const medium = this.notifications.filter(n => n.priority === 'medium').length;
    const low = this.notifications.filter(n => n.priority === 'low').length;

    const byCategory = this.notifications.reduce((acc, n) => {
      acc[n.category] = (acc[n.category] || 0) + 1;
      return acc;
    }, {});

    const byType = this.notifications.reduce((acc, n) => {
      acc[n.typeId] = (acc[n.typeId] || 0) + 1;
      return acc;
    }, {});

    return {
      total,
      unread,
      read: total - unread,
      priority: { critical, high, medium, low },
      byCategory,
      byType,
      lastUpdated: new Date()
    };
  }

  // Mark notification as read
  markAsRead(notificationId) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = true;
      this.notifySubscribers('notifications', this.notifications);
    }
  }

  // Mark all as read
  markAllAsRead() {
    this.notifications.forEach(n => n.isRead = true);
    this.notifySubscribers('notifications', this.notifications);
  }

  // Archive notification
  archiveNotification(notificationId) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isArchived = true;
      this.notifySubscribers('notifications', this.notifications);
    }
  }

  // Delete notification
  deleteNotification(notificationId) {
    this.notifications = this.notifications.filter(n => n.id !== notificationId);
    this.notifySubscribers('notifications', this.notifications);
  }

  // Bulk operations
  bulkMarkAsRead(notificationIds) {
    notificationIds.forEach(id => this.markAsRead(id));
  }

  bulkArchive(notificationIds) {
    notificationIds.forEach(id => this.archiveNotification(id));
  }

  bulkDelete(notificationIds) {
    this.notifications = this.notifications.filter(n => !notificationIds.includes(n.id));
    this.notifySubscribers('notifications', this.notifications);
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
