import { useState, useEffect } from "react";
import {
  LayoutDashboard,
  MapPinned,
  Bot,
  Bell,
  LogOut,
  Menu,
  X,
  ChevronRight
} from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../../../context/AuthContext";
import logo from "../../../assets/logo.png";

// ✅ Enhanced Navigation Item with better error handling
const NavItem = ({ icon, label, to, isActive, isMobile, onClick }) => {
  const navigate = useNavigate();

  const handleClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      // Ensure navigation path is valid
      if (to && typeof to === 'string') {
        console.log(`OrgSidebar: Navigating to: ${to}`); // Debug log
        console.log(`OrgSidebar: Current location: ${window.location.pathname}`); // Debug log
        navigate(to, { replace: false });

        // Close mobile menu if applicable
        if (isMobile && onClick) {
          onClick();
        }
      } else {
        console.error(`OrgSidebar: Invalid navigation path: ${to}`);
      }
    } catch (error) {
      console.error('OrgSidebar: Navigation error:', error);
    }
  };

  return (
    <div
      className={`flex items-center gap-3 px-3 py-3 rounded-lg cursor-pointer transition-all duration-200 ${
        isActive
          ? 'bg-blue-50 text-blue-600 border-l-4 border-blue-600'
          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
      }`}
      onClick={handleClick}
    >
      <div className="flex-shrink-0">
        {icon}
      </div>
      <span className="font-medium text-sm">{label}</span>
      {isActive && (
        <ChevronRight className="w-4 h-4 ml-auto flex-shrink-0" />
      )}
    </div>
  );
};

const OrgSidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // ✅ Enhanced navigation items with proper paths
  const navigationItems = [
    {
      icon: <LayoutDashboard className="w-5 h-5" />,
      label: "Dashboard",
      to: "/orgdashboard"
    },
    {
      icon: <MapPinned className="w-5 h-5" />,
      label: "Map",
      to: "/orgmapsection"
    },
    {
      icon: <Bot className="w-5 h-5" />,
      label: "Drones",
      to: "/orgdronepage"
    },
    {
      icon: <Bell className="w-5 h-5" />,
      label: "Notifications",
      to: "/orgnotification"
    }
  ];

  // ✅ Enhanced logout with proper error handling
  const handleLogout = async () => {
    try {
      console.log('OrgSidebar: Logout initiated');

      // Close mobile menu if open
      setIsMobileMenuOpen(false);

      // Call logout from context
      if (logout && typeof logout === 'function') {
        await logout();
      }

      // Navigate to home
      navigate('/', { replace: true });
      console.log('OrgSidebar: Logout completed successfully');
    } catch (error) {
      console.error('OrgSidebar: Logout error:', error);
      // Still navigate to home even if logout fails
      navigate('/', { replace: true });
    }
  };

  // ✅ Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  // ✅ Close mobile menu on window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <>
      {/* ✅ Mobile Menu Button */}
      <button
        className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-white rounded-lg shadow-md border border-gray-200"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        aria-label="Toggle menu"
      >
        {isMobileMenuOpen ? (
          <X className="w-6 h-6 text-gray-600" />
        ) : (
          <Menu className="w-6 h-6 text-gray-600" />
        )}
      </button>

      {/* ✅ Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* ✅ Enhanced Sidebar */}
      <div
        className={`fixed top-0 left-0 h-full bg-white shadow-xl border-r border-gray-200 z-40 transition-transform duration-300 ease-in-out ${
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 w-72`}
      >
        <div className="flex flex-col h-full">
          {/* ✅ Enhanced Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">
                S.H.A.K.T.I
              </h2>
            </div>
            <p className="text-sm text-gray-500 mt-1 text-left">Organization Portal</p>
          </div>

          {/* ✅ Enhanced Navigation */}
          <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
            {navigationItems.map((item, index) => (
              <NavItem
                key={index}
                icon={item.icon}
                label={item.label}
                to={item.to}
                isActive={location.pathname === item.to}
                isMobile={isMobileMenuOpen}
                onClick={() => setIsMobileMenuOpen(false)}
              />
            ))}
          </nav>

          {/* ✅ Enhanced Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center gap-3 mb-4">
              <img
                src={logo}
                alt="Organization Logo"
                className="w-10 h-10 rounded-lg object-contain"
                onError={(e) => {
                  console.error('OrgSidebar: Logo failed to load');
                  e.target.style.display = 'none';
                }}
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  Organization
                </p>
              </div>
            </div>

            {/* ✅ Enhanced Logout Button */}
            <button
              onClick={handleLogout}
              className="w-full flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-red-600 bg-red-50 hover:bg-red-100 rounded-lg transition-colors duration-200 border border-red-200 hover:border-red-300"
            >
              <LogOut className="w-4 h-4" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default OrgSidebar;
