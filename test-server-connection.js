const axios = require('axios');

async function testServerConnection() {
  try {
    console.log('🔍 Testing server connection...');
    
    // Test basic server health
    const response = await axios.get('http://localhost:5000/api/health', {
      timeout: 5000
    });
    
    console.log('✅ Server is running!');
    console.log('Response:', response.data);
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server is not running on port 5000');
      console.log('Please start the server with: cd server && npm run dev');
    } else if (error.code === 'ENOTFOUND') {
      console.log('❌ Cannot resolve localhost');
    } else {
      console.log('❌ Error connecting to server:', error.message);
    }
  }
}

testServerConnection();
