import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  Shield,
  Plus,
  Search,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Eye,
  Trash2,
  <PERSON><PERSON>heck,
  Target
} from 'lucide-react';

const ComplianceReports = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [complianceReports, setComplianceReports] = useState([]);

  // Initialize with sample compliance report data
  React.useEffect(() => {
    if (complianceReports.length === 0) {
      setComplianceReports([
        {
          id: 'CR-2024-001',
          auditId: 'AUD-2024-0156',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          auditor: '<PERSON>',
          date: '2024-01-15',
          time: '09:30',
          duration: '4.5 hours',
          location: 'Compliance Office',
          complianceType: 'FAA Part 107',
          auditType: 'Annual Compliance Audit',
          status: 'Compliant',
          reportType: 'Regulatory Compliance',
          completedBy: 'John Smith',
          completedAt: '2024-01-15 14:00',
          complianceScore: 98,
          regulations: [
            { regulation: 'FAA Part 107.15', status: 'Compliant', score: 100 },
            { regulation: 'FAA Part 107.19', status: 'Compliant', score: 95 },
            { regulation: 'FAA Part 107.25', status: 'Compliant', score: 100 },
            { regulation: 'FAA Part 107.31', status: 'Compliant', score: 98 },
            { regulation: 'FAA Part 107.51', status: 'Minor Issue', score: 85 }
          ],
          findings: [
            'All major regulatory requirements met',
            'Documentation complete and up-to-date',
            'Minor improvement needed in flight log detail'
          ],
          recommendations: [
            'Enhance flight log documentation',
            'Continue current compliance procedures',
            'Schedule next audit in 12 months'
          ],
          certifications: [
            { name: 'Remote Pilot Certificate', status: 'Valid', expires: '2025-06-15' },
            { name: 'Aircraft Registration', status: 'Valid', expires: '2025-12-31' },
            { name: 'Insurance Coverage', status: 'Valid', expires: '2024-08-20' }
          ],
          violations: [],
          corrective_actions: [
            'Implement enhanced flight logging procedures'
          ],
          nextAudit: '2025-01-15',
          attachments: ['compliance_checklist.pdf', 'certificates.zip', 'audit_report.pdf']
        },
        {
          id: 'CR-2024-002',
          auditId: 'AUD-2024-0154',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          auditor: 'Sarah Johnson',
          date: '2024-01-14',
          time: '14:20',
          duration: '3.8 hours',
          location: 'Field Operations',
          complianceType: 'OSHA Safety',
          auditType: 'Safety Compliance Review',
          status: 'Non-Compliant',
          reportType: 'Safety Compliance',
          completedBy: 'Sarah Johnson',
          completedAt: '2024-01-14 18:05',
          complianceScore: 72,
          regulations: [
            { regulation: 'OSHA 1926.95', status: 'Compliant', score: 100 },
            { regulation: 'OSHA 1926.451', status: 'Non-Compliant', score: 45 },
            { regulation: 'OSHA 1910.132', status: 'Compliant', score: 90 },
            { regulation: 'OSHA 1910.269', status: 'Minor Issue', score: 75 }
          ],
          findings: [
            'Safety equipment not properly maintained',
            'Missing safety documentation for high-risk operations',
            'Operator training records incomplete'
          ],
          recommendations: [
            'Immediate safety equipment inspection and replacement',
            'Complete missing safety documentation',
            'Conduct comprehensive operator retraining',
            'Implement enhanced safety monitoring procedures'
          ],
          certifications: [
            { name: 'Safety Training Certificate', status: 'Expired', expires: '2023-12-15' },
            { name: 'First Aid Certification', status: 'Valid', expires: '2024-09-30' },
            { name: 'Equipment Inspection', status: 'Overdue', expires: '2024-01-01' }
          ],
          violations: [
            'Inadequate safety equipment maintenance',
            'Missing required safety documentation'
          ],
          corrective_actions: [
            'Replace expired safety equipment',
            'Update safety documentation',
            'Retrain all operators',
            'Implement monthly safety reviews'
          ],
          nextAudit: '2024-02-14',
          attachments: ['safety_violations.pdf', 'corrective_plan.pdf', 'training_records.csv']
        },
        {
          id: 'CR-2024-003',
          auditId: 'AUD-2024-0152',
          droneId: 'DRN-005',
          droneName: 'Mapper Gamma',
          auditor: 'Mike Wilson',
          date: '2024-01-13',
          time: '11:15',
          duration: '2.5 hours',
          location: 'Quality Assurance Lab',
          complianceType: 'ISO 9001',
          auditType: 'Quality Management Audit',
          status: 'Compliant',
          reportType: 'Quality Compliance',
          completedBy: 'Mike Wilson',
          completedAt: '2024-01-13 13:45',
          complianceScore: 94,
          regulations: [
            { regulation: 'ISO 9001:2015 Clause 4', status: 'Compliant', score: 95 },
            { regulation: 'ISO 9001:2015 Clause 5', status: 'Compliant', score: 98 },
            { regulation: 'ISO 9001:2015 Clause 6', status: 'Compliant', score: 92 },
            { regulation: 'ISO 9001:2015 Clause 7', status: 'Compliant', score: 90 },
            { regulation: 'ISO 9001:2015 Clause 8', status: 'Compliant', score: 96 }
          ],
          findings: [
            'Quality management system fully implemented',
            'Documentation meets ISO standards',
            'Continuous improvement processes active'
          ],
          recommendations: [
            'Maintain current quality standards',
            'Consider advanced quality metrics',
            'Schedule next audit in 6 months'
          ],
          certifications: [
            { name: 'ISO 9001:2015 Certificate', status: 'Valid', expires: '2025-03-20' },
            { name: 'Quality Manager Certification', status: 'Valid', expires: '2024-11-15' },
            { name: 'Calibration Certificate', status: 'Valid', expires: '2024-07-30' }
          ],
          violations: [],
          corrective_actions: [],
          nextAudit: '2024-07-13',
          attachments: ['iso_audit_report.pdf', 'quality_metrics.xlsx', 'certificates.pdf']
        },
        {
          id: 'CR-2024-004',
          auditId: 'AUD-2024-0148',
          droneId: 'DRN-002',
          droneName: 'Inspector Delta',
          auditor: 'Emily Chen',
          date: '2024-01-12',
          time: '08:45',
          duration: '5.2 hours',
          location: 'Environmental Office',
          complianceType: 'EPA Environmental',
          auditType: 'Environmental Impact Assessment',
          status: 'Under Review',
          reportType: 'Environmental Compliance',
          completedBy: '',
          completedAt: '',
          complianceScore: 0,
          regulations: [
            { regulation: 'EPA NEPA Requirements', status: 'Under Review', score: 0 },
            { regulation: 'Clean Air Act Compliance', status: 'Under Review', score: 0 },
            { regulation: 'Noise Pollution Standards', status: 'Under Review', score: 0 },
            { regulation: 'Wildlife Protection Act', status: 'Under Review', score: 0 }
          ],
          findings: [
            'Environmental impact assessment in progress',
            'Noise level measurements being conducted',
            'Wildlife impact study ongoing'
          ],
          recommendations: [
            'Complete environmental impact assessment',
            'Finalize noise level compliance testing',
            'Submit wildlife impact study results'
          ],
          certifications: [
            { name: 'Environmental Permit', status: 'Pending', expires: 'TBD' },
            { name: 'Noise Compliance Certificate', status: 'Pending', expires: 'TBD' },
            { name: 'Wildlife Impact Assessment', status: 'In Progress', expires: 'TBD' }
          ],
          violations: [],
          corrective_actions: [],
          nextAudit: '2024-02-12',
          attachments: ['environmental_study.pdf', 'noise_measurements.csv', 'wildlife_report.pdf']
        },
        {
          id: 'CR-2024-005',
          auditId: 'AUD-2024-0145',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          auditor: 'David Rodriguez',
          date: '2024-01-11',
          time: '15:30',
          duration: '1.8 hours',
          location: 'Data Security Office',
          complianceType: 'GDPR Data Protection',
          auditType: 'Data Privacy Compliance',
          status: 'Compliant',
          reportType: 'Data Protection Compliance',
          completedBy: 'David Rodriguez',
          completedAt: '2024-01-11 17:18',
          complianceScore: 96,
          regulations: [
            { regulation: 'GDPR Article 5', status: 'Compliant', score: 98 },
            { regulation: 'GDPR Article 6', status: 'Compliant', score: 95 },
            { regulation: 'GDPR Article 25', status: 'Compliant', score: 94 },
            { regulation: 'GDPR Article 32', status: 'Compliant', score: 97 }
          ],
          findings: [
            'Data protection measures fully implemented',
            'Privacy by design principles followed',
            'Data retention policies compliant'
          ],
          recommendations: [
            'Continue current data protection practices',
            'Regular privacy impact assessments',
            'Monitor regulatory updates'
          ],
          certifications: [
            { name: 'Data Protection Officer Certification', status: 'Valid', expires: '2024-12-31' },
            { name: 'Privacy Impact Assessment', status: 'Valid', expires: '2024-06-30' },
            { name: 'Security Audit Certificate', status: 'Valid', expires: '2024-10-15' }
          ],
          violations: [],
          corrective_actions: [],
          nextAudit: '2024-07-11',
          attachments: ['gdpr_compliance_report.pdf', 'privacy_policy.pdf', 'data_audit.csv']
        }
      ]);
    }
  }, [complianceReports.length]);

  // Form state for new compliance report
  const [newComplianceReport, setNewComplianceReport] = useState({
    auditId: '',
    droneId: '',
    droneName: '',
    auditor: '',
    date: '',
    time: '',
    location: '',
    complianceType: 'FAA Part 107',
    auditType: 'Annual Compliance Audit',
    reportType: 'Regulatory Compliance',
    notes: ''
  });

  // Add new compliance report function
  const handleAddComplianceReport = (e) => {
    e.preventDefault();
    
    const newReport = {
      id: `CR-2024-${String(complianceReports.length + 1).padStart(3, '0')}`,
      ...newComplianceReport,
      status: 'Under Review',
      completedBy: '',
      completedAt: '',
      duration: '0 hours',
      complianceScore: 0,
      regulations: [],
      findings: [],
      recommendations: [],
      certifications: [],
      violations: [],
      corrective_actions: [],
      nextAudit: '',
      attachments: []
    };
    
    setComplianceReports([...complianceReports, newReport]);
    setNewComplianceReport({
      auditId: '',
      droneId: '',
      droneName: '',
      auditor: '',
      date: '',
      time: '',
      location: '',
      complianceType: 'FAA Part 107',
      auditType: 'Annual Compliance Audit',
      reportType: 'Regulatory Compliance',
      notes: ''
    });
    setShowAddModal(false);
  };

  // Delete compliance report function
  const handleDeleteComplianceReport = (id) => {
    setComplianceReports(complianceReports.filter(report => report.id !== id));
  };

  // Update compliance report status
  const handleUpdateStatus = (id, newStatus) => {
    setComplianceReports(complianceReports.map(report => 
      report.id === id ? { ...report, status: newStatus } : report
    ));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Compliant': return 'bg-green-100 text-green-700 border-green-200';
      case 'Non-Compliant': return 'bg-red-100 text-red-700 border-red-200';
      case 'Under Review': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'Pending': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Compliant': return <CheckCircle className="w-4 h-4" />;
      case 'Non-Compliant': return <XCircle className="w-4 h-4" />;
      case 'Under Review': return <Clock className="w-4 h-4" />;
      case 'Pending': return <AlertTriangle className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    if (score > 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const filteredComplianceReports = complianceReports.filter(report => {
    const matchesSearch = report.auditId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.droneName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.auditor.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.complianceType.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || report.status.toLowerCase() === filterStatus.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  const headerActions = (
    <div className="flex items-center justify-between gap-4">
      {/* Left Side - Search */}
      <div className="flex items-center gap-4 flex-1">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search reports..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-80 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Quick Filters */}
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="compliant">Compliant</option>
          <option value="non-compliant">Non-Compliant</option>
          <option value="under review">Under Review</option>
        </select>
      </div>

      {/* Right Side - Actions */}
      <div className="flex items-center gap-3">
        <button
          onClick={() => setShowAddModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          New Report
        </button>
      </div>
    </div>
  );

  return (
    <QCLayout
      title="Compliance Reports"
      subtitle="Regulatory compliance audits and certification management"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold text-gray-900">{filteredComplianceReports.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Shield className="w-3 h-3" />
                  Generated
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Shield className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Compliant</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredComplianceReports.filter(r => r.status === 'Compliant').length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  Approved
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Non-Compliant</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredComplianceReports.filter(r => r.status === 'Non-Compliant').length}
                </p>
                <p className="text-sm text-red-600 flex items-center gap-1 mt-1">
                  <XCircle className="w-3 h-3" />
                  Action needed
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fef2f2'}}>
                <XCircle className="w-6 h-6 text-red-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Under Review</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredComplianceReports.filter(r => r.status === 'Under Review').length}
                </p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Clock className="w-3 h-3" />
                  In progress
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Clock className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Compliance Reports Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Compliance Report Records</h3>
                <p className="text-sm text-gray-600 mt-1">Regulatory compliance audits and certification management</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Audit Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Compliance Info
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status & Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Findings & Actions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredComplianceReports.map((report) => (
                  <tr key={report.id} className="hover:bg-gray-50 transition-colors">
                    {/* Audit Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <Shield className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{report.id}</h4>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                              {report.auditId}
                            </span>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-900">{report.droneId} - {report.droneName}</p>
                            <div className="flex items-center gap-3 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <User className="w-3 h-3" />
                                {report.auditor}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                {report.date} at {report.time}
                              </span>
                              <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {report.location}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Compliance Info */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{report.complianceType}</p>
                          <p className="text-xs text-gray-600">{report.auditType}</p>
                        </div>
                        <div className="text-xs text-gray-500">
                          <p>Duration: {report.duration}</p>
                          <p>Report Type: {report.reportType}</p>
                        </div>
                        <div className="text-xs text-gray-500">
                          <p>Regulations: {report.regulations.length}</p>
                          <p>Certifications: {report.certifications.length}</p>
                        </div>
                      </div>
                    </td>

                    {/* Status & Score */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(report.status)}`}>
                          {getStatusIcon(report.status)}
                          <span className="ml-1">{report.status}</span>
                        </span>

                        {report.complianceScore > 0 && (
                          <div className="text-center">
                            <div className={`text-xl font-bold ${getScoreColor(report.complianceScore)}`}>
                              {report.complianceScore}%
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                              <div
                                className={`h-2 rounded-full ${report.complianceScore >= 90 ? 'bg-green-500' : report.complianceScore >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}
                                style={{width: `${report.complianceScore}%`}}
                              ></div>
                            </div>
                          </div>
                        )}

                        {report.completedBy && (
                          <div className="text-xs text-gray-500">
                            <p>Completed by: {report.completedBy}</p>
                            <p>At: {report.completedAt}</p>
                          </div>
                        )}

                        {report.nextAudit && (
                          <div className="text-xs text-gray-500">
                            <p>Next Audit: {report.nextAudit}</p>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Findings & Actions */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        {report.findings.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Findings:</p>
                            <div className="space-y-1">
                              {report.findings.slice(0, 2).map((finding, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <FileCheck className="w-3 h-3 text-blue-500 flex-shrink-0" />
                                  <span className="text-xs text-blue-700">{finding}</span>
                                </div>
                              ))}
                              {report.findings.length > 2 && (
                                <p className="text-xs text-gray-500">+{report.findings.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}

                        {report.violations.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Violations:</p>
                            <div className="space-y-1">
                              {report.violations.slice(0, 2).map((violation, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <XCircle className="w-3 h-3 text-red-500 flex-shrink-0" />
                                  <span className="text-xs text-red-700">{violation}</span>
                                </div>
                              ))}
                              {report.violations.length > 2 && (
                                <p className="text-xs text-gray-500">+{report.violations.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}

                        {report.corrective_actions.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Actions:</p>
                            <div className="space-y-1">
                              {report.corrective_actions.slice(0, 1).map((action, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <Target className="w-3 h-3 text-orange-500 flex-shrink-0" />
                                  <span className="text-xs text-orange-700">{action}</span>
                                </div>
                              ))}
                              {report.corrective_actions.length > 1 && (
                                <p className="text-xs text-gray-500">+{report.corrective_actions.length - 1} more</p>
                              )}
                            </div>
                          </div>
                        )}

                        <div className="text-xs text-gray-500">
                          <p>Attachments: {report.attachments.length}</p>
                        </div>
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                          onClick={() => navigate(`/qc-reports/compliance-reports/${report.id}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <select
                          value={report.status}
                          onChange={(e) => handleUpdateStatus(report.id, e.target.value)}
                          className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="Pending">Pending</option>
                          <option value="Under Review">Under Review</option>
                          <option value="Compliant">Compliant</option>
                          <option value="Non-Compliant">Non-Compliant</option>
                        </select>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteComplianceReport(report.id)}
                          title="Delete Report"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add Compliance Report Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">New Compliance Report</h3>
                <p className="text-sm text-gray-600 mt-1">Create a new compliance audit report</p>
              </div>

              <form onSubmit={handleAddComplianceReport} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Audit ID</label>
                    <input
                      type="text"
                      value={newComplianceReport.auditId}
                      onChange={(e) => setNewComplianceReport({...newComplianceReport, auditId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., AUD-2024-0157"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone ID</label>
                    <input
                      type="text"
                      value={newComplianceReport.droneId}
                      onChange={(e) => setNewComplianceReport({...newComplianceReport, droneId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., DRN-001"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Drone Name</label>
                    <input
                      type="text"
                      value={newComplianceReport.droneName}
                      onChange={(e) => setNewComplianceReport({...newComplianceReport, droneName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Surveyor Alpha"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Auditor</label>
                    <input
                      type="text"
                      value={newComplianceReport.auditor}
                      onChange={(e) => setNewComplianceReport({...newComplianceReport, auditor: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., John Smith"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                    <input
                      type="date"
                      value={newComplianceReport.date}
                      onChange={(e) => setNewComplianceReport({...newComplianceReport, date: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Time</label>
                    <input
                      type="time"
                      value={newComplianceReport.time}
                      onChange={(e) => setNewComplianceReport({...newComplianceReport, time: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input
                      type="text"
                      value={newComplianceReport.location}
                      onChange={(e) => setNewComplianceReport({...newComplianceReport, location: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Compliance Office"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Compliance Type</label>
                    <select
                      value={newComplianceReport.complianceType}
                      onChange={(e) => setNewComplianceReport({...newComplianceReport, complianceType: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="FAA Part 107">FAA Part 107</option>
                      <option value="OSHA Safety">OSHA Safety</option>
                      <option value="ISO 9001">ISO 9001</option>
                      <option value="EPA Environmental">EPA Environmental</option>
                      <option value="GDPR Data Protection">GDPR Data Protection</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Audit Type</label>
                  <select
                    value={newComplianceReport.auditType}
                    onChange={(e) => setNewComplianceReport({...newComplianceReport, auditType: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="Annual Compliance Audit">Annual Compliance Audit</option>
                    <option value="Safety Compliance Review">Safety Compliance Review</option>
                    <option value="Quality Management Audit">Quality Management Audit</option>
                    <option value="Environmental Impact Assessment">Environmental Impact Assessment</option>
                    <option value="Data Privacy Compliance">Data Privacy Compliance</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
                  <select
                    value={newComplianceReport.reportType}
                    onChange={(e) => setNewComplianceReport({...newComplianceReport, reportType: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="Regulatory Compliance">Regulatory Compliance</option>
                    <option value="Safety Compliance">Safety Compliance</option>
                    <option value="Quality Compliance">Quality Compliance</option>
                    <option value="Environmental Compliance">Environmental Compliance</option>
                    <option value="Data Protection Compliance">Data Protection Compliance</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                  <textarea
                    value={newComplianceReport.notes}
                    onChange={(e) => setNewComplianceReport({...newComplianceReport, notes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Additional notes about the compliance audit..."
                  />
                </div>

                <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Compliance Report
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </QCLayout>
  );
};

export default ComplianceReports;
