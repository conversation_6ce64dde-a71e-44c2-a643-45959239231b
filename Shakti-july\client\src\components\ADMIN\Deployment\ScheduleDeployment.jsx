import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Calendar,
  Clock,
  MapPin,
  Plus,
  Search,
  Filter,
  Settings,
  CheckCircle,
  AlertTriangle,
  User,
  Save,
  X,
  Edit,
  Trash2,
  RefreshCw,
  Plane,
  Target
} from 'lucide-react';
import {
  FaRocket,
  FaPlane,
  FaCalendarAlt,
  FaClock,
  FaUser,
  FaMapMarkerAlt,
  FaCheckCircle,
  FaExclamationTriangle,
  FaTimesCircle,
  FaPlay,
  FaPause,
  FaStop
} from 'react-icons/fa';

import AdminSidebar from '../common/AdminSidebar';

const ScheduleDeployment = () => {
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedTime, setSelectedTime] = useState('09:00');
  const [selectedDrone, setSelectedDrone] = useState('');
  const [selectedZone, setSelectedZone] = useState('');
  const [deploymentType, setDeploymentType] = useState('');
  const [duration, setDuration] = useState(60);
  const [priority, setPriority] = useState('medium');
  const [notes, setNotes] = useState('');
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock data for drones
  const availableDrones = [
    { id: 'PRYMAJ95175', name: 'Falcon-X1', status: 'available', battery: 95, location: 'Base Station A' },
    { id: 'PRYMAJ95173', name: 'Eagle-Pro', status: 'available', battery: 87, location: 'Base Station B' },
    { id: 'PRYMAJ95178', name: 'Hawk-Elite', status: 'maintenance', battery: 0, location: 'Maintenance Bay' },
    { id: 'PRYMAJ95176', name: 'Phoenix-Max', status: 'available', battery: 92, location: 'Base Station A' }
  ];

  // Mock data for deployment zones
  const deploymentZones = [
    { id: 'zone-1', name: 'North Industrial Zone', type: 'Industrial', status: 'active' },
    { id: 'zone-2', name: 'South Agricultural Area', type: 'Agricultural', status: 'active' },
    { id: 'zone-3', name: 'East Residential District', type: 'Residential', status: 'restricted' },
    { id: 'zone-4', name: 'West Commercial Hub', type: 'Commercial', status: 'active' },
    { id: 'zone-5', name: 'Central Park Area', type: 'Recreational', status: 'active' }
  ];

  // Mock data for scheduled deployments
  const [scheduledDeployments, setScheduledDeployments] = useState([
    {
      id: 1,
      droneId: 'PRYMAJ95175',
      droneName: 'Falcon-X1',
      zone: 'North Industrial Zone',
      type: 'Surveillance',
      date: '2024-02-15',
      time: '09:00',
      duration: 120,
      priority: 'high',
      status: 'scheduled',
      operator: 'John Smith'
    },
    {
      id: 2,
      droneId: 'PRYMAJ95173',
      droneName: 'Eagle-Pro',
      zone: 'South Agricultural Area',
      type: 'Monitoring',
      date: '2024-02-16',
      time: '14:00',
      duration: 90,
      priority: 'medium',
      status: 'in-progress',
      operator: 'Sarah Johnson'
    },
    {
      id: 3,
      droneId: 'PRYMAJ95176',
      droneName: 'Phoenix-Max',
      zone: 'West Commercial Hub',
      type: 'Inspection',
      date: '2024-02-17',
      time: '11:00',
      duration: 60,
      priority: 'low',
      status: 'completed',
      operator: 'Mike Wilson'
    }
  ]);

  const deploymentTypes = [
    'Surveillance',
    'Monitoring',
    'Inspection',
    'Search & Rescue',
    'Delivery',
    'Mapping',
    'Security Patrol',
    'Environmental Survey'
  ];

  const operators = [
    'John Smith',
    'Sarah Johnson',
    'Mike Wilson',
    'Emily Davis',
    'Robert Brown'
  ];

  const getStatusIcon = (status) => {
    switch (status) {
      case 'scheduled':
        return <FaClock className="w-4 h-4 text-blue-600" />;
      case 'in-progress':
        return <FaPlay className="w-4 h-4 text-green-600" />;
      case 'completed':
        return <FaCheckCircle className="w-4 h-4 text-gray-600" />;
      case 'cancelled':
        return <FaTimesCircle className="w-4 h-4 text-red-600" />;
      default:
        return <FaClock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'in-progress':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredDeployments = scheduledDeployments.filter(deployment => {
    const matchesSearch = !searchTerm || 
      deployment.droneName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      deployment.zone.toLowerCase().includes(searchTerm.toLowerCase()) ||
      deployment.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      deployment.operator.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || deployment.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const handleScheduleDeployment = () => {
    if (!selectedDrone || !selectedZone || !deploymentType || !selectedDate) {
      alert('Please fill in all required fields');
      return;
    }

    const newDeployment = {
      id: Date.now(),
      droneId: selectedDrone,
      droneName: availableDrones.find(d => d.id === selectedDrone)?.name || 'Unknown',
      zone: deploymentZones.find(z => z.id === selectedZone)?.name || 'Unknown',
      type: deploymentType,
      date: selectedDate,
      time: selectedTime,
      duration,
      priority,
      status: 'scheduled',
      operator: operators[0] // Default to first operator
    };

    setScheduledDeployments([...scheduledDeployments, newDeployment]);
    setShowScheduleModal(false);
    
    // Reset form
    setSelectedDrone('');
    setSelectedZone('');
    setDeploymentType('');
    setNotes('');
    setPriority('medium');
    setDuration(60);
  };

  const handleDeleteDeployment = (id) => {
    if (window.confirm('Are you sure you want to delete this deployment?')) {
      setScheduledDeployments(scheduledDeployments.filter(deployment => deployment.id !== id));
    }
  };

  const handleStatusChange = (id, newStatus) => {
    setScheduledDeployments(scheduledDeployments.map(deployment => 
      deployment.id === id ? { ...deployment, status: newStatus } : deployment
    ));
  };

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
      <AdminSidebar />
      
      {/* Main Content */}
      <div className="lg:pl-[250px] w-full">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admindeployment')}
                className="flex items-center gap-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <ArrowLeft size={16} />
                <span className="hidden sm:inline">Back to Deployment</span>
              </button>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <FaRocket className="text-blue-600" />
                  Schedule Deployment
                </h2>
                <p className="text-gray-600 mt-1">Plan and schedule drone deployment missions</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowScheduleModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus size={18} />
                <span className="hidden sm:inline">New Deployment</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 lg:p-6 space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Scheduled</p>
                  <p className="text-2xl font-bold text-gray-900">{scheduledDeployments.length}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <FaCalendarAlt className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">In Progress</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {scheduledDeployments.filter(d => d.status === 'in-progress').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <FaPlay className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Available Drones</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {availableDrones.filter(d => d.status === 'available').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                  <FaPlane className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Zones</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {deploymentZones.filter(z => z.status === 'active').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <FaMapMarkerAlt className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    placeholder="Search by drone, zone, type, or operator..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="flex gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="in-progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
          </div>

          {/* Deployments Table */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <FaRocket className="text-blue-600" />
                Scheduled Deployments ({filteredDeployments.length})
              </h3>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Drone
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Zone & Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Schedule
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Operator
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredDeployments.map((deployment) => (
                    <tr key={deployment.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <FaPlane className="h-5 w-5 text-blue-600" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{deployment.droneName}</div>
                            <div className="text-xs text-gray-500">ID: {deployment.droneId}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-medium">{deployment.zone}</div>
                        <div className="text-xs text-gray-500">{deployment.type}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 flex items-center gap-1">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          {deployment.date}
                        </div>
                        <div className="text-xs text-gray-500 flex items-center gap-1">
                          <Clock className="w-3 h-3 text-gray-400" />
                          {deployment.time} ({deployment.duration}min)
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 flex items-center gap-2">
                          <User className="w-4 h-4 text-gray-400" />
                          {deployment.operator}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(deployment.priority)}`}>
                          {deployment.priority.charAt(0).toUpperCase() + deployment.priority.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(deployment.status)}
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(deployment.status)}`}>
                            {deployment.status.charAt(0).toUpperCase() + deployment.status.slice(1).replace('-', ' ')}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end gap-2">
                          {deployment.status === 'scheduled' && (
                            <button
                              onClick={() => handleStatusChange(deployment.id, 'in-progress')}
                              className="text-green-600 hover:text-green-900 p-1 rounded transition-colors"
                              title="Start Deployment"
                            >
                              <FaPlay size={16} />
                            </button>
                          )}
                          {deployment.status === 'in-progress' && (
                            <button
                              onClick={() => handleStatusChange(deployment.id, 'completed')}
                              className="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                              title="Complete Deployment"
                            >
                              <FaStop size={16} />
                            </button>
                          )}
                          <button
                            onClick={() => handleDeleteDeployment(deployment.id)}
                            className="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                            title="Delete"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {filteredDeployments.length === 0 && (
                <div className="text-center py-12">
                  <FaRocket className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No deployments scheduled</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchTerm || statusFilter !== 'all'
                      ? 'No deployments match your current filters.'
                      : 'Get started by scheduling your first deployment.'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Schedule Deployment Modal */}
      {showScheduleModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <FaRocket className="text-blue-600" />
                  Schedule New Deployment
                </h3>
                <button
                  onClick={() => setShowScheduleModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Drone *
                  </label>
                  <select
                    value={selectedDrone}
                    onChange={(e) => setSelectedDrone(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Choose a drone...</option>
                    {availableDrones.filter(drone => drone.status === 'available').map((drone) => (
                      <option key={drone.id} value={drone.id}>
                        {drone.name} ({drone.id}) - Battery: {drone.battery}%
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Deployment Zone *
                  </label>
                  <select
                    value={selectedZone}
                    onChange={(e) => setSelectedZone(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select zone...</option>
                    {deploymentZones.filter(zone => zone.status === 'active').map((zone) => (
                      <option key={zone.id} value={zone.id}>
                        {zone.name} ({zone.type})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Deployment Type *
                  </label>
                  <select
                    value={deploymentType}
                    onChange={(e) => setDeploymentType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select type...</option>
                    {deploymentTypes.map((type) => (
                      <option key={type} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority Level
                  </label>
                  <select
                    value={priority}
                    onChange={(e) => setPriority(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Scheduled Date *
                  </label>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Scheduled Time *
                  </label>
                  <input
                    type="time"
                    value={selectedTime}
                    onChange={(e) => setSelectedTime(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (minutes)
                  </label>
                  <input
                    type="number"
                    min="15"
                    max="480"
                    value={duration}
                    onChange={(e) => setDuration(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes (Optional)
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add any additional notes or instructions..."
                />
              </div>
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex items-center justify-end gap-4">
                <button
                  onClick={() => setShowScheduleModal(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleScheduleDeployment}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <Save size={16} />
                  Schedule Deployment
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ScheduleDeployment;
