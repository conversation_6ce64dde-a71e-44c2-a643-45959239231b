require('dotenv').config();
const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Simple authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret');
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({
      success: false,
      message: 'Invalid or expired token'
    });
  }
};

// Optional authentication (for endpoints that work with or without auth)
const optionalAuth = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret');
      req.user = decoded;
    } catch (error) {
      // Token invalid, but continue without user
      req.user = null;
    }
  }
  next();
};

// In-memory data
const users = [
  {
    _id: '507f1f77bcf86cd799439001',
    username: 'admin',
    email: '<EMAIL>',
    password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', // Admin123!
    role: 'admin',
    profile: { adminLevel: 'super' }
  },
  {
    _id: '507f1f77bcf86cd799439002',
    username: 'salamkisan',
    email: '<EMAIL>',
    password: '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // Org123!
    role: 'org',
    profile: { 
      organizationId: '507f1f77bcf86cd799439011',
      organizationName: 'Salam Kisan'
    }
  },
  {
    _id: '507f1f77bcf86cd799439003',
    username: 'testorg',
    email: '<EMAIL>',
    password: '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // Test123!
    role: 'org',
    profile: { 
      organizationId: '507f1f77bcf86cd799439012',
      organizationName: 'Test Organization'
    }
  }
];

const organizations = [
  {
    _id: '507f1f77bcf86cd799439011',
    name: 'Salam Kisan',
    displayName: 'Salam Kisan Agricultural Services',
    type: 'private',
    contact: { primaryEmail: '<EMAIL>', phone: '+919876543210' },
    status: 'active',
    isVerified: true,
    createdAt: new Date('2023-01-15')
  },
  {
    _id: '507f1f77bcf86cd799439012',
    name: 'Test Organization',
    displayName: 'Test Organization Ltd.',
    type: 'government',
    contact: { primaryEmail: '<EMAIL>', phone: '+919876543211' },
    status: 'active',
    isVerified: true,
    createdAt: new Date('2023-02-20')
  }
];

let individuals = [];

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'SHAKTI Drone Management API is running (Quick Server)',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: 'development'
  });
});

// Authentication
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('Login attempt:', username);
    
    const user = users.find(u => u.username === username);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    
    const token = jwt.sign(
      { 
        id: user._id, 
        username: user.username, 
        role: user.role,
        profile: user.profile
      },
      process.env.JWT_SECRET || 'default_secret',
      { expiresIn: '7d' }
    );
    
    const userResponse = { ...user };
    delete userResponse.password;
    
    console.log('✅ Login successful for:', username);
    
    res.json({
      success: true,
      message: 'Login successful',
      data: { token, user: userResponse }
    });
    
  } catch (error) {
    console.error('❌ Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed'
    });
  }
});

// Organizations (with optional auth - works for both authenticated and public access)
app.get('/api/organizations', optionalAuth, (req, res) => {
  console.log('Organizations request from:', req.user?.username || 'anonymous');
  res.json({
    success: true,
    message: 'Organizations retrieved successfully',
    data: {
      organizations,
      pagination: { totalCount: organizations.length, currentPage: 1, totalPages: 1 }
    }
  });
});

app.get('/api/organizations/stats', optionalAuth, (req, res) => {
  console.log('Organization stats request from:', req.user?.username || 'anonymous');
  res.json({
    success: true,
    message: 'Organization statistics retrieved successfully',
    data: {
      totalOrganizations: organizations.length,
      activeOrganizations: organizations.filter(o => o.status === 'active').length,
      pendingOrganizations: 0,
      verifiedOrganizations: organizations.filter(o => o.isVerified).length
    }
  });
});

// Individuals (with optional auth)
app.post('/api/individuals', optionalAuth, (req, res) => {
  try {
    const individualData = {
      ...req.body,
      _id: Date.now().toString(),
      gender: req.body.gender?.toLowerCase(),
      createdAt: new Date().toISOString(),
      status: 'pending'
    };
    
    individuals.push(individualData);
    console.log('✅ Individual created:', individualData.fullName);
    
    res.status(201).json({
      success: true,
      message: 'Individual created successfully',
      data: { individual: individualData }
    });
    
  } catch (error) {
    console.error('❌ Error creating individual:', error);
    res.status(400).json({
      success: false,
      message: 'Failed to create individual'
    });
  }
});

app.get('/api/individuals', optionalAuth, (req, res) => {
  console.log('Individuals request from:', req.user?.username || 'anonymous');
  res.json({
    success: true,
    message: 'Individuals retrieved successfully',
    data: {
      individuals: individuals.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)),
      pagination: { totalCount: individuals.length, currentPage: 1, totalPages: 1 }
    }
  });
});

app.get('/api/individuals/stats/overview', optionalAuth, (req, res) => {
  console.log('Individual stats request from:', req.user?.username || 'anonymous');
  res.json({
    success: true,
    message: 'Individual statistics retrieved successfully',
    data: {
      totalIndividuals: individuals.length,
      pendingIndividuals: individuals.filter(i => i.status === 'pending').length,
      approvedIndividuals: individuals.filter(i => i.status === 'approved').length,
      rejectedIndividuals: individuals.filter(i => i.status === 'rejected').length,
      suspendedIndividuals: individuals.filter(i => i.status === 'suspended').length,
      verifiedIndividuals: individuals.filter(i => i.isVerified).length
    }
  });
});

// Individual status updates
app.patch('/api/individuals/:id/status', (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    const individual = individuals.find(i => i._id === id);
    if (!individual) {
      return res.status(404).json({
        success: false,
        message: 'Individual not found'
      });
    }
    
    individual.status = status;
    if (status === 'approved') {
      individual.isVerified = true;
      individual.verificationDate = new Date().toISOString();
    }
    
    console.log(`✅ Individual status updated: ${id} -> ${status}`);
    
    res.json({
      success: true,
      message: 'Individual status updated successfully',
      data: { individual }
    });
    
  } catch (error) {
    console.error('❌ Error updating individual status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update individual status'
    });
  }
});

app.delete('/api/individuals/:id', (req, res) => {
  try {
    const { id } = req.params;
    const index = individuals.findIndex(i => i._id === id);
    
    if (index === -1) {
      return res.status(404).json({
        success: false,
        message: 'Individual not found'
      });
    }
    
    individuals.splice(index, 1);
    console.log(`✅ Individual deleted: ${id}`);
    
    res.json({
      success: true,
      message: 'Individual deleted successfully',
      data: { individualId: id }
    });
    
  } catch (error) {
    console.error('❌ Error deleting individual:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete individual'
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found'
  });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log('\n🚀 SHAKTI Quick Server Started');
  console.log('📍 Environment: development (in-memory database)');
  console.log(`🌐 Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📚 API Base URL: http://localhost:${PORT}/api`);
  console.log('⏰ Started at:', new Date().toISOString());
  
  console.log('\n📋 Available Login Credentials:');
  console.log('================================');
  console.log('👑 Admin Login:');
  console.log('   Username: admin');
  console.log('   Password: Admin123!');
  console.log('\n🏢 Organization Logins:');
  console.log('   Username: salamkisan');
  console.log('   Password: Org123!');
  console.log('   Username: testorg');
  console.log('   Password: Test123!');
  console.log('\n🎉 Ready to use! Your forms will work immediately!');
});
