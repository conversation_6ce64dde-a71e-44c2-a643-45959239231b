import React from 'react';
import { Routes, Route } from 'react-router-dom';
import './App.css';


// import for login page :-
import Login from './components/Login';



// imports forv ADMIN web pages :-
import AdminDashboard from './components/ADMIN/AdminDashboard/AdminDashboard';
import OrganizationForm from './components/ADMIN/common/OrganizationForm';
import IndividualForm from './components/ADMIN/common/IndividualForm';
import AddDrone from './components/ADMIN/common/AddDrone';
import DroneEditForm from './components/ADMIN/DronePage/DroneEditForm';
import AdminSidebar from './components/ADMIN/common/AdminSidebar';
import OrganizationPage from './components/ADMIN/OrganizationPages/OrganizationPage';
import IndividualPage from './components/ADMIN/IndividualPages/IndividualPage';
import DronePage from './components/ADMIN/DronePage/DronePage';
import DroneLogs from './components/ADMIN/DronePage/DroneLogs';
import EnhancedMap from './components/ADMIN/AdminMap/EnhancedMap';
import OrgStatsMap from './components/ADMIN/AdminMap/OrgStatsMap';
import AdminNotification from './components/ADMIN/AdminNotification/AdminNotification';
import Inventory from './components/ADMIN/Inventory/Inventory';
import InventoryAnalytics from './components/ADMIN/Inventory/InventoryAnalytics';
import MaintenanceScheduler from './components/ADMIN/Inventory/MaintenanceScheduler';
import InventorySettings from './components/ADMIN/Inventory/InventorySettings';
import BulkUpload from './components/ADMIN/Inventory/BulkUpload';
import AdminDeployment from './components/ADMIN/Deployment/AdminDeployment';
import DeploymentAnalytics from './components/ADMIN/Deployment/DeploymentAnalytics';
import DeploymentSettings from './components/ADMIN/Deployment/DeploymentSettings';
import ScheduleDeployment from './components/ADMIN/Deployment/ScheduleDeployment';
import ManageZones from './components/ADMIN/Deployment/ManageZones';



// import for ORGANIZATION web pages :-
import OrgDashboard from './components/ORG/Dashboard/OrgDashboard';
import OrgMapSection from './components/ORG/OrgMap/OrgMapSection';
import ViewLogs from './components/ORG/OrgMap/ViewLogs';
import OrgDronePage from './components/ORG/DronePage/OrgDronePage';
import OrgNotification from './components/ORG/OrgNotification/OrgNotification';
import OrgAddDrone from './components/ORG/common/OrgAddDrone';



// import for QC (Quality Control/Maintenance) web pages :-
import QCDashboard from './components/QC/Dashboard/QCDashboard';
import QCMaintenanceScheduler from './components/QC/Maintenance/MaintenanceScheduler';
import QCWorkOrders from './components/QC/Maintenance/WorkOrders';
import QCMaintenanceHistory from './components/QC/Maintenance/MaintenanceHistory';
import QCPartsInventory from './components/QC/Maintenance/PartsInventory';
import QCPreFlightInspection from './components/QC/Inspections/PreFlightInspection';
import QCPostFlightInspection from './components/QC/Inspections/PostFlightInspection';
import QCQualityAssessment from './components/QC/Inspections/QualityAssessment';
import QCCompliance from './components/QC/Inspections/Compliance';
import QCPreFlightInspectionDetail from './components/QC/Inspections/PreFlightInspectionDetail';
import QCPostFlightInspectionDetail from './components/QC/Inspections/PostFlightInspectionDetail';
import QCQualityAssessmentDetail from './components/QC/Inspections/QualityAssessmentDetail';
import QCComplianceDetail from './components/QC/Inspections/ComplianceDetail';
import QCSystemHealth from './components/QC/Diagnostics/SystemHealth';
import QCErrorLogs from './components/QC/Diagnostics/ErrorLogs';
import QCPerformanceAnalytics from './components/QC/Diagnostics/PerformanceAnalytics';
import QCSystemHealthDetail from './components/QC/Diagnostics/SystemHealthDetail';
import QCErrorLogsDetail from './components/QC/Diagnostics/ErrorLogsDetail';
import QCPerformanceAnalyticsDetail from './components/QC/Diagnostics/PerformanceAnalyticsDetail';
import QCFlightReports from './components/QC/Reports/FlightReports';
import QCMaintenanceReports from './components/QC/Reports/MaintenanceReports';
import QCComplianceReports from './components/QC/Reports/ComplianceReports';
import QCSettings from './components/QC/Settings';




// ✅ ADD THESE IMPORTS
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './routes/ProtectedRoute';
import DroneTrackingPage from './components/ORG/common/DroneTrackingPage';






function App() {
  return (
    <Routes>
      <Route path="/" element={<Login />} />

           {/* ADMIN ROUTES */}
           <Route
            path="/adminsidebar"
            element={
              <ProtectedRoute>
                <AdminSidebar />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admindashboard"
            element={
              <ProtectedRoute>
                <AdminDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/organizationpage"
            element={
              <ProtectedRoute>
                <OrganizationPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/organizationform"
            element={
              <ProtectedRoute>
                <OrganizationForm />
              </ProtectedRoute>
            }
          />
          <Route
            path="/individualform"
            element={
              <ProtectedRoute>
                <IndividualForm />
              </ProtectedRoute>
            }
          />
          <Route
            path="/individualpage"
            element={
              <ProtectedRoute>
                <IndividualPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/individuals"
            element={
              <ProtectedRoute>
                <IndividualPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/dronepage"
            element={
              <ProtectedRoute>
                <DronePage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/adddrone"
            element={
              <ProtectedRoute>
                <AddDrone />
              </ProtectedRoute>
            }
          />
          <Route
            path="/dronelogs"
            element={
              <ProtectedRoute>
                <DroneLogs />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/drones/edit/:id"
            element={
              <ProtectedRoute>
                <DroneEditForm />
              </ProtectedRoute>
            }
          />
          <Route
            path="/enhanced-map"
            element={
              <ProtectedRoute>
                <EnhancedMap />
              </ProtectedRoute>
            }
          />
          <Route
            path="/orgstatsmap"
            element={
              <ProtectedRoute>
                <OrgStatsMap />
              </ProtectedRoute>
            }
          />
          <Route
            path="/adminnotification"
            element={
              <ProtectedRoute>
                <AdminNotification />
              </ProtectedRoute>
            }
          />
          <Route
            path="/inventory"
            element={
              <ProtectedRoute>
                <Inventory />
              </ProtectedRoute>
            }
          />
          <Route
            path="/inventory-analytics"
            element={
              <ProtectedRoute>
                <InventoryAnalytics />
              </ProtectedRoute>
            }
          />
          <Route
            path="/inventory-maintenance"
            element={
              <ProtectedRoute>
                <MaintenanceScheduler />
              </ProtectedRoute>
            }
          />
          <Route
            path="/inventory-settings"
            element={
              <ProtectedRoute>
                <InventorySettings />
              </ProtectedRoute>
            }
          />
          <Route
            path="/inventory-bulk-upload"
            element={
              <ProtectedRoute>
                <BulkUpload />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admindeployment"
            element={
              <ProtectedRoute>
                <AdminDeployment />
              </ProtectedRoute>
            }
          />
          <Route
            path="/deployment-analytics"
            element={
              <ProtectedRoute>
                <DeploymentAnalytics />
              </ProtectedRoute>
            }
          />
          <Route
            path="/deployment-settings"
            element={
              <ProtectedRoute>
                <DeploymentSettings />
              </ProtectedRoute>
            }
          />
          <Route
            path="/schedule-deployment"
            element={
              <ProtectedRoute>
                <ScheduleDeployment />
              </ProtectedRoute>
            }
          />
          <Route
            path="/manage-zones"
            element={
              <ProtectedRoute>
                <ManageZones />
              </ProtectedRoute>
            }
          />










          {/* ORG ROUTES */}
          <Route
            path="/orgdashboard"
            element={
              <ProtectedRoute>
                <OrgDashboard />
              </ProtectedRoute>
            }
          />

           <Route
            path="/orgmapsection"
            element={
              <ProtectedRoute>
                <OrgMapSection />
              </ProtectedRoute>
            }
          />

          <Route
            path="/viewlogs"
            element={
              <ProtectedRoute>
                <ViewLogs />
              </ProtectedRoute>
            }
          />

          <Route
            path="/orgdronepage"
            element={
              <ProtectedRoute>
                <OrgDronePage />
              </ProtectedRoute>
            }
          />
         
         <Route
            path="/orgnotification"
            element={
              <ProtectedRoute>
                <OrgNotification />
              </ProtectedRoute>
            }
          />

          <Route
            path="/orgadddrone"
            element={
              <ProtectedRoute>
                <OrgAddDrone />
              </ProtectedRoute>
            }
          />

          <Route
            path="/dronetrackingpage"
            element={
              <ProtectedRoute>
                <DroneTrackingPage />
              </ProtectedRoute>
            }
          />

          {/* QC (Quality Control/Maintenance) ROUTES */}



          <Route
            path="/qc-dashboard"
            element={
              <ProtectedRoute>
                <QCDashboard />
              </ProtectedRoute>
            }
          />

          {/* QC Maintenance Routes */}
          <Route
            path="/qc-maintenance/scheduler"
            element={
              <ProtectedRoute>
                <QCMaintenanceScheduler />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-maintenance/work-orders"
            element={
              <ProtectedRoute>
                <QCWorkOrders />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-maintenance/history"
            element={
              <ProtectedRoute>
                <QCMaintenanceHistory />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-maintenance/inventory"
            element={
              <ProtectedRoute>
                <QCPartsInventory />
              </ProtectedRoute>
            }
          />

          {/* QC Inspection Routes */}
          <Route
            path="/qc-inspections/pre-flight"
            element={
              <ProtectedRoute>
                <QCPreFlightInspection />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-inspections/post-flight"
            element={
              <ProtectedRoute>
                <QCPostFlightInspection />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-inspections/quality-assessment"
            element={
              <ProtectedRoute>
                <QCQualityAssessment />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-inspections/compliance"
            element={
              <ProtectedRoute>
                <QCCompliance />
              </ProtectedRoute>
            }
          />

          {/* QC Inspection Detail Routes */}
          <Route
            path="/qc-inspections/pre-flight/:id"
            element={
              <ProtectedRoute>
                <QCPreFlightInspectionDetail />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-inspections/post-flight/:id"
            element={
              <ProtectedRoute>
                <QCPostFlightInspectionDetail />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-inspections/quality-assessment/:id"
            element={
              <ProtectedRoute>
                <QCQualityAssessmentDetail />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-inspections/compliance/:id"
            element={
              <ProtectedRoute>
                <QCComplianceDetail />
              </ProtectedRoute>
            }
          />

          {/* QC Diagnostics Routes */}
          <Route
            path="/qc-diagnostics/system-health"
            element={
              <ProtectedRoute>
                <QCSystemHealth />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-diagnostics/error-logs"
            element={
              <ProtectedRoute>
                <QCErrorLogs />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-diagnostics/performance-analytics"
            element={
              <ProtectedRoute>
                <QCPerformanceAnalytics />
              </ProtectedRoute>
            }
          />

          {/* QC Diagnostics Detail Routes */}
          <Route
            path="/qc-diagnostics/system-health/:id"
            element={
              <ProtectedRoute>
                <QCSystemHealthDetail />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-diagnostics/error-logs/:id"
            element={
              <ProtectedRoute>
                <QCErrorLogsDetail />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-diagnostics/performance-analytics/:id"
            element={
              <ProtectedRoute>
                <QCPerformanceAnalyticsDetail />
              </ProtectedRoute>
            }
          />

          {/* QC Reports Routes */}
          <Route
            path="/qc-reports/flight-reports"
            element={
              <ProtectedRoute>
                <QCFlightReports />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-reports/maintenance-reports"
            element={
              <ProtectedRoute>
                <QCMaintenanceReports />
              </ProtectedRoute>
            }
          />
          <Route
            path="/qc-reports/compliance-reports"
            element={
              <ProtectedRoute>
                <QCComplianceReports />
              </ProtectedRoute>
            }
          />

          {/* QC Settings Routes */}
          <Route
            path="/qc-settings/*"
            element={
              <ProtectedRoute>
                <QCSettings />
              </ProtectedRoute>
            }
          />


        </Routes>
  );
}

export default App;
