import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import L from "leaflet";
import {
  X,
  Plane,
  Battery,
  Gauge,
  MapPin,
  Clock,
  Activity,
  AlertTriangle,
  CheckCircle,
  Pause,
  Settings,
  RefreshCw,
  Maximize2,
  Building,
  User,
  Phone,
  Mail,
  Calendar
} from 'lucide-react';

// Create drone status icons
const createDroneIcon = (status, battery) => {
  const colors = {
    ACTIVE: '#10b981',
    FLYING: '#3b82f6',
    INACTIVE: '#6b7280',
    MAINTENANCE: '#f59e0b',
    CRASHED: '#ef4444'
  };

  const color = colors[status] || colors.INACTIVE;
  const batteryColor = battery > 50 ? '#10b981' : battery > 20 ? '#f59e0b' : '#ef4444';

  return new L.DivIcon({
    html: `
      <div style="
        background-color: ${color};
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      ">
        <div style="
          width: 8px;
          height: 8px;
          background-color: white;
          border-radius: 50%;
        "></div>
        <div style="
          position: absolute;
          bottom: -3px;
          right: -3px;
          width: 8px;
          height: 8px;
          background-color: ${batteryColor};
          border: 1px solid white;
          border-radius: 50%;
        "></div>
      </div>
    `,
    className: 'custom-drone-icon',
    iconSize: [24, 24],
    iconAnchor: [12, 12],
    popupAnchor: [0, -12],
  });
};

const DroneDetailsPanel = ({ 
  entity, 
  entityType, 
  drones, 
  isVisible, 
  onClose, 
  onRefresh 
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedDrone, setSelectedDrone] = useState(null);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await onRefresh();
    setIsRefreshing(false);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'ACTIVE': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'FLYING': return <Activity className="w-4 h-4 text-blue-600" />;
      case 'INACTIVE': return <Pause className="w-4 h-4 text-gray-600" />;
      case 'MAINTENANCE': return <Settings className="w-4 h-4 text-amber-600" />;
      case 'CRASHED': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default: return <Plane className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-600 bg-green-50 border-green-200';
      case 'FLYING': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'INACTIVE': return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'MAINTENANCE': return 'text-amber-600 bg-amber-50 border-amber-200';
      case 'CRASHED': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getBatteryColor = (battery) => {
    if (battery > 50) return 'text-green-600 bg-green-100';
    if (battery > 20) return 'text-amber-600 bg-amber-100';
    return 'text-red-600 bg-red-100';
  };

  if (!isVisible || !entity) return null;

  const activeDrones = drones.filter(drone => drone.status === 'ACTIVE' || drone.status === 'FLYING');
  const inactiveDrones = drones.filter(drone => drone.status === 'INACTIVE');
  const maintenanceDrones = drones.filter(drone => drone.status === 'MAINTENANCE');
  const crashedDrones = drones.filter(drone => drone.status === 'CRASHED');

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            {entityType === 'organization' ? (
              <Building className="w-6 h-6 text-blue-600" />
            ) : (
              <User className="w-6 h-6 text-green-600" />
            )}
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                {entityType === 'organization' ? entity.name : entity.fullName}
              </h2>
              <p className="text-sm text-gray-600">
                {entityType === 'organization' ? entity.orgId : entity.individualId} • 
                {entity.state}, {entityType === 'organization' ? entity.district : entity.city}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - Drone List */}
          <div className="w-1/3 border-r border-gray-200 p-4 overflow-y-auto">
            {/* Stats */}
            <div className="grid grid-cols-2 gap-3 mb-6">
              <div className="bg-green-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">Active</span>
                </div>
                <span className="text-2xl font-bold text-green-900">{activeDrones.length}</span>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Pause className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-800">Inactive</span>
                </div>
                <span className="text-2xl font-bold text-gray-900">{inactiveDrones.length}</span>
              </div>
              <div className="bg-amber-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <Settings className="w-4 h-4 text-amber-600" />
                  <span className="text-sm font-medium text-amber-800">Maintenance</span>
                </div>
                <span className="text-2xl font-bold text-amber-900">{maintenanceDrones.length}</span>
              </div>
              <div className="bg-red-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800">Crashed</span>
                </div>
                <span className="text-2xl font-bold text-red-900">{crashedDrones.length}</span>
              </div>
            </div>

            {/* Drone List */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Drone Fleet</h3>
              {drones.map((drone) => (
                <div
                  key={drone.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                    selectedDrone?.id === drone.id ? 'ring-2 ring-blue-500 bg-blue-50' : 'bg-white'
                  }`}
                  onClick={() => setSelectedDrone(drone)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(drone.status)}
                      <span className="font-medium text-gray-900">{drone.id}</span>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(drone.status)}`}>
                      {drone.status}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div className="flex items-center gap-1">
                      <Battery className="w-3 h-3" />
                      <span className={`font-medium ${getBatteryColor(drone.battery).split(' ')[0]}`}>
                        {drone.battery}%
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Gauge className="w-3 h-3" />
                      <span>{drone.altitude}m</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{drone.lastUpdate}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      <span>{drone.lat.toFixed(3)}, {drone.lng.toFixed(3)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Panel - Map */}
          <div className="flex-1 relative">
            {drones.length > 0 ? (
              <MapContainer
                center={[drones[0].lat, drones[0].lng]}
                zoom={14}
                style={{ height: "100%", width: "100%" }}
                className="z-0"
              >
                <TileLayer
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                />

                {/* Entity Location Marker */}
                <Marker
                  position={entity.position}
                  icon={new L.DivIcon({
                    html: `
                      <div style="
                        background-color: ${entityType === 'organization' ? '#3b82f6' : '#10b981'};
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        border: 3px solid white;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 16px;
                        font-weight: bold;
                      ">
                        ${entityType === 'organization' ? '🏢' : '👤'}
                      </div>
                    `,
                    className: 'custom-entity-icon',
                    iconSize: [40, 40],
                    iconAnchor: [20, 20],
                    popupAnchor: [0, -20],
                  })}
                >
                  <Popup>
                    <div className="p-2">
                      <h3 className="font-bold text-gray-800 mb-2">
                        {entityType === 'organization' ? entity.name : entity.fullName}
                      </h3>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-1">
                          <Phone className="w-3 h-3 text-gray-400" />
                          <span className="text-gray-600">{entity.phone}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Mail className="w-3 h-3 text-gray-400" />
                          <span className="text-gray-600">{entity.email}</span>
                        </div>
                      </div>
                    </div>
                  </Popup>
                </Marker>

                {/* Drone Markers */}
                {drones.map((drone) => (
                  <Marker
                    key={drone.id}
                    position={[drone.lat, drone.lng]}
                    icon={createDroneIcon(drone.status, drone.battery)}
                    eventHandlers={{
                      click: () => setSelectedDrone(drone),
                    }}
                  >
                    <Popup>
                      <div className="p-2 min-w-[200px]">
                        <div className="flex items-center gap-2 mb-2">
                          {getStatusIcon(drone.status)}
                          <h3 className="font-bold text-gray-800">{drone.id}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(drone.status)}`}>
                            {drone.status}
                          </span>
                        </div>
                        
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Battery:</span>
                            <span className={`font-medium ${getBatteryColor(drone.battery).split(' ')[0]}`}>
                              {drone.battery}%
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Altitude:</span>
                            <span className="font-medium">{drone.altitude}m</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Last Update:</span>
                            <span className="font-medium">{drone.lastUpdate}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Position:</span>
                            <span className="font-medium text-xs">{drone.lat.toFixed(4)}, {drone.lng.toFixed(4)}</span>
                          </div>
                        </div>
                      </div>
                    </Popup>
                  </Marker>
                ))}
              </MapContainer>
            ) : (
              <div className="flex items-center justify-center h-full bg-gray-50">
                <div className="text-center">
                  <Plane className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">No Drones Available</h3>
                  <p className="text-gray-500">This {entityType} has no drones assigned.</p>
                </div>
              </div>
            )}

            {/* Selected Drone Info Overlay */}
            {selectedDrone && (
              <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-4 max-w-xs z-10">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-bold text-gray-900">{selectedDrone.id}</h4>
                  <button
                    onClick={() => setSelectedDrone(null)}
                    className="p-1 hover:bg-gray-100 rounded"
                  >
                    <X className="w-4 h-4 text-gray-600" />
                  </button>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(selectedDrone.status)}
                    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(selectedDrone.status)}`}>
                      {selectedDrone.status}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center gap-1">
                      <Battery className="w-3 h-3 text-gray-400" />
                      <span className={`font-medium ${getBatteryColor(selectedDrone.battery).split(' ')[0]}`}>
                        {selectedDrone.battery}%
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Gauge className="w-3 h-3 text-gray-400" />
                      <span>{selectedDrone.altitude}m</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3 text-gray-400" />
                    <span className="text-gray-600">Last update: {selectedDrone.lastUpdate}</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <MapPin className="w-3 h-3 text-gray-400" />
                    <span className="text-gray-600 text-xs">
                      {selectedDrone.lat.toFixed(4)}, {selectedDrone.lng.toFixed(4)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DroneDetailsPanel;
