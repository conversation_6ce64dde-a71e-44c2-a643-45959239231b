import React, { useEffect, useState, useCallback } from "react";
import { useNavigate } from 'react-router-dom';
import {
  Bell,
  RefreshCw,
  Loader2
} from 'lucide-react';

// Import components
import AdminSidebar from '../common/AdminSidebar';
import notificationService from '../../../services/notificationService';
import NotificationStats from './NotificationStats';
import NotificationFilters from './NotificationFilters';
import NotificationList from './NotificationList';
import NotificationModal from './NotificationModal';

const AdminNotification = () => {
  const navigate = useNavigate();

  // State management
  const [notifications, setNotifications] = useState([]);
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const [statistics, setStatistics] = useState({
    total: 0,
    unread: 0,
    read: 0,
    priority: { critical: 0, high: 0, medium: 0, low: 0 },
    byCategory: {},
    byType: {},
    lastUpdated: new Date()
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    priority: 'all',
    category: 'all',
    status: 'all',
    dateRange: 'all',
    organization: 'all'
  });

  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [activeTab, setActiveTab] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState(null);
  const [showNotificationModal, setShowNotificationModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize] = useState(20); // Reduced from 50 for better performance

  // Load notifications from API with pagination
  const loadNotifications = useCallback(async (page = currentPage) => {
    setIsLoading(true);
    try {
      const params = {
        page,
        limit: pageSize,
        sortBy,
        sortOrder,
        ...filters,
        search: searchTerm
      };

      const [notificationsResponse, statsResponse] = await Promise.all([
        notificationService.getAllNotifications(params),
        notificationService.getNotificationStats()
      ]);

      if (notificationsResponse.success) {
        const data = notificationsResponse.data.data;
        setNotifications(data.notifications || []);
        setTotalPages(data.pagination?.totalPages || 1);
        setTotalCount(data.pagination?.totalCount || 0);
        setCurrentPage(data.pagination?.currentPage || 1);
      } else {
        console.error('Failed to load notifications:', notificationsResponse.error);
        setNotifications([]);
        setTotalPages(1);
        setTotalCount(0);
      }

      if (statsResponse.success) {
        setStatistics(statsResponse.data.data || {
          total: 0,
          unread: 0,
          read: 0,
          priority: { critical: 0, high: 0, medium: 0, low: 0 },
          byCategory: {},
          byType: {},
          lastUpdated: new Date()
        });
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
      setNotifications([]);
      setTotalPages(1);
      setTotalCount(0);
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, pageSize, sortBy, sortOrder, filters, searchTerm]);

  // Initialize notifications
  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  // Handle page change
  const handlePageChange = useCallback((newPage) => {
    setCurrentPage(newPage);
    loadNotifications(newPage);
  }, [loadNotifications]);

  // Reset to first page when filters change
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
      loadNotifications(1);
    } else {
      loadNotifications(1);
    }
  }, [filters, searchTerm, sortBy, sortOrder]); // eslint-disable-line react-hooks/exhaustive-deps

  // Since we're using server-side filtering and pagination,
  // we can directly use the notifications from the API
  useEffect(() => {
    setFilteredNotifications(notifications);
  }, [notifications]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await loadNotifications();
    } catch (error) {
      console.error('Error refreshing notifications:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [loadNotifications]);

  // Handle notification click
  const handleNotificationClick = useCallback(async (notification) => {
    setSelectedNotification(notification);
    setShowNotificationModal(true);

    // Mark as read if unread
    if (!notification.isRead) {
      try {
        await notificationService.markAsRead(notification._id);
        // Refresh notifications to update the UI
        loadNotifications();
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }
  }, [loadNotifications]);

  // Handle mark as read
  const handleMarkAsRead = useCallback(async (notificationId) => {
    try {
      await notificationService.markAsRead(notificationId);
      loadNotifications();
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, [loadNotifications]);

  // Handle archive
  const handleArchive = useCallback(async (notificationId) => {
    try {
      await notificationService.archiveNotification(notificationId);
      loadNotifications();
    } catch (error) {
      console.error('Error archiving notification:', error);
    }
  }, [loadNotifications]);

  // Handle delete
  const handleDelete = useCallback(async (notificationId) => {
    try {
      await notificationService.deleteNotification(notificationId);
      loadNotifications();
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  }, [loadNotifications]);

  // Handle bulk actions
  const handleBulkAction = useCallback(async (action, notificationIds) => {
    try {
      if (action === 'markAsRead') {
        await notificationService.bulkMarkAsRead(notificationIds);
      } else if (action === 'archive') {
        await notificationService.bulkArchive(notificationIds);
      } else if (action === 'delete') {
        await notificationService.bulkDelete(notificationIds);
      }
      loadNotifications();
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error);
    }
  }, [loadNotifications]);

  // Handle export
  const handleExport = useCallback(() => {
    // In a real app, this would generate a CSV or PDF
    console.log('Exporting notifications:', filteredNotifications);
    alert('Notifications exported successfully!');
  }, [filteredNotifications]);

  // Handle view drone
  const handleViewDrone = useCallback((droneId) => {
    navigate(`/dronepage?id=${droneId}`);
  }, [navigate]);

  // Handle sort change
  const handleSortChange = useCallback((newSortBy, newSortOrder) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />
      <div className="ml-[250px]">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4 text-black">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <Bell className="text-blue-600" />
                Notification Center
              </h2>
              <p className="text-gray-600 mt-1">
                Monitor and manage all system notifications
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                {isRefreshing ? (
                  <Loader2 className="w-5 h-5 text-gray-600 animate-spin" />
                ) : (
                  <RefreshCw className="w-5 h-5 text-gray-600" />
                )}
                <span>Refresh</span>
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-6">
          {/* Tabs */}
          <div className="flex items-center gap-2 mb-6 bg-white rounded-lg border border-gray-200 p-1">
            <button
              onClick={() => setActiveTab('all')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'all'
                  ? 'bg-blue-50 text-blue-600'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              All Notifications
            </button>
            <button
              onClick={() => setActiveTab('unread')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'unread'
                  ? 'bg-blue-50 text-blue-600'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              Unread
              {statistics.unread > 0 && (
                <span className="ml-2 bg-blue-600 text-white text-xs rounded-full px-2 py-0.5">
                  {statistics.unread}
                </span>
              )}
            </button>
            <button
              onClick={() => setActiveTab('critical')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'critical'
                  ? 'bg-red-50 text-red-600'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              Critical
              {statistics.priority.critical > 0 && (
                <span className="ml-2 bg-red-600 text-white text-xs rounded-full px-2 py-0.5">
                  {statistics.priority.critical}
                </span>
              )}
            </button>
            <button
              onClick={() => setActiveTab('archived')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'archived'
                  ? 'bg-gray-200 text-gray-800'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              Archived
            </button>
          </div>

          {/* Stats Dashboard */}
          <div className="mb-6">
            <NotificationStats
              statistics={statistics}
              isLoading={isLoading}
            />
          </div>

          {/* Filters */}
          <div className="mb-6">
            <NotificationFilters
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              filters={filters}
              onFiltersChange={setFilters}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              onRefresh={handleRefresh}
              onExport={handleExport}
              isRefreshing={isRefreshing}
            />
          </div>

          {/* Notification List */}
          <div>
            <NotificationList
              notifications={filteredNotifications}
              onNotificationClick={handleNotificationClick}
              onMarkAsRead={handleMarkAsRead}
              onArchive={handleArchive}
              onDelete={handleDelete}
              onBulkAction={handleBulkAction}
              isLoading={isLoading}
              currentPage={currentPage}
              totalPages={totalPages}
              totalCount={totalCount}
              pageSize={pageSize}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      </div>

      {/* Notification Modal */}
      <NotificationModal
        notification={selectedNotification}
        isOpen={showNotificationModal}
        onClose={() => setShowNotificationModal(false)}
        onMarkAsRead={handleMarkAsRead}
        onArchive={handleArchive}
        onDelete={handleDelete}
        onViewDrone={handleViewDrone}
      />
    </div>
  );
};

export default AdminNotification;
