const axios = require('axios');

async function testIndividualAPI() {
  try {
    console.log('🔑 Step 1: Getting JWT Token...');
    
    // Login to get token
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'admin',
      password: 'Admin123!'
    });

    if (!loginResponse.data.success) {
      console.error('❌ Login failed:', loginResponse.data.message);
      return;
    }

    const token = loginResponse.data.data.token;
    console.log('✅ Login successful!');
    console.log('🎯 JWT Token:', token.substring(0, 50) + '...');

    console.log('\n👤 Step 2: Creating Individual...');
    
    // Create individual with exact same data structure as frontend
    const individualData = {
      fullName: "John Doe",
      gender: "male",
      dateOfBirth: "1990-01-15",
      contact: {
        primaryEmail: "<EMAIL>",
        phone: "9876543210",
        alternativePhone: "9876543211"
      },
      address: {
        street: "123 Main Street, Sector 15",
        city: "New Delhi",
        state: "Delhi",
        country: "India",
        postalCode: "110001"
      },
      documents: {
        panNumber: "**********",
        aadharNumber: "123456789012",
        idProofPath: "id-proof-uploaded.pdf",
        kycDocumentPath: "kyc-document.pdf"
      },
      status: "pending"
    };

    console.log('📤 Sending individual data:', JSON.stringify(individualData, null, 2));

    const individualResponse = await axios.post('http://localhost:5000/api/individuals', individualData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (individualResponse.data.success) {
      console.log('✅ Individual created successfully!');
      console.log('📋 Response:', JSON.stringify(individualResponse.data, null, 2));
      console.log('\n🎉 SUCCESS: The individual API is working correctly!');
      console.log('🔗 Individual ID:', individualResponse.data.data.individual._id);
      
      // Test getting all individuals
      console.log('\n📋 Step 3: Getting All Individuals...');
      const getAllResponse = await axios.get('http://localhost:5000/api/individuals', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (getAllResponse.data.success) {
        console.log('✅ Retrieved individuals successfully!');
        console.log('📊 Total individuals:', getAllResponse.data.data.individuals.length);
        console.log('📈 Pagination:', getAllResponse.data.data.pagination);
      }
      
      // Test getting individual stats
      console.log('\n📊 Step 4: Getting Individual Stats...');
      const statsResponse = await axios.get('http://localhost:5000/api/individuals/stats/overview', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (statsResponse.data.success) {
        console.log('✅ Retrieved individual stats successfully!');
        console.log('📊 Stats:', JSON.stringify(statsResponse.data.data, null, 2));
      }
      
    } else {
      console.error('❌ Individual creation failed:', individualResponse.data.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    
    if (error.response?.data?.errors) {
      console.error('🔍 Validation Errors:', error.response.data.errors);
    }
  }
}

testIndividualAPI();
