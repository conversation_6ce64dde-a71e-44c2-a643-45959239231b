import api from './api';

class NotificationService {
  // Get all notifications with pagination and filtering
  async getAllNotifications(params = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      // Add pagination parameters
      if (params.page) queryParams.append('page', params.page);
      if (params.limit) queryParams.append('limit', params.limit);
      
      // Add sorting parameters
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
      
      // Add filter parameters
      if (params.priority && params.priority !== 'all') {
        queryParams.append('priority', params.priority);
      }
      if (params.category && params.category !== 'all') {
        queryParams.append('category', params.category);
      }
      if (params.status && params.status !== 'all') {
        queryParams.append('status', params.status);
      }
      if (params.dateRange && params.dateRange !== 'all') {
        queryParams.append('dateRange', params.dateRange);
      }
      if (params.organization && params.organization !== 'all') {
        queryParams.append('organization', params.organization);
      }
      if (params.search) {
        queryParams.append('search', params.search);
      }

      const response = await api.get(`/notifications?${queryParams.toString()}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch notifications'
      };
    }
  }

  // Get notification statistics
  async getNotificationStats() {
    try {
      const response = await api.get('/notifications/stats');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error fetching notification stats:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch notification statistics'
      };
    }
  }

  // Get a single notification by ID
  async getNotificationById(id) {
    try {
      const response = await api.get(`/notifications/${id}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error fetching notification:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch notification'
      };
    }
  }

  // Mark notification as read
  async markAsRead(id) {
    try {
      const response = await api.patch(`/notifications/${id}/read`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to mark notification as read'
      };
    }
  }

  // Mark notification as unread
  async markAsUnread(id) {
    try {
      const response = await api.patch(`/notifications/${id}/unread`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error marking notification as unread:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to mark notification as unread'
      };
    }
  }

  // Archive notification
  async archiveNotification(id) {
    try {
      const response = await api.patch(`/notifications/${id}/archive`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error archiving notification:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to archive notification'
      };
    }
  }

  // Unarchive notification
  async unarchiveNotification(id) {
    try {
      const response = await api.patch(`/notifications/${id}/unarchive`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error unarchiving notification:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to unarchive notification'
      };
    }
  }

  // Delete notification
  async deleteNotification(id) {
    try {
      const response = await api.delete(`/notifications/${id}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error deleting notification:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to delete notification'
      };
    }
  }

  // Bulk operations
  async bulkMarkAsRead(ids) {
    try {
      const response = await api.patch('/notifications/bulk/read', { ids });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error bulk marking as read:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to mark notifications as read'
      };
    }
  }

  async bulkArchive(ids) {
    try {
      const response = await api.patch('/notifications/bulk/archive', { ids });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error bulk archiving:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to archive notifications'
      };
    }
  }

  async bulkDelete(ids) {
    try {
      const response = await api.delete('/notifications/bulk', { data: { ids } });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error bulk deleting:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to delete notifications'
      };
    }
  }

  // Create a new notification (for testing purposes)
  async createNotification(notificationData) {
    try {
      const response = await api.post('/notifications', notificationData);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error creating notification:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to create notification'
      };
    }
  }

  // WebSocket connection for real-time notifications
  connectWebSocket(onNotification) {
    try {
      const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:5000';
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('WebSocket connected for notifications');
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'notification' && onNotification) {
            onNotification(data.notification);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = () => {
        console.log('WebSocket disconnected');
        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          this.connectWebSocket(onNotification);
        }, 5000);
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

      return ws;
    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      return null;
    }
  }
}

export default new NotificationService();
