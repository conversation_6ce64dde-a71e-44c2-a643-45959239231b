const mongoose = require('mongoose');
const Individual = require('../models/Individual');
const { sendSuccess, sendError } = require('../utils/response');
const NotificationService = require('../utils/notificationService');

/**
 * Get all individuals with filtering and pagination
 */
const getAllIndividuals = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter = {};

    if (search) {
      filter.$or = [
        { fullName: { $regex: search, $options: 'i' } },
        { 'contact.primaryEmail': { $regex: search, $options: 'i' } },
        { 'documents.panNumber': { $regex: search, $options: 'i' } }
      ];
    }

    if (status) {
      filter.status = status;
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [individuals, totalCount] = await Promise.all([
      Individual.find(filter)
        .populate('createdBy', 'username')
        .populate('verifiedBy', 'username')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Individual.countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / parseInt(limit));
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return sendSuccess(res, {
      individuals,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit: parseInt(limit)
      }
    }, 'Individuals retrieved successfully');

  } catch (error) {
    return sendError(res, 'Failed to retrieve individuals', 500);
  }
};

/**
 * Get individual by ID
 */
const getIndividualById = async (req, res) => {
  try {
    const { id } = req.params;

    const individual = await Individual.findById(id)
      .populate('createdBy', 'username')
      .populate('verifiedBy', 'username');

    if (!individual) {
      return sendError(res, 'Individual not found', 404);
    }

    return sendSuccess(res, { individual }, 'Individual retrieved successfully');

  } catch (error) {
    return sendError(res, 'Failed to retrieve individual', 500);
  }
};

/**
 * Create new individual
 */
const createIndividual = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const individualData = {
      ...req.body,
      createdBy: req.user._id,
      lastModifiedBy: req.user._id
    };

    // Check for duplicate email
    const existingEmail = await Individual.findOne({ 
      'contact.primaryEmail': individualData.contact.primaryEmail 
    }).session(session);
    
    if (existingEmail) {
      await session.abortTransaction();
      return sendError(res, 'Individual with this email already exists', 409);
    }

    // Check for duplicate PAN
    const existingPAN = await Individual.findOne({ 
      'documents.panNumber': individualData.documents.panNumber.toUpperCase() 
    }).session(session);
    
    if (existingPAN) {
      await session.abortTransaction();
      return sendError(res, 'Individual with this PAN number already exists', 409);
    }

    // Check for duplicate Aadhar
    const existingAadhar = await Individual.findOne({ 
      'documents.aadharNumber': individualData.documents.aadharNumber 
    }).session(session);
    
    if (existingAadhar) {
      await session.abortTransaction();
      return sendError(res, 'Individual with this Aadhar number already exists', 409);
    }

    // Create individual
    const individual = new Individual(individualData);
    await individual.save({ session });

    // Populate the created individual
    await individual.populate([
      { path: 'createdBy', select: 'username' }
    ]);

    await session.commitTransaction();

    // Create notification for individual creation
    try {
      await NotificationService.createIndividualNotification('created', individual, req.user);
    } catch (notificationError) {
      // Don't fail the main operation if notification fails
    }

    return sendSuccess(res, { individual }, 'Individual created successfully', 201);

  } catch (error) {
    await session.abortTransaction();

    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => ({
        field: err.path,
        message: err.message,
        value: err.value
      }));
      return sendError(res, `Validation failed: ${validationErrors.map(e => `${e.field}: ${e.message}`).join(', ')}`, 400, validationErrors);
    }

    if (error.name === 'MongoError' || error.name === 'MongoServerError') {
      return sendError(res, 'Database error occurred', 500);
    }

    return sendError(res, 'Failed to create individual', 500);

  } finally {
    session.endSession();
  }
};

/**
 * Update individual
 */
const updateIndividual = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      lastModifiedBy: req.user._id
    };

    const individual = await Individual.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('createdBy', 'username')
     .populate('verifiedBy', 'username');

    if (!individual) {
      return sendError(res, 'Individual not found', 404);
    }

    // Create notification for individual update
    try {
      await NotificationService.createIndividualNotification('updated', individual, req.user);
    } catch (notificationError) {
      // Don't fail the main operation if notification fails
    }

    return sendSuccess(res, { individual }, 'Individual updated successfully');

  } catch (error) {
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return sendError(res, `Validation failed: ${validationErrors.join(', ')}`, 400);
    }

    return sendError(res, 'Failed to update individual', 500);
  }
};

/**
 * Update individual status
 */
const updateIndividualStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const updateData = {
      status,
      lastModifiedBy: req.user._id
    };

    // If approving, set verification details
    if (status === 'approved') {
      updateData.isVerified = true;
      updateData.verificationDate = new Date();
      updateData.verifiedBy = req.user._id;
    }

    const individual = await Individual.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('createdBy', 'username')
     .populate('verifiedBy', 'username');

    if (!individual) {
      return sendError(res, 'Individual not found', 404);
    }

    return sendSuccess(res, { individual }, 'Individual status updated successfully');

  } catch (error) {
    return sendError(res, 'Failed to update individual status', 500);
  }
};

/**
 * Delete individual
 */
const deleteIndividual = async (req, res) => {
  try {
    const { id } = req.params;

    const individual = await Individual.findById(id);
    if (!individual) {
      return sendError(res, 'Individual not found', 404);
    }

    await Individual.findByIdAndDelete(id);

    return sendSuccess(res, { individualId: id }, 'Individual deleted successfully');

  } catch (error) {
    return sendError(res, 'Failed to delete individual', 500);
  }
};

/**
 * Get individual statistics
 */
const getIndividualStats = async (req, res) => {
  try {
    const stats = await Individual.aggregate([
      {
        $group: {
          _id: null,
          totalIndividuals: { $sum: 1 },
          pendingIndividuals: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          approvedIndividuals: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          rejectedIndividuals: {
            $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
          },
          suspendedIndividuals: {
            $sum: { $cond: [{ $eq: ['$status', 'suspended'] }, 1, 0] }
          },
          verifiedIndividuals: {
            $sum: { $cond: ['$isVerified', 1, 0] }
          },
          totalAllocatedDrones: { $sum: { $ifNull: ['$allocatedDrones', 0] } }
        }
      }
    ]);

    const result = stats[0] || {
      totalIndividuals: 0,
      pendingIndividuals: 0,
      approvedIndividuals: 0,
      rejectedIndividuals: 0,
      suspendedIndividuals: 0,
      verifiedIndividuals: 0,
      totalAllocatedDrones: 0
    };

    // Ensure totalAllocatedDrones is included
    if (!result.hasOwnProperty('totalAllocatedDrones')) {
      result.totalAllocatedDrones = 0;
    }

    return sendSuccess(res, result, 'Individual statistics retrieved successfully');

  } catch (error) {
    return sendError(res, 'Failed to retrieve individual statistics', 500);
  }
};

module.exports = {
  getAllIndividuals,
  getIndividualById,
  createIndividual,
  updateIndividual,
  updateIndividualStatus,
  deleteIndividual,
  getIndividualStats
};
