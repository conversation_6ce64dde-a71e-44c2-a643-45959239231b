import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  ArrowLeft,
  ClipboardCheck,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Battery,
  Thermometer,
  Activity,
  Zap,
  Edit,
  Download,
  Printer,
  TrendingUp,
  FileText
} from 'lucide-react';

const PostFlightInspectionDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [inspection, setInspection] = useState(null);
  const [loading, setLoading] = useState(true);

  // Sample inspection data
  useEffect(() => {
    setTimeout(() => {
      const sampleInspection = {
        id: id || 'POI-2024-001',
        droneId: 'DRN-001',
        droneName: 'Surveyor Alpha',
        inspector: '<PERSON>',
        date: '2024-01-15',
        time: '17:30',
        location: 'Landing Pad A',
        status: 'Passed',
        flightDuration: '45 minutes',
        flightDistance: '12.5 km',
        checklist: {
          battery: { 
            status: 'Pass', 
            voltage: '24.8V', 
            temperature: '32°C', 
            cycles: '156',
            degradation: '2%',
            efficiency: '96%'
          },
          motors: { 
            status: 'Pass', 
            temperature: '45°C', 
            vibration: 'Normal', 
            efficiency: '98%',
            runtime: '45min',
            wear: 'Minimal'
          },
          sensors: { 
            status: 'Pass', 
            calibration: 'Stable', 
            accuracy: 'High', 
            drift: 'None',
            temperature: 'Normal',
            humidity: 'Acceptable'
          },
          structure: { 
            status: 'Pass', 
            condition: 'Excellent', 
            wear: 'Minimal', 
            damage: 'None',
            stress: 'Normal',
            fatigue: 'Low'
          },
          dataLog: { 
            status: 'Pass', 
            errors: '0', 
            warnings: '2', 
            storage: '85%',
            corruption: 'None',
            backup: 'Complete'
          }
        },
        issues: [],
        findings: ['Minor GPS drift during flight', 'Battery performance excellent'],
        notes: 'Successful mission completion. All systems performed within normal parameters.',
        nextMaintenance: '2024-01-20',
        flightMetrics: {
          maxAltitude: '120m',
          avgSpeed: '15 km/h',
          maxSpeed: '25 km/h',
          totalWaypoints: '24',
          completedWaypoints: '24',
          dataCollected: '2.3 GB'
        },
        environmentalData: {
          startTemp: '18°C',
          endTemp: '22°C',
          avgHumidity: '65%',
          maxWindSpeed: '12 km/h',
          weatherCondition: 'Clear'
        }
      };
      setInspection(sampleInspection);
      setLoading(false);
    }, 500);
  }, [id]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pass': return 'bg-green-100 text-green-700 border-green-200';
      case 'Fail': return 'bg-red-100 text-red-700 border-red-200';
      case 'Warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Pass': return <CheckCircle className="w-4 h-4" />;
      case 'Fail': return <XCircle className="w-4 h-4" />;
      case 'Warning': return <AlertTriangle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const headerActions = (
    <div className="flex items-center gap-3">
      <button
        onClick={() => navigate('/qc-inspections/post-flight')}
        className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
      >
        <ArrowLeft className="w-4 h-4" />
        Back to List
      </button>
      <button className="px-4 py-2 text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors flex items-center gap-2">
        <Edit className="w-4 h-4" />
        Edit
      </button>
      <button className="px-4 py-2 text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors flex items-center gap-2">
        <Download className="w-4 h-4" />
        Export
      </button>
      <button className="px-4 py-2 text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors flex items-center gap-2">
        <Printer className="w-4 h-4" />
        Print
      </button>
    </div>
  );

  if (loading) {
    return (
      <QCLayout title="Loading..." subtitle="Please wait">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </QCLayout>
    );
  }

  if (!inspection) {
    return (
      <QCLayout title="Inspection Not Found" subtitle="The requested inspection could not be found">
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Inspection not found</p>
          <button
            onClick={() => navigate('/qc-inspections/post-flight')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Inspections
          </button>
        </div>
      </QCLayout>
    );
  }

  return (
    <QCLayout
      title={`Post-Flight Inspection - ${inspection.id}`}
      subtitle={`${inspection.droneName} (${inspection.droneId})`}
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Inspection Overview */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 rounded-xl flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <ClipboardCheck className="w-8 h-8 text-blue-500" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 mb-2">{inspection.id}</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Inspector:</span>
                    <span className="font-medium">{inspection.inspector}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Date:</span>
                    <span className="font-medium">{inspection.date} at {inspection.time}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Location:</span>
                    <span className="font-medium">{inspection.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Duration:</span>
                    <span className="font-medium">{inspection.flightDuration}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-right">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(inspection.status)}`}>
                {getStatusIcon(inspection.status)}
                <span className="ml-2">{inspection.status}</span>
              </span>
              <p className="text-sm text-gray-500 mt-2">Next Maintenance: {inspection.nextMaintenance}</p>
            </div>
          </div>
        </div>

        {/* Flight Metrics */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
              <TrendingUp className="w-5 h-5 text-blue-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Flight Performance Metrics</h3>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{inspection.flightDistance}</p>
              <p className="text-sm text-gray-600">Distance</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">{inspection.flightMetrics.maxAltitude}</p>
              <p className="text-sm text-gray-600">Max Altitude</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-purple-600">{inspection.flightMetrics.avgSpeed}</p>
              <p className="text-sm text-gray-600">Avg Speed</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-orange-600">{inspection.flightMetrics.completedWaypoints}/{inspection.flightMetrics.totalWaypoints}</p>
              <p className="text-sm text-gray-600">Waypoints</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-indigo-600">{inspection.flightMetrics.dataCollected}</p>
              <p className="text-sm text-gray-600">Data Collected</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-teal-600">{inspection.environmentalData.weatherCondition}</p>
              <p className="text-sm text-gray-600">Weather</p>
            </div>
          </div>
        </div>

        {/* System Health Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Battery Analysis */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Battery className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Battery Analysis</h3>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(inspection.checklist.battery.status)}`}>
                  {getStatusIcon(inspection.checklist.battery.status)}
                  <span className="ml-1">{inspection.checklist.battery.status}</span>
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Voltage:</span>
                <span className="font-medium ml-2">{inspection.checklist.battery.voltage}</span>
              </div>
              <div>
                <span className="text-gray-600">Temperature:</span>
                <span className="font-medium ml-2">{inspection.checklist.battery.temperature}</span>
              </div>
              <div>
                <span className="text-gray-600">Cycles:</span>
                <span className="font-medium ml-2">{inspection.checklist.battery.cycles}</span>
              </div>
              <div>
                <span className="text-gray-600">Efficiency:</span>
                <span className="font-medium ml-2">{inspection.checklist.battery.efficiency}</span>
              </div>
              <div>
                <span className="text-gray-600">Degradation:</span>
                <span className="font-medium ml-2">{inspection.checklist.battery.degradation}</span>
              </div>
            </div>
          </div>

          {/* Motor Analysis */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Zap className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Motor Performance</h3>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(inspection.checklist.motors.status)}`}>
                  {getStatusIcon(inspection.checklist.motors.status)}
                  <span className="ml-1">{inspection.checklist.motors.status}</span>
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Temperature:</span>
                <span className="font-medium ml-2">{inspection.checklist.motors.temperature}</span>
              </div>
              <div>
                <span className="text-gray-600">Vibration:</span>
                <span className="font-medium ml-2">{inspection.checklist.motors.vibration}</span>
              </div>
              <div>
                <span className="text-gray-600">Efficiency:</span>
                <span className="font-medium ml-2">{inspection.checklist.motors.efficiency}</span>
              </div>
              <div>
                <span className="text-gray-600">Runtime:</span>
                <span className="font-medium ml-2">{inspection.checklist.motors.runtime}</span>
              </div>
              <div>
                <span className="text-gray-600">Wear:</span>
                <span className="font-medium ml-2">{inspection.checklist.motors.wear}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default PostFlightInspectionDetail;
