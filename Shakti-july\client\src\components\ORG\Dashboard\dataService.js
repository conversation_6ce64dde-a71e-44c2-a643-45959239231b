// Centralized Data Management Service for ORG Dashboard
import OrganizationPortalService from '../../../services/organizationPortalService';

class DataService {
  constructor() {
    this.subscribers = new Map();
    this.data = {
      drones: [],
      stats: {},
      activities: [],
      performance: {},
      analytics: {},
      notifications: []
    };
    this.isInitialized = false;
    this.updateInterval = null;
  }

  // Initialize the service with real backend data
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Initialize organization data if needed
      await OrganizationPortalService.initializeOrganization();

      // Load real data from backend
      await this.loadInitialData();
      this.startRealTimeUpdates();
      this.isInitialized = true;
      console.log('DataService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize DataService:', error);
      // Fallback to mock data if backend fails
      await this.loadMockData();
      this.isInitialized = true;
    }
  }

  // Load real data from backend
  async loadInitialData() {
    try {
      // Load dashboard data
      const dashboardResponse = await OrganizationPortalService.getDashboardData();
      if (dashboardResponse.success) {
        this.data.stats = dashboardResponse.data.statistics;
        this.data.drones = dashboardResponse.data.activeDrones || [];
        this.data.activities = dashboardResponse.data.activities || [];
      }

      // Load additional drone data
      const dronesResponse = await OrganizationPortalService.getDrones({ limit: 50 });
      if (dronesResponse.success) {
        this.data.drones = dronesResponse.data.drones || [];
      }

      // Load analytics
      const analyticsResponse = await OrganizationPortalService.getAnalytics();
      if (analyticsResponse.success) {
        this.data.analytics = analyticsResponse.data;
      }

      console.log('✅ Real data loaded from backend');
    } catch (error) {
      console.error('❌ Failed to load real data:', error);
      throw error;
    }
  }

  // Load mock data as fallback
  async loadMockData() {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    this.data = {
      drones: [
        {
          id: 'PRYMAA456991',
          name: 'ARJUNA',
          status: 'Active',
          pilot: 'Deepchand Jaiswal',
          location: { lat: 19.0760, lng: 72.8777, area: 'Mumbai, Maharashtra' },
          battery: 85,
          altitude: 120,
          speed: 45,
          mission: 'Crop Spraying',
          lastUpdate: new Date(),
          efficiency: 92,
          hoursFlown: 245,
          maintenanceScore: 8.5
        },
        {
          id: 'PRYMAA456567',
          name: 'ARJUNA ADVANCE',
          status: 'Flying',
          pilot: 'Yuvraj Khade',
          location: { lat: 28.6139, lng: 77.2090, area: 'Delhi' },
          battery: 67,
          altitude: 95,
          speed: 38,
          mission: 'Surveillance',
          lastUpdate: new Date(),
          efficiency: 88,
          hoursFlown: 189,
          maintenanceScore: 9.2
        },
        {
          id: 'PRYMAA456956',
          name: 'TEJAS',
          status: 'Maintenance',
          pilot: 'Vishal Shelke',
          location: { lat: 17.3850, lng: 78.4867, area: 'Hyderabad, Telangana' },
          battery: 100,
          altitude: 0,
          speed: 0,
          mission: 'Scheduled Maintenance',
          lastUpdate: new Date(),
          efficiency: 85,
          hoursFlown: 312,
          maintenanceScore: 7.8
        },
        {
          id: 'PRYMAA456927',
          name: 'VIKRANT',
          status: 'Active',
          pilot: 'Om Unge',
          location: { lat: 13.0827, lng: 80.2707, area: 'Chennai, Tamil Nadu' },
          battery: 92,
          altitude: 110,
          speed: 42,
          mission: 'Fertilizer Spraying',
          lastUpdate: new Date(),
          efficiency: 90,
          hoursFlown: 198,
          maintenanceScore: 8.9
        },
        {
          id: 'PRYMAA456741',
          name: 'KARNA',
          status: 'Crashed',
          pilot: 'Prathamesh Jadhav',
          location: { lat: 21.1458, lng: 79.0882, area: 'Nagpur, Maharashtra' },
          battery: 0,
          altitude: 0,
          speed: 0,
          mission: 'Emergency Landing',
          lastUpdate: new Date(),
          efficiency: 0,
          hoursFlown: 156,
          maintenanceScore: 3.2
        }
      ],
      stats: {
        totalDrones: 128,
        activeDrones: 94,
        inMaintenance: 18,
        organizations: 16,
        totalFlightHours: 2847,
        totalAreaCovered: 15420,
        fuelSaved: 23.5,
        efficiency: 87.2
      },
      activities: [
        {
          id: 1,
          type: 'success',
          title: 'Mission Completed',
          description: 'Drone ARJUNA successfully completed crop spraying in Maharashtra',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          droneId: 'PRYMAA456991'
        },
        {
          id: 2,
          type: 'warning',
          title: 'Low Battery Alert',
          description: 'Drone TEJAS battery level at 15% - returning to base',
          timestamp: new Date(Date.now() - 12 * 60 * 1000),
          droneId: 'PRYMAA456567'
        },
        {
          id: 3,
          type: 'info',
          title: 'Pilot Assignment',
          description: 'New pilot Deepchand Jaiswal assigned to Drone VIKRANT',
          timestamp: new Date(Date.now() - 25 * 60 * 1000),
          droneId: 'PRYMAA456927'
        }
      ],
      performance: {
        efficiency: 87,
        uptime: 94,
        accuracy: 96,
        speed: 78,
        fuelSaving: 23,
        coverage: 89,
        satisfaction: 92
      },
      analytics: {
        monthlyData: [
          { month: "Jan", hectares: 60, target: 80, efficiency: 75, cost: 45000 },
          { month: "Feb", hectares: 120, target: 100, efficiency: 85, cost: 52000 },
          { month: "Mar", hectares: 140, target: 120, efficiency: 92, cost: 58000 },
          { month: "Apr", hectares: 80, target: 110, efficiency: 68, cost: 48000 },
          { month: "May", hectares: 145, target: 130, efficiency: 88, cost: 62000 },
          { month: "Jun", hectares: 170, target: 150, efficiency: 95, cost: 68000 }
        ],
        regionalData: [
          { name: "Maharashtra", value: 45, drones: 28, area: 1250, efficiency: 92 },
          { name: "Gujarat", value: 30, drones: 18, area: 850, efficiency: 88 },
          { name: "Haryana", value: 10, drones: 8, area: 320, efficiency: 85 },
          { name: "Telangana", value: 10, drones: 6, area: 280, efficiency: 90 },
          { name: "Karnataka", value: 5, drones: 4, area: 150, efficiency: 87 }
        ]
      },
      notifications: [
        { id: 1, message: 'System maintenance scheduled for tonight', type: 'info', read: false },
        { id: 2, message: 'New drone registration approved', type: 'success', read: false },
        { id: 3, message: 'Weather alert for Gujarat region', type: 'warning', read: true }
      ]
    };

    this.notifySubscribers('dataLoaded', this.data);
  }

  // Start real-time updates
  startRealTimeUpdates() {
    this.updateInterval = setInterval(() => {
      this.updateDronePositions();
      this.updateStats();
      this.generateRandomActivity();
    }, 5000);
  }

  // Stop real-time updates
  stopRealTimeUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  // Update drone positions and status
  updateDronePositions() {
    this.data.drones = this.data.drones.map(drone => {
      if (drone.status === 'Active' || drone.status === 'Flying') {
        return {
          ...drone,
          location: {
            ...drone.location,
            lat: drone.location.lat + (Math.random() - 0.5) * 0.01,
            lng: drone.location.lng + (Math.random() - 0.5) * 0.01
          },
          battery: Math.max(0, Math.min(100, drone.battery + (Math.random() - 0.5) * 5)),
          speed: Math.max(0, Math.min(60, drone.speed + (Math.random() - 0.5) * 10)),
          lastUpdate: new Date()
        };
      }
      return drone;
    });

    this.notifySubscribers('dronesUpdated', this.data.drones);
  }

  // Update statistics
  updateStats() {
    const activeDrones = this.data.drones.filter(d => d.status === 'Active' || d.status === 'Flying').length;
    const maintenanceDrones = this.data.drones.filter(d => d.status === 'Maintenance').length;
    
    this.data.stats = {
      ...this.data.stats,
      activeDrones,
      inMaintenance: maintenanceDrones,
      efficiency: Math.max(80, Math.min(95, this.data.stats.efficiency + (Math.random() - 0.5) * 2))
    };

    this.notifySubscribers('statsUpdated', this.data.stats);
  }

  // Generate random activity
  generateRandomActivity() {
    const activities = [
      'Mission started', 'Mission completed', 'Battery low', 'Maintenance required',
      'Weather alert', 'Pilot changed', 'Route updated', 'System update'
    ];
    
    const types = ['success', 'warning', 'info'];
    const randomActivity = {
      id: Date.now(),
      type: types[Math.floor(Math.random() * types.length)],
      title: activities[Math.floor(Math.random() * activities.length)],
      description: 'System generated activity update',
      timestamp: new Date(),
      droneId: this.data.drones[Math.floor(Math.random() * this.data.drones.length)].id
    };

    this.data.activities.unshift(randomActivity);
    if (this.data.activities.length > 20) {
      this.data.activities = this.data.activities.slice(0, 20);
    }

    this.notifySubscribers('activityAdded', randomActivity);
  }

  // Subscribe to data updates
  subscribe(event, callback) {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, new Set());
    }
    this.subscribers.get(event).add(callback);

    // Return unsubscribe function
    return () => {
      const eventSubscribers = this.subscribers.get(event);
      if (eventSubscribers) {
        eventSubscribers.delete(callback);
      }
    };
  }

  // Notify subscribers
  notifySubscribers(event, data) {
    const eventSubscribers = this.subscribers.get(event);
    if (eventSubscribers) {
      eventSubscribers.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in subscriber callback for ${event}:`, error);
        }
      });
    }
  }

  // Get current data
  getData(type) {
    return this.data[type] || null;
  }

  // Update data
  updateData(type, newData) {
    this.data[type] = newData;
    this.notifySubscribers(`${type}Updated`, newData);
  }

  // API simulation methods
  async fetchDroneDetails(droneId) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return this.data.drones.find(drone => drone.id === droneId);
  }

  async updateDroneStatus(droneId, status) {
    try {
      // Update via backend API
      const response = await OrganizationPortalService.updateDroneStatus(droneId, status);
      if (response.success) {
        // Update local data
        const droneIndex = this.data.drones.findIndex(drone => drone.id === droneId);
        if (droneIndex !== -1) {
          this.data.drones[droneIndex].status = status;
          this.notifySubscribers('droneStatusUpdated', { droneId, status });
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ Failed to update drone status:', error);
      return false;
    }
  }

  // Refresh data from backend
  async refreshData() {
    try {
      await this.loadInitialData();
      this.notifySubscribers('dataRefreshed', this.data);
      return true;
    } catch (error) {
      console.error('❌ Failed to refresh data:', error);
      return false;
    }
  }

  // Add new drone via backend
  async addDrone(droneData) {
    try {
      const response = await OrganizationPortalService.addDrone(droneData);
      if (response.success) {
        // Add to local data
        this.data.drones.push(response.data.drone);
        this.notifySubscribers('droneAdded', response.data.drone);
        return response.data.drone;
      }
      throw new Error(response.message || 'Failed to add drone');
    } catch (error) {
      console.error('❌ Failed to add drone:', error);
      throw error;
    }
  }

  async exportData(type, format = 'csv') {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const data = this.getData(type);
    
    if (format === 'csv') {
      // Convert to CSV format
      return this.convertToCSV(data);
    }
    
    return data;
  }

  convertToCSV(data) {
    if (!Array.isArray(data)) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => row[header]).join(','))
    ].join('\n');
    
    return csvContent;
  }

  // Cleanup
  destroy() {
    this.stopRealTimeUpdates();
    this.subscribers.clear();
    this.data = {};
    this.isInitialized = false;
  }
}

// Create singleton instance
const dataService = new DataService();

export default dataService;
