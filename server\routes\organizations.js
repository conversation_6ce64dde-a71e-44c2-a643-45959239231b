const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');
const auth = require('../middleware/auth');
const validation = require('../middleware/validation');
const organizationController = require('../controllers/organizationController');

// Validation rules for organization creation
const createOrganizationValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Organization name must be between 2 and 100 characters'),
  
  body('type')
    .isIn(['government', 'private', 'ngo', 'research', 'military', 'other'])
    .withMessage('Invalid organization type'),
  
  body('contact.primaryEmail')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid primary email is required'),
  
  body('contact.phone')
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Valid phone number is required'),
  
  body('address.street')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Street address is required and cannot exceed 200 characters'),
  
  body('address.city')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('City is required and cannot exceed 100 characters'),
  
  body('address.state')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('State is required and cannot exceed 100 characters'),
  
  body('address.country')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Country is required and cannot exceed 100 characters'),
  
  body('address.postalCode')
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Postal code is required and cannot exceed 20 characters'),
  
  body('registration.registrationNumber')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Registration number is required and cannot exceed 50 characters'),
  
  body('registration.registrationDate')
    .isISO8601()
    .withMessage('Valid registration date is required'),
  
  body('primaryContact.name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Primary contact name is required and cannot exceed 100 characters'),
  
  body('primaryContact.designation')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Primary contact designation is required and cannot exceed 100 characters'),
  
  body('primaryContact.email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid primary contact email is required'),
  
  body('primaryContact.phone')
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Valid primary contact phone is required')
];

// Validation rules for organization update
const updateOrganizationValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid organization ID'),
  
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Organization name must be between 2 and 100 characters'),
  
  body('type')
    .optional()
    .isIn(['government', 'private', 'ngo', 'research', 'military', 'other'])
    .withMessage('Invalid organization type'),
  
  body('contact.primaryEmail')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid primary email is required'),
  
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended', 'pending'])
    .withMessage('Invalid status')
];

// Validation rules for getting organization by ID
const getOrganizationValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid organization ID')
];

// Validation rules for query parameters
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  query('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended', 'pending'])
    .withMessage('Invalid status filter'),
  
  query('type')
    .optional()
    .isIn(['government', 'private', 'ngo', 'research', 'military', 'other'])
    .withMessage('Invalid type filter'),
  
  query('verified')
    .optional()
    .isBoolean()
    .withMessage('Verified must be a boolean value')
];

// Routes

// GET /api/organizations - Get all organizations with filtering and pagination (public access for development)
router.get('/',
  // Temporarily remove auth for development - uncomment below for production
  // auth.authenticate,
  // auth.authorize(['admin']),
  queryValidation,
  validation.handleValidationErrors,
  organizationController.getAllOrganizations
);

// GET /api/organizations/stats - Get organization statistics (public access for development)
router.get('/stats',
  // Temporarily remove auth for development - uncomment below for production
  // auth.authenticate,
  // auth.authorize(['admin']),
  organizationController.getOrganizationStats
);

// GET /api/organizations/analytics - Get organization analytics data (public access for development)
router.get('/analytics',
  // Temporarily remove auth for development - uncomment below for production
  // auth.authenticate,
  // auth.authorize(['admin']),
  organizationController.getOrganizationAnalytics
);

// GET /api/organizations/:id - Get organization by ID
router.get('/:id',
  auth.authenticate,
  auth.authorize(['admin', 'org']),
  getOrganizationValidation,
  validation.handleValidationErrors,
  organizationController.getOrganizationById
);

// POST /api/organizations - Create new organization
router.post('/',
  auth.authenticate,
  auth.authorize(['admin']),
  createOrganizationValidation,
  validation.handleValidationErrors,
  organizationController.createOrganization
);

// PUT /api/organizations/:id - Update organization
router.put('/:id',
  auth.authenticate,
  auth.authorize(['admin']),
  updateOrganizationValidation,
  validation.handleValidationErrors,
  organizationController.updateOrganization
);

// PATCH /api/organizations/:id/status - Update organization status
router.patch('/:id/status',
  auth.authenticate,
  auth.authorize(['admin']),
  param('id').isMongoId().withMessage('Invalid organization ID'),
  body('status').isIn(['active', 'inactive', 'suspended', 'pending']).withMessage('Invalid status'),
  validation.handleValidationErrors,
  organizationController.updateOrganizationStatus
);

// PATCH /api/organizations/:id/verify - Verify organization
router.patch('/:id/verify',
  auth.authenticate,
  auth.authorize(['admin']),
  param('id').isMongoId().withMessage('Invalid organization ID'),
  validation.handleValidationErrors,
  organizationController.verifyOrganization
);

// POST /api/organizations/:id/notes - Add note to organization
router.post('/:id/notes',
  auth.authenticate,
  auth.authorize(['admin']),
  param('id').isMongoId().withMessage('Invalid organization ID'),
  body('content').trim().isLength({ min: 1, max: 1000 }).withMessage('Note content is required and cannot exceed 1000 characters'),
  body('type').optional().isIn(['general', 'verification', 'compliance', 'support']).withMessage('Invalid note type'),
  validation.handleValidationErrors,
  organizationController.addOrganizationNote
);

// DELETE /api/organizations/:id - Delete organization (soft delete)
router.delete('/:id',
  auth.authenticate,
  auth.authorize(['admin']),
  getOrganizationValidation,
  validation.handleValidationErrors,
  organizationController.deleteOrganization
);

// GET /api/organizations/deleted - Get deleted organizations
router.get('/deleted',
  auth.authenticate,
  auth.authorize(['admin']),
  organizationController.getDeletedOrganizations
);

// POST /api/organizations/:id/restore - Restore deleted organization
router.post('/:id/restore',
  auth.authenticate,
  auth.authorize(['admin']),
  getOrganizationValidation,
  validation.handleValidationErrors,
  organizationController.restoreOrganization
);

// GET /api/organizations/:id/users - Get users belonging to organization
router.get('/:id/users',
  auth.authenticate,
  auth.authorize(['admin', 'org']),
  getOrganizationValidation,
  validation.handleValidationErrors,
  organizationController.getOrganizationUsers
);

// GET /api/organizations/:id/drones - Get drones belonging to organization
router.get('/:id/drones',
  auth.authenticate,
  auth.authorize(['admin', 'org']),
  getOrganizationValidation,
  validation.handleValidationErrors,
  organizationController.getOrganizationDrones
);

module.exports = router;
