const User = require('../models/User');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { generateAuthToken } = require('../utils/jwt');
const { sendSuccess, sendError, sendAuthError } = require('../utils/response');

// In-memory users for fallback when MongoDB is not connected
const inMemoryUsers = [
  {
    _id: '507f1f77bcf86cd799439001',
    username: 'admin',
    email: '<EMAIL>',
    password: '$2b$12$NQ/F2EcToJjicggs2YgaDuiFR1c0Dsmr58aYezVXELiSD5nl7Eq8e', // Admin123!
    role: 'admin',
    profile: { adminLevel: 'super' },
    isActive: true,
    lastLogin: null
  },
  {
    _id: '507f1f77bcf86cd799439002',
    username: 'salamkisan',
    email: '<EMAIL>',
    password: '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // Org123!
    role: 'org',
    profile: {
      organizationId: '507f1f77bcf86cd799439011',
      organizationName: 'Salam Kisan'
    },
    isActive: true,
    lastLogin: null
  },
  {
    _id: '507f1f77bcf86cd799439003',
    username: 'testorg',
    email: '<EMAIL>',
    password: '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // Test123!
    role: 'org',
    profile: {
      organizationId: '507f1f77bcf86cd799439012',
      organizationName: 'Test Organization'
    },
    isActive: true,
    lastLogin: null
  }
];

// Check if MongoDB is connected
const isMongoConnected = () => {
  return mongoose.connection.readyState === 1;
};

/**
 * @desc    Login user
 * @route   POST /api/auth/login
 * @access  Public
 */
const login = async (req, res) => {
  try {
    const { username, password, role } = req.body;
    console.log('Login attempt:', username);

    let user = null;

    if (isMongoConnected()) {
      // Use MongoDB if connected
      console.log('Using MongoDB for authentication');
      user = await User.findByCredentials(username, password);
    } else {
      // Use in-memory storage if MongoDB not connected
      console.log('Using in-memory storage for authentication');
      const foundUser = inMemoryUsers.find(u => u.username === username || u.email === username);

      if (!foundUser) {
        return sendAuthError(res, 'Invalid username or password');
      }

      // Check password
      const isValidPassword = await bcrypt.compare(password, foundUser.password);
      if (!isValidPassword) {
        return sendAuthError(res, 'Invalid username or password');
      }

      user = foundUser;
    }

    // Check if role matches (optional role-based login)
    if (role && user.role !== role) {
      return sendAuthError(res, 'Invalid credentials for the selected role');
    }

    // Generate JWT token
    const token = generateAuthToken(user);

    // Prepare user data for response (exclude sensitive information)
    const userData = {
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      profile: user.profile,
      lastLogin: user.lastLogin,
      isActive: user.isActive
    };

    console.log('✅ Login successful for:', username);

    // Send success response
    sendSuccess(res, {
      user: userData,
      token,
      expiresIn: process.env.JWT_EXPIRE || '7d'
    }, 'Login successful', 200);

  } catch (error) {
    console.error('Login error:', error.message);

    // Handle specific error types
    if (error.message.includes('Invalid login credentials')) {
      return sendAuthError(res, 'Invalid username or password');
    } else if (error.message.includes('Account is temporarily locked')) {
      return sendAuthError(res, 'Account is temporarily locked due to too many failed login attempts. Please try again later.');
    } else {
      return sendError(res, 'Login failed. Please try again.', 500);
    }
  }
};

/**
 * @desc    Register new user (Admin only)
 * @route   POST /api/auth/register
 * @access  Private (Admin only)
 */
const register = async (req, res) => {
  try {
    const { username, email, password, role, profile } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ username }, { email }]
    });

    if (existingUser) {
      return sendError(res, 'User with this username or email already exists', 400);
    }

    // Create new user
    const userData = {
      username,
      email,
      password,
      role,
      profile: profile || {}
    };

    const user = new User(userData);
    await user.save();

    // Generate token for the new user
    const token = generateAuthToken(user);

    // Prepare response data
    const responseData = {
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      profile: user.profile,
      isActive: user.isActive,
      createdAt: user.createdAt
    };

    sendSuccess(res, {
      user: responseData,
      token
    }, 'User registered successfully', 201);

  } catch (error) {
    console.error('Registration error:', error);
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => ({
        field: err.path,
        message: err.message
      }));
      return sendError(res, 'Validation failed', 400, validationErrors);
    }
    
    return sendError(res, 'Registration failed. Please try again.', 500);
  }
};

/**
 * @desc    Get current user profile
 * @route   GET /api/auth/me
 * @access  Private
 */
const getMe = async (req, res) => {
  try {
    const user = req.user;

    const userData = {
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      profile: user.profile,
      lastLogin: user.lastLogin,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    sendSuccess(res, { user: userData }, 'Profile retrieved successfully');

  } catch (error) {
    console.error('Get profile error:', error);
    return sendError(res, 'Failed to retrieve profile', 500);
  }
};

/**
 * @desc    Update user profile
 * @route   PUT /api/auth/profile
 * @access  Private
 */
const updateProfile = async (req, res) => {
  try {
    const userId = req.user._id;
    const { email, profile } = req.body;

    // Build update object
    const updateData = {};
    if (email) updateData.email = email;
    if (profile) updateData.profile = { ...req.user.profile, ...profile };

    // Update user
    const user = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    const userData = {
      id: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      profile: user.profile,
      lastLogin: user.lastLogin,
      isActive: user.isActive,
      updatedAt: user.updatedAt
    };

    sendSuccess(res, { user: userData }, 'Profile updated successfully');

  } catch (error) {
    console.error('Update profile error:', error);
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => ({
        field: err.path,
        message: err.message
      }));
      return sendError(res, 'Validation failed', 400, validationErrors);
    }
    
    return sendError(res, 'Failed to update profile', 500);
  }
};

/**
 * @desc    Change password
 * @route   PUT /api/auth/change-password
 * @access  Private
 */
const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user._id;

    // Get user with password
    const user = await User.findById(userId).select('+password');
    
    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Verify current password
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    
    if (!isCurrentPasswordValid) {
      return sendAuthError(res, 'Current password is incorrect');
    }

    // Update password
    user.password = newPassword;
    await user.save();

    sendSuccess(res, null, 'Password changed successfully');

  } catch (error) {
    console.error('Change password error:', error);
    return sendError(res, 'Failed to change password', 500);
  }
};

/**
 * @desc    Logout user (client-side token removal)
 * @route   POST /api/auth/logout
 * @access  Private
 */
const logout = async (req, res) => {
  try {
    // In a JWT-based system, logout is typically handled client-side
    // by removing the token from storage. This endpoint can be used
    // for logging purposes or future token blacklisting implementation.
    
    sendSuccess(res, null, 'Logged out successfully');
  } catch (error) {
    console.error('Logout error:', error);
    return sendError(res, 'Logout failed', 500);
  }
};

module.exports = {
  login,
  register,
  getMe,
  updateProfile,
  changePassword,
  logout
};
