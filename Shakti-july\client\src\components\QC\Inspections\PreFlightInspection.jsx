import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  ClipboardCheck,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Battery,
  Wifi,
  Camera,
  Settings
} from 'lucide-react';

const PreFlightInspection = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDrone, setFilterDrone] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [inspections, setInspections] = useState([]);

  // Initialize with sample inspection data
  React.useEffect(() => {
    if (inspections.length === 0) {
      setInspections([
        {
          id: 'PFI-2024-001',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          inspector: '<PERSON>',
          date: '2024-01-15',
          time: '08:30',
          location: 'Hangar A',
          status: 'Passed',
          duration: '25 minutes',
          checklist: {
            battery: { status: 'Pass', voltage: '25.2V', percentage: '98%' },
            propellers: { status: 'Pass', condition: 'Excellent', balance: 'Good' },
            camera: { status: 'Pass', focus: 'Sharp', gimbal: 'Stable' },
            gps: { status: 'Pass', satellites: '12', accuracy: '1.2m' },
            sensors: { status: 'Pass', imu: 'Calibrated', compass: 'Normal' },
            communication: { status: 'Pass', signal: 'Strong', range: 'Full' }
          },
          issues: [],
          notes: 'All systems operational. Ready for flight.',
          nextInspection: '2024-01-16'
        },
        {
          id: 'PFI-2024-002',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          inspector: 'Sarah Johnson',
          date: '2024-01-15',
          time: '09:15',
          location: 'Field Station B',
          status: 'Failed',
          duration: '35 minutes',
          checklist: {
            battery: { status: 'Pass', voltage: '24.8V', percentage: '95%' },
            propellers: { status: 'Fail', condition: 'Damaged', balance: 'Poor' },
            camera: { status: 'Pass', focus: 'Sharp', gimbal: 'Stable' },
            gps: { status: 'Pass', satellites: '10', accuracy: '1.5m' },
            sensors: { status: 'Pass', imu: 'Calibrated', compass: 'Normal' },
            communication: { status: 'Pass', signal: 'Strong', range: 'Full' }
          },
          issues: ['Propeller #3 has visible crack', 'Propeller imbalance detected'],
          notes: 'Flight prohibited until propeller replacement. Maintenance required.',
          nextInspection: '2024-01-16'
        },
        {
          id: 'PFI-2024-003',
          droneId: 'DRN-005',
          droneName: 'Mapper Gamma',
          inspector: 'Mike Wilson',
          date: '2024-01-14',
          time: '16:45',
          location: 'Hangar A',
          status: 'Conditional',
          duration: '30 minutes',
          checklist: {
            battery: { status: 'Warning', voltage: '24.1V', percentage: '88%' },
            propellers: { status: 'Pass', condition: 'Good', balance: 'Good' },
            camera: { status: 'Pass', focus: 'Sharp', gimbal: 'Stable' },
            gps: { status: 'Pass', satellites: '11', accuracy: '1.3m' },
            sensors: { status: 'Pass', imu: 'Calibrated', compass: 'Normal' },
            communication: { status: 'Pass', signal: 'Strong', range: 'Full' }
          },
          issues: ['Battery voltage slightly low'],
          notes: 'Flight approved with reduced duration. Monitor battery closely.',
          nextInspection: '2024-01-15'
        }
      ]);
    }
  }, [inspections.length]);

  // Form state for new inspection
  const [newInspection, setNewInspection] = useState({
    droneId: '',
    droneName: '',
    inspector: '',
    date: '',
    time: '',
    location: '',
    checklist: {
      battery: { status: 'Pass', voltage: '', percentage: '' },
      propellers: { status: 'Pass', condition: '', balance: '' },
      camera: { status: 'Pass', focus: '', gimbal: '' },
      gps: { status: 'Pass', satellites: '', accuracy: '' },
      sensors: { status: 'Pass', imu: '', compass: '' },
      communication: { status: 'Pass', signal: '', range: '' }
    },
    issues: [],
    notes: ''
  });

  // Add new inspection function
  const handleAddInspection = (e) => {
    e.preventDefault();
    
    // Determine overall status based on checklist
    const hasFailures = Object.values(newInspection.checklist).some(item => item.status === 'Fail');
    const hasWarnings = Object.values(newInspection.checklist).some(item => item.status === 'Warning');
    
    let overallStatus = 'Passed';
    if (hasFailures) overallStatus = 'Failed';
    else if (hasWarnings) overallStatus = 'Conditional';

    const newInsp = {
      id: `PFI-2024-${String(inspections.length + 1).padStart(3, '0')}`,
      ...newInspection,
      status: overallStatus,
      duration: '30 minutes',
      nextInspection: new Date(Date.now() + 24*60*60*1000).toISOString().split('T')[0]
    };
    
    setInspections([...inspections, newInsp]);
    setNewInspection({
      droneId: '',
      droneName: '',
      inspector: '',
      date: '',
      time: '',
      location: '',
      checklist: {
        battery: { status: 'Pass', voltage: '', percentage: '' },
        propellers: { status: 'Pass', condition: '', balance: '' },
        camera: { status: 'Pass', focus: '', gimbal: '' },
        gps: { status: 'Pass', satellites: '', accuracy: '' },
        sensors: { status: 'Pass', imu: '', compass: '' },
        communication: { status: 'Pass', signal: '', range: '' }
      },
      issues: [],
      notes: ''
    });
    setShowAddModal(false);
  };

  // Delete inspection function
  const handleDeleteInspection = (id) => {
    setInspections(inspections.filter(inspection => inspection.id !== id));
  };

  // Update inspection status
  const handleUpdateStatus = (id, newStatus) => {
    setInspections(inspections.map(inspection => 
      inspection.id === id ? { ...inspection, status: newStatus } : inspection
    ));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Passed': return 'bg-green-100 text-green-700 border-green-200';
      case 'Failed': return 'bg-red-100 text-red-700 border-red-200';
      case 'Conditional': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'In Progress': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Passed': return <CheckCircle className="w-4 h-4" />;
      case 'Failed': return <XCircle className="w-4 h-4" />;
      case 'Conditional': return <AlertTriangle className="w-4 h-4" />;
      case 'In Progress': return <Clock className="w-4 h-4" />;
      default: return <ClipboardCheck className="w-4 h-4" />;
    }
  };

  const getChecklistItemColor = (status) => {
    switch (status) {
      case 'Pass': return 'text-green-600';
      case 'Fail': return 'text-red-600';
      case 'Warning': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const filteredInspections = inspections.filter(inspection => {
    const matchesSearch = inspection.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         inspection.droneName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         inspection.inspector.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === 'all' || inspection.status.toLowerCase() === filterStatus.toLowerCase();
    const matchesDrone = filterDrone === 'all' || inspection.droneId === filterDrone;
    return matchesSearch && matchesStatus && matchesDrone;
  });

  const headerActions = (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search inspections..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
          onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
          onBlur={(e) => e.target.style.boxShadow = 'none'}
        />
      </div>
      
      <select
        value={filterStatus}
        onChange={(e) => setFilterStatus(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Status</option>
        <option value="passed">Passed</option>
        <option value="failed">Failed</option>
        <option value="conditional">Conditional</option>
        <option value="in progress">In Progress</option>
      </select>

      <button
        onClick={() => setShowAddModal(true)}
        className="px-4 py-2 text-white rounded-lg transition-all duration-150 hover:shadow-lg hover:-translate-y-0.5 flex items-center gap-2"
        style={{backgroundColor: '#e0e7ff'}}
        onMouseEnter={(e) => e.target.style.backgroundColor = '#c7d2fe'}
        onMouseLeave={(e) => e.target.style.backgroundColor = '#e0e7ff'}
      >
        <Plus className="w-4 h-4 text-blue-600" />
        <span className="text-blue-700 font-medium">New Inspection</span>
      </button>
    </div>
  );

  return (
    <QCLayout
      title="Pre-Flight Inspections"
      subtitle="Conduct and manage pre-flight safety inspections"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Today's Inspections</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredInspections.filter(i => i.date === new Date().toISOString().split('T')[0]).length}
                </p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <ClipboardCheck className="w-3 h-3" />
                  Completed
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <ClipboardCheck className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Passed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredInspections.filter(i => i.status === 'Passed').length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  Ready for flight
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Inspections Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Pre-Flight Inspection Records</h3>
                <p className="text-sm text-gray-600 mt-1">Track and manage all pre-flight safety inspections</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Inspection Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Drone & Inspector
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Checklist Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Issues & Notes
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredInspections.map((inspection) => (
                  <tr key={inspection.id} className="hover:bg-gray-50 transition-colors">
                    {/* Inspection Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <ClipboardCheck className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{inspection.id}</h4>
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(inspection.status)}`}>
                              {getStatusIcon(inspection.status)}
                              <span className="ml-1">{inspection.status}</span>
                            </span>
                          </div>
                          <div className="flex items-center gap-3 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {inspection.date} at {inspection.time}
                            </span>
                            <span className="flex items-center gap-1">
                              <MapPin className="w-3 h-3" />
                              {inspection.location}
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {inspection.duration}
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Drone & Inspector */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{inspection.droneId}</p>
                          <p className="text-xs text-gray-600">{inspection.droneName}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                            <User className="w-3 h-3 text-blue-500" />
                          </div>
                          <p className="text-sm text-gray-900">{inspection.inspector}</p>
                        </div>
                      </div>
                    </td>

                    {/* Checklist Status */}
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Battery className="w-3 h-3 text-gray-400" />
                          <span className={`text-xs font-medium ${getChecklistItemColor(inspection.checklist.battery.status)}`}>
                            Battery: {inspection.checklist.battery.status}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Settings className="w-3 h-3 text-gray-400" />
                          <span className={`text-xs font-medium ${getChecklistItemColor(inspection.checklist.propellers.status)}`}>
                            Propellers: {inspection.checklist.propellers.status}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Camera className="w-3 h-3 text-gray-400" />
                          <span className={`text-xs font-medium ${getChecklistItemColor(inspection.checklist.camera.status)}`}>
                            Camera: {inspection.checklist.camera.status}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Wifi className="w-3 h-3 text-gray-400" />
                          <span className={`text-xs font-medium ${getChecklistItemColor(inspection.checklist.gps.status)}`}>
                            GPS: {inspection.checklist.gps.status}
                          </span>
                        </div>
                      </div>
                    </td>

                    {/* Issues & Notes */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        {inspection.issues.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Issues:</p>
                            <div className="space-y-1">
                              {inspection.issues.slice(0, 2).map((issue, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <AlertTriangle className="w-3 h-3 text-red-500 flex-shrink-0" />
                                  <span className="text-xs text-red-700">{issue}</span>
                                </div>
                              ))}
                              {inspection.issues.length > 2 && (
                                <p className="text-xs text-gray-500">+{inspection.issues.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}

                        <div className="mt-2 pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-500 mb-1">Notes:</p>
                          <p className="text-xs text-gray-700">{inspection.notes}</p>
                        </div>
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                          onClick={() => navigate(`/qc-inspections/pre-flight/${inspection.id}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <select
                          value={inspection.status}
                          onChange={(e) => handleUpdateStatus(inspection.id, e.target.value)}
                          className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          <option value="Passed">Passed</option>
                          <option value="Failed">Failed</option>
                          <option value="Conditional">Conditional</option>
                          <option value="In Progress">In Progress</option>
                        </select>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteInspection(inspection.id)}
                          title="Delete Inspection"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add Inspection Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">New Pre-Flight Inspection</h3>
                  <button
                    onClick={() => setShowAddModal(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <form onSubmit={handleAddInspection} className="p-6">
                {/* Basic Information */}
                <div className="mb-6">
                  <h4 className="text-md font-semibold text-gray-900 mb-4">Basic Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Drone ID*</label>
                      <input
                        type="text"
                        required
                        value={newInspection.droneId}
                        onChange={(e) => setNewInspection({...newInspection, droneId: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., DRN-001"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Drone Name*</label>
                      <input
                        type="text"
                        required
                        value={newInspection.droneName}
                        onChange={(e) => setNewInspection({...newInspection, droneName: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., Surveyor Alpha"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Inspector*</label>
                      <input
                        type="text"
                        required
                        value={newInspection.inspector}
                        onChange={(e) => setNewInspection({...newInspection, inspector: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., John Smith"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Date*</label>
                      <input
                        type="date"
                        required
                        value={newInspection.date}
                        onChange={(e) => setNewInspection({...newInspection, date: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Time*</label>
                      <input
                        type="time"
                        required
                        value={newInspection.time}
                        onChange={(e) => setNewInspection({...newInspection, time: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Location*</label>
                      <input
                        type="text"
                        required
                        value={newInspection.location}
                        onChange={(e) => setNewInspection({...newInspection, location: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., Hangar A"
                      />
                    </div>
                  </div>
                </div>

                {/* Inspection Checklist */}
                <div className="mb-6">
                  <h4 className="text-md font-semibold text-gray-900 mb-4">Inspection Checklist</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Battery */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <Battery className="w-5 h-5 text-blue-500" />
                        <h5 className="font-medium text-gray-900">Battery System</h5>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Status*</label>
                          <select
                            required
                            value={newInspection.checklist.battery.status}
                            onChange={(e) => setNewInspection({
                              ...newInspection,
                              checklist: {
                                ...newInspection.checklist,
                                battery: { ...newInspection.checklist.battery, status: e.target.value }
                              }
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="Pass">Pass</option>
                            <option value="Warning">Warning</option>
                            <option value="Fail">Fail</option>
                          </select>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Voltage</label>
                            <input
                              type="text"
                              value={newInspection.checklist.battery.voltage}
                              onChange={(e) => setNewInspection({
                                ...newInspection,
                                checklist: {
                                  ...newInspection.checklist,
                                  battery: { ...newInspection.checklist.battery, voltage: e.target.value }
                                }
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="25.2V"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Percentage</label>
                            <input
                              type="text"
                              value={newInspection.checklist.battery.percentage}
                              onChange={(e) => setNewInspection({
                                ...newInspection,
                                checklist: {
                                  ...newInspection.checklist,
                                  battery: { ...newInspection.checklist.battery, percentage: e.target.value }
                                }
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="98%"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Propellers */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <Settings className="w-5 h-5 text-blue-500" />
                        <h5 className="font-medium text-gray-900">Propellers</h5>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Status*</label>
                          <select
                            required
                            value={newInspection.checklist.propellers.status}
                            onChange={(e) => setNewInspection({
                              ...newInspection,
                              checklist: {
                                ...newInspection.checklist,
                                propellers: { ...newInspection.checklist.propellers, status: e.target.value }
                              }
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="Pass">Pass</option>
                            <option value="Warning">Warning</option>
                            <option value="Fail">Fail</option>
                          </select>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Condition</label>
                            <input
                              type="text"
                              value={newInspection.checklist.propellers.condition}
                              onChange={(e) => setNewInspection({
                                ...newInspection,
                                checklist: {
                                  ...newInspection.checklist,
                                  propellers: { ...newInspection.checklist.propellers, condition: e.target.value }
                                }
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Excellent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Balance</label>
                            <input
                              type="text"
                              value={newInspection.checklist.propellers.balance}
                              onChange={(e) => setNewInspection({
                                ...newInspection,
                                checklist: {
                                  ...newInspection.checklist,
                                  propellers: { ...newInspection.checklist.propellers, balance: e.target.value }
                                }
                              })}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Good"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Notes */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Inspection Notes</label>
                  <textarea
                    rows={3}
                    value={newInspection.notes}
                    onChange={(e) => setNewInspection({...newInspection, notes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Add any additional notes or observations..."
                  />
                </div>

                <div className="flex items-center justify-end gap-3 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Complete Inspection
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </QCLayout>
  );
};

export default PreFlightInspection;
