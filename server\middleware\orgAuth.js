const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Organization = require('../models/Organization');
const { verifyToken } = require('../utils/jwt');

/**
 * Simple authentication middleware for organization portal
 * This is a simplified version that works without complex role checking
 */
const authenticate = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.header('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No valid token provided.'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token - try utility function first, fallback to direct verification
    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      // Fallback to direct JWT verification without strict issuer/audience
      decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret');
    }

    // Get user ID from token payload
    const userId = decoded.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token format.'
      });
    }

    // Get user from database
    let user = await User.findById(userId).select('-password');

    // If not found in database, check if it's a test user from in-memory storage
    if (!user) {
      console.log('🔍 User not found in database, checking in-memory users...');

      // Check in-memory test users (from authController.js)
      const inMemoryUsers = [
        {
          _id: '507f1f77bcf86cd799439003',
          username: 'testorg',
          email: '<EMAIL>',
          role: 'org',
          profile: {
            organizationId: '507f1f77bcf86cd799439012',
            organizationName: 'Test Organization'
          },
          isActive: true
        }
      ];

      user = inMemoryUsers.find(u => u._id === userId || u.username === decoded.username);
    }

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token. User not found.'
      });
    }

    console.log('✅ User found:', user.username, 'Role:', user.role);

    // For organization portal, we need to ensure user has organization access
    let organizationId = null;

    // Check if user has organization in their profile
    if (user.organization) {
      organizationId = user.organization;
    } else if (user.profile?.organizationId) {
      organizationId = user.profile.organizationId;
    }

    // If no organization found, try to find one where user is associated
    if (!organizationId && (user.role === 'org' || user.userType === 'organization')) {
      try {
        const organization = await Organization.findOne({
          $or: [
            { createdBy: user._id },
            { 'contactInfo.email': user.email }
          ]
        });

        if (organization) {
          organizationId = organization._id;
        }
      } catch (dbError) {
        console.log('⚠️ Database query failed, using fallback organization ID');
        // Fallback for test users
        if (user.profile?.organizationId) {
          organizationId = user.profile.organizationId;
        }
      }
    }

    // For testing purposes, if still no organization, use a default one
    if (!organizationId && (process.env.NODE_ENV === 'development' || user.role === 'org')) {
      // Use a default organization ID for testing
      organizationId = user.profile?.organizationId || '507f1f77bcf86cd799439012';
    }

    if (!organizationId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. User is not associated with any organization.'
      });
    }

    // Add user and organization info to request
    req.user = {
      ...user.toObject(),
      organizationId,
      organization: organizationId
    };

    next();

  } catch (error) {
    console.error('❌ Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token.'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired.'
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Authentication failed.'
    });
  }
};

/**
 * Simple authorization middleware for organization users
 */
const authorize = (roles = []) => {
  return (req, res, next) => {
    try {
      // If no roles specified, just check if user is authenticated
      if (roles.length === 0) {
        return next();
      }

      // Check if user has required role
      const userRole = req.user.userType || req.user.role;
      
      if (roles.includes('org') && (userRole === 'organization' || userRole === 'org')) {
        return next();
      }

      if (roles.includes(userRole)) {
        return next();
      }

      return res.status(403).json({
        success: false,
        message: 'Access denied. Insufficient permissions.'
      });

    } catch (error) {
      console.error('❌ Authorization error:', error);
      return res.status(500).json({
        success: false,
        message: 'Authorization failed.'
      });
    }
  };
};

module.exports = {
  authenticate,
  authorize
};
