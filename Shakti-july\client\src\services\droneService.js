import api from './api';

class DroneService {
  /**
   * Create a new drone
   * @param {Object} droneData - Drone data to create
   * @returns {Promise<Object>} API response
   */
  async createDrone(droneData) {
    try {
      console.log('🔍 DroneService: Sending request to /api/drones');
      console.log('🔍 DroneService: Request data:', JSON.stringify(droneData, null, 2));

      const response = await api.post('/drones', droneData);

      console.log('🔍 DroneService: Response received:', response);

      if (response.data.success) {
        return {
          success: true,
          data: response.data.data,
          message: response.data.message
        };
      } else {
        throw new Error(response.data.message || 'Failed to create drone');
      }
    } catch (error) {
      console.error('🔍 DroneService: Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });

      // Handle different error types - same pattern as organizationService
      if (error.response?.status === 401) {
        throw new Error('Authentication failed. Please log in again.');
      } else if (error.response?.status === 403) {
        throw new Error('Access denied. You do not have permission to create drones.');
      } else if (error.response?.status === 400) {
        const errorData = error.response.data;
        let errorMessage = 'Validation failed';

        // Check if there are specific validation errors
        if (errorData.errors && Array.isArray(errorData.errors)) {
          errorMessage = errorData.errors.map(err => err.msg).join(', ');
        } else if (errorData.message) {
          errorMessage = errorData.message;
        }

        throw new Error(errorMessage);
      } else if (error.response?.status === 500) {
        const errorMessage = error.response.data.message || 'Server error occurred';
        throw new Error(`Server Error: ${errorMessage}`);
      }

      throw new Error(error.response?.data?.message || error.message || 'Failed to create drone');
    }
  }

  /**
   * Get all drones with filtering and pagination
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} API response
   */
  async getAllDrones(params = {}) {
    try {
      const response = await api.get('/drones', { params });
      
      return {
        success: true,
        data: response.data,
        message: 'Drones retrieved successfully'
      };
    } catch (error) {
      if (error.response?.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view drones');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to retrieve drones');
      }
    }
  }

  /**
   * Get drone by ID
   * @param {string} droneId - Drone ID
   * @returns {Promise<Object>} API response
   */
  async getDroneById(droneId) {
    try {
      const response = await api.get(`/drones/${droneId}`);
      
      return {
        success: true,
        data: response.data,
        message: 'Drone retrieved successfully'
      };
    } catch (error) {
      if (error.response?.status === 404) {
        throw new Error('Drone not found');
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view this drone');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to retrieve drone');
      }
    }
  }

  /**
   * Update drone
   * @param {string} droneId - Drone ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} API response
   */
  async updateDrone(droneId, updateData) {
    try {
      const response = await api.put(`/drones/${droneId}`, updateData);
      
      return {
        success: true,
        data: response.data,
        message: 'Drone updated successfully'
      };
    } catch (error) {
      if (error.response?.status === 404) {
        throw new Error('Drone not found');
      } else if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid update data provided');
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to update this drone');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to update drone');
      }
    }
  }

  /**
   * Delete drone
   * @param {string} droneId - Drone ID
   * @returns {Promise<Object>} API response
   */
  async deleteDrone(droneId) {
    try {
      const response = await api.delete(`/drones/${droneId}`);
      
      return {
        success: true,
        data: response.data,
        message: 'Drone deleted successfully'
      };
    } catch (error) {
      if (error.response?.status === 404) {
        throw new Error('Drone not found');
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to delete this drone');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to delete drone');
      }
    }
  }

  /**
   * Get drone statistics
   * @returns {Promise<Object>} API response
   */
  async getDroneStats() {
    try {
      const response = await api.get('/drones/stats');
      
      return {
        success: true,
        data: response.data,
        message: 'Drone statistics retrieved successfully'
      };
    } catch (error) {
      if (error.response?.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view drone statistics');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to retrieve drone statistics');
      }
    }
  }

  /**
   * Update drone status
   * @param {string} droneId - Drone ID
   * @param {string} status - New status
   * @returns {Promise<Object>} API response
   */
  async updateDroneStatus(droneId, status) {
    try {
      const response = await api.patch(`/drones/${droneId}/status`, { status });
      
      return {
        success: true,
        data: response.data,
        message: 'Drone status updated successfully'
      };
    } catch (error) {
      if (error.response?.status === 404) {
        throw new Error('Drone not found');
      } else if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid status provided');
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to update drone status');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to update drone status');
      }
    }
  }
}

export default new DroneService();
