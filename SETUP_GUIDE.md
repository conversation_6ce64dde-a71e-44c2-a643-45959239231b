# 🚀 SHAKTI Drone Management - Complete Setup Guide

## 📋 Prerequisites

Before starting, ensure you have the following installed:

- **Node.js** (v14 or higher) - [Download here](https://nodejs.org/)
- **MongoDB** - [Download here](https://www.mongodb.com/try/download/community) or use [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
- **Git** - [Download here](https://git-scm.com/)
- **Code Editor** (VS Code recommended)

## 🗄️ Database Setup

### Option 1: Local MongoDB Installation

1. **Install MongoDB Community Edition**
   - Windows: Download and run the installer
   - macOS: `brew install mongodb-community`
   - Linux: Follow [official guide](https://docs.mongodb.com/manual/installation/)

2. **Start MongoDB Service**
   ```bash
   # Windows (as service)
   net start MongoDB
   
   # macOS
   brew services start mongodb-community
   
   # Linux
   sudo systemctl start mongod
   ```

3. **Verify MongoDB is running**
   ```bash
   mongo --eval "db.adminCommand('ismaster')"
   ```

### Option 2: MongoDB Atlas (Cloud)

1. Create account at [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. Create a new cluster (free tier available)
3. Create database user with read/write permissions
4. Whitelist your IP address
5. Get connection string (replace `<password>` with your password)

## 🔧 Backend Setup

### 1. Navigate to Server Directory
```bash
cd Shakti-july/server
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Configure Environment Variables
```bash
# Copy environment template
cp .env.example .env
```

Edit `.env` file with your settings:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration (choose one)
# Local MongoDB:
MONGODB_URI=mongodb://localhost:27017/shakti_drone_management

# MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/shakti_drone_management

# JWT Configuration
JWT_SECRET=shakti_super_secret_jwt_key_2025_change_in_production_environment
JWT_EXPIRE=7d

# CORS Configuration
CLIENT_URL=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_SALT_ROUNDS=12
```

### 4. Seed Database with Default Users
```bash
npm run seed
```

You should see output like:
```
🌱 Starting database seeding...
✅ MongoDB Connected: localhost:27017
🗑️ Clearing existing users...
👥 Creating default users...
✅ Created user: admin (admin)
✅ Created user: salamkisan (org)
✅ Created user: maintenance (maintenance)
✅ Created user: testorg (org)
🎉 Database seeding completed successfully!
```

### 5. Start Backend Server
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

You should see:
```
🚀 SHAKTI Drone Management API Server Started
📍 Environment: development
🌐 Server running on port 5000
📊 Health check: http://localhost:5000/api/health
📚 API Base URL: http://localhost:5000/api
⏰ Started at: 2025-01-29T10:30:00.000Z
```

## 🎨 Frontend Setup

### 1. Navigate to Client Directory
```bash
cd Shakti-july/client
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Configure Environment Variables
```bash
# Copy environment template
cp .env.example .env
```

The `.env` file should contain:
```env
# API Configuration
VITE_API_URL=http://localhost:5000/api

# Development Configuration
VITE_NODE_ENV=development
```

### 4. Start Frontend Development Server
```bash
npm run dev
```

You should see:
```
  VITE v6.3.5  ready in 1234 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
```

## 🧪 Testing the Setup

### 1. Test Backend API
Open browser and visit: `http://localhost:5000/api/health`

You should see:
```json
{
  "success": true,
  "message": "SHAKTI Drone Management API is running",
  "timestamp": "2025-01-29T10:30:00.000Z",
  "version": "1.0.0",
  "environment": "development"
}
```

### 2. Test Frontend Application
Open browser and visit: `http://localhost:5173/`

You should see the SHAKTI login page with three role options.

### 3. Test Login Functionality

Use these default credentials:

| Role | Username | Password | Expected Redirect |
|------|----------|----------|-------------------|
| Admin | `admin` | `Admin123!` | `/admindashboard` |
| Organization | `salamkisan` | `Org123!` | `/orgdashboard` |
| Maintenance | `maintenance` | `Maint123!` | `/qc-dashboard` |

## 🔍 Troubleshooting

### Backend Issues

**MongoDB Connection Error**
```
❌ Database connection failed: MongoNetworkError
```
- Ensure MongoDB is running
- Check connection string in `.env`
- Verify network connectivity

**Port Already in Use**
```
Error: listen EADDRINUSE: address already in use :::5000
```
- Change PORT in `.env` file
- Kill process using port: `lsof -ti:5000 | xargs kill -9` (macOS/Linux)

**JWT Secret Error**
```
❌ Token generation failed
```
- Ensure JWT_SECRET is set in `.env`
- Use a strong, unique secret key

### Frontend Issues

**API Connection Error**
```
Network Error: Request failed with status code 500
```
- Ensure backend server is running
- Check VITE_API_URL in frontend `.env`
- Verify CORS configuration

**Login Not Working**
- Check browser console for errors
- Verify credentials match seeded users
- Check network tab for API requests

**Environment Variables Not Loading**
- Restart development server after changing `.env`
- Ensure `.env` file is in correct directory
- Check for typos in variable names

## 📱 Development Workflow

### Running Both Servers

**Option 1: Separate Terminals**
```bash
# Terminal 1 - Backend
cd Shakti-july/server
npm run dev

# Terminal 2 - Frontend
cd Shakti-july/client
npm run dev
```

**Option 2: Concurrent (from root directory)**
```bash
# Install concurrently globally
npm install -g concurrently

# Run both servers
concurrently "cd server && npm run dev" "cd client && npm run dev"
```

### Making Changes

1. **Backend Changes**: Server auto-restarts with nodemon
2. **Frontend Changes**: Browser auto-refreshes with Vite HMR
3. **Database Changes**: Re-run `npm run seed` if needed

## 🔐 Default User Accounts

After seeding, you can login with:

```
┌─────────────┬─────────────────────┬─────────────┬─────────────────────────┐
│ Role        │ Username            │ Password    │ Email                   │
├─────────────┼─────────────────────┼─────────────┼─────────────────────────┤
│ Admin       │ admin               │ Admin123!   │ <EMAIL>        │
│ Organization│ salamkisan          │ Org123!     │ <EMAIL>  │
│ Maintenance │ maintenance         │ Maint123!   │ <EMAIL>  │
│ Test Org    │ testorg             │ Test123!    │ <EMAIL>        │
└─────────────┴─────────────────────┴─────────────┴─────────────────────────┘
```

## 🎯 Next Steps

1. **Test all login roles** to ensure proper redirection
2. **Explore the dashboard** features for each role
3. **Check browser console** for any JavaScript errors
4. **Monitor server logs** for API request/response details
5. **Customize user data** by modifying `server/utils/seedDatabase.js`

## 📞 Getting Help

If you encounter issues:

1. Check the console logs (both frontend and backend)
2. Verify all environment variables are set correctly
3. Ensure all dependencies are installed
4. Check that ports 5000 and 5173 are available
5. Restart both servers after configuration changes

The setup is complete! You now have a fully functional SHAKTI Drone Management System with backend authentication.
