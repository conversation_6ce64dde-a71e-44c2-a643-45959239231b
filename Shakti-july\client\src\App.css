#root {
  max-width: 1920px;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.leaflet-left .leaflet-control-zoom {
  float: left !important;
  left: 0 !important;
  position: absolute !important;
  z-index: 1000 !important;
}

/* Fix for modal overlay issues with Leaflet maps */
.leaflet-container {
  z-index: 1 !important;
}

.leaflet-pane {
  z-index: 1 !important;
}

.leaflet-map-pane {
  z-index: 1 !important;
}

/* Ensure modal overlays are always on top */
.modal-overlay {
  z-index: 9999 !important;
}
