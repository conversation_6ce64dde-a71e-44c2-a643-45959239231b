import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Edit,
  Download,
  Printer,
  Bug,
  Zap,
  Wifi,
  Database,
  Code,
  Settings,
  Activity,
  FileText,
  Terminal,
  Layers,
  Target
} from 'lucide-react';

const ErrorLogsDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [errorLog, setErrorLog] = useState(null);
  const [loading, setLoading] = useState(true);

  // Sample detailed error log data
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const sampleData = {
        'ERR-2024-001': {
          id: 'ERR-2024-001',
          droneId: 'DRN-001',
          droneName: 'Surveyor Alpha',
          timestamp: '2024-01-15 14:32:15',
          severity: 'Critical',
          category: 'System',
          errorCode: 'SYS_001',
          title: 'Motor Controller Failure',
          description: 'Motor controller #2 has stopped responding to commands',
          location: 'Flight Mission Area B',
          flightId: 'FLT-2024-0156',
          technician: 'Auto-Detected',
          status: 'Open',
          stackTrace: `MotorController.cpp:245 - Motor::executeCommand()
DroneSystem.cpp:1023 - SystemManager::processMotorCommand()
FlightControl.cpp:567 - FlightController::updateMotorSpeeds()
MainLoop.cpp:89 - main()`,
          affectedSystems: ['Motors', 'Flight Control', 'Safety Systems'],
          resolution: '',
          resolvedBy: '',
          resolvedAt: '',
          occurrences: 1,
          firstOccurrence: '2024-01-15 14:32:15',
          lastOccurrence: '2024-01-15 14:32:15',
          detailedInfo: {
            errorType: 'Hardware Communication Failure',
            subsystem: 'Motor Control Unit',
            component: 'ESC #2 (Electronic Speed Controller)',
            firmwareVersion: 'v2.1.4',
            hardwareRevision: 'Rev C',
            operatingConditions: {
              temperature: '45°C',
              voltage: '24.8V',
              current: '12.5A',
              rpm: '0 (Expected: 5200)'
            },
            diagnosticData: {
              i2cResponse: 'No ACK received',
              pwmSignal: 'Present (1500μs)',
              powerSupply: 'Normal (24.8V)',
              thermalStatus: 'Normal'
            }
          },
          impactAnalysis: {
            flightSafety: 'Critical - Immediate landing required',
            missionImpact: 'Mission aborted',
            systemReliability: 'Compromised - Single point of failure',
            dataLoss: 'None - Emergency landing successful'
          },
          troubleshootingSteps: [
            'Verify power connections to ESC #2',
            'Check I2C communication bus integrity',
            'Test ESC with known good motor',
            'Inspect for physical damage or overheating',
            'Update ESC firmware if available',
            'Replace ESC if hardware failure confirmed'
          ],
          relatedEvents: [
            {
              timestamp: '2024-01-15 14:30:12',
              event: 'Motor #2 RPM fluctuation detected',
              severity: 'Warning'
            },
            {
              timestamp: '2024-01-15 14:31:45',
              event: 'ESC #2 temperature spike (65°C)',
              severity: 'Warning'
            },
            {
              timestamp: '2024-01-15 14:32:15',
              event: 'Motor controller failure',
              severity: 'Critical'
            }
          ],
          systemLogs: [
            '[14:30:12] WARN: Motor #2 RPM variance detected: Expected 5200, Actual 4950-5400',
            '[14:31:45] WARN: ESC #2 temperature threshold exceeded: 65°C (Max: 60°C)',
            '[14:32:10] ERROR: I2C communication timeout with ESC #2',
            '[14:32:15] CRITICAL: Motor #2 controller unresponsive - Emergency landing initiated',
            '[14:32:16] INFO: Flight mode changed to EMERGENCY_LAND',
            '[14:32:45] INFO: Emergency landing completed successfully'
          ]
        },
        'ERR-2024-002': {
          id: 'ERR-2024-002',
          droneId: 'DRN-003',
          droneName: 'Scout Beta',
          timestamp: '2024-01-15 11:45:22',
          severity: 'Warning',
          category: 'Communication',
          errorCode: 'COM_003',
          title: 'Signal Strength Degradation',
          description: 'Communication signal strength dropped below threshold during flight',
          location: 'Remote Survey Site',
          flightId: 'FLT-2024-0154',
          technician: 'Sarah Johnson',
          status: 'In Progress',
          stackTrace: `CommModule.cpp:189 - RadioLink::checkSignalStrength()
Telemetry.cpp:445 - TelemetryManager::processSignalQuality()
DataLink.cpp:223 - DataLinkManager::maintainConnection()
NetworkManager.cpp:156 - NetworkManager::monitorConnections()`,
          affectedSystems: ['Communication', 'Telemetry', 'Data Transmission'],
          resolution: 'Antenna alignment adjustment in progress',
          resolvedBy: '',
          resolvedAt: '',
          occurrences: 3,
          firstOccurrence: '2024-01-14 16:20:10',
          lastOccurrence: '2024-01-15 11:45:22',
          detailedInfo: {
            errorType: 'Signal Quality Degradation',
            subsystem: 'Radio Communication',
            component: '2.4GHz Transceiver Module',
            firmwareVersion: 'v1.8.2',
            hardwareRevision: 'Rev B',
            operatingConditions: {
              frequency: '2.437 GHz',
              power: '100mW',
              rssi: '-78 dBm (Threshold: -70 dBm)',
              snr: '12 dB'
            },
            diagnosticData: {
              antennaVSWR: '1.8:1 (Acceptable: <2:1)',
              txPower: '98mW (Expected: 100mW)',
              rxSensitivity: '-95 dBm',
              packetLoss: '2.3%'
            }
          },
          impactAnalysis: {
            flightSafety: 'Moderate - Reduced control reliability',
            missionImpact: 'Degraded telemetry quality',
            systemReliability: 'Reduced - Intermittent communication',
            dataLoss: 'Minimal - Some telemetry packets lost'
          },
          troubleshootingSteps: [
            'Check antenna orientation and alignment',
            'Verify antenna connections are secure',
            'Test in different frequency channels',
            'Inspect for electromagnetic interference',
            'Calibrate radio frequency settings',
            'Consider antenna replacement if damaged'
          ],
          relatedEvents: [
            {
              timestamp: '2024-01-14 16:20:10',
              event: 'First signal degradation detected',
              severity: 'Warning'
            },
            {
              timestamp: '2024-01-15 09:15:33',
              event: 'Signal strength improved temporarily',
              severity: 'Info'
            },
            {
              timestamp: '2024-01-15 11:45:22',
              event: 'Signal degradation recurred',
              severity: 'Warning'
            }
          ],
          systemLogs: [
            '[11:45:20] WARN: RSSI dropped to -75 dBm (Threshold: -70 dBm)',
            '[11:45:21] WARN: Packet loss increased to 1.8%',
            '[11:45:22] WARN: Signal strength below operational threshold',
            '[11:45:23] INFO: Switching to backup communication protocol',
            '[11:45:25] INFO: Connection stabilized on backup channel',
            '[11:45:30] INFO: Primary channel recovery attempted'
          ]
        }
      };

      setErrorLog(sampleData[id] || null);
      setLoading(false);
    }, 1000);
  }, [id]);

  if (loading) {
    return (
      <QCLayout title="Loading..." subtitle="Please wait">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </QCLayout>
    );
  }

  if (!errorLog) {
    return (
      <QCLayout title="Error Log Not Found" subtitle="The requested error log could not be found">
        <div className="text-center py-12">
          <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Log Not Found</h3>
          <p className="text-gray-600 mb-4">The error log with ID "{id}" could not be found.</p>
          <button
            onClick={() => navigate('/qc-diagnostics/error-logs')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Error Logs
          </button>
        </div>
      </QCLayout>
    );
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'Critical': return 'bg-red-100 text-red-700 border-red-200';
      case 'Error': return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'Warning': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Info': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'Critical': return <XCircle className="w-4 h-4" />;
      case 'Error': return <AlertCircle className="w-4 h-4" />;
      case 'Warning': return <AlertTriangle className="w-4 h-4" />;
      case 'Info': return <CheckCircle className="w-4 h-4" />;
      default: return <Bug className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Resolved': return 'bg-green-100 text-green-700 border-green-200';
      case 'In Progress': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'Open': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'System': return <Zap className="w-5 h-5" />;
      case 'Communication': return <Wifi className="w-5 h-5" />;
      case 'Storage': return <Database className="w-5 h-5" />;
      case 'Sensor': return <Settings className="w-5 h-5" />;
      case 'Battery': return <Zap className="w-5 h-5" />;
      default: return <Code className="w-5 h-5" />;
    }
  };

  return (
    <QCLayout
      title={`Error Log - ${errorLog.id}`}
      subtitle={`${errorLog.droneId} - ${errorLog.droneName}`}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => navigate('/qc-diagnostics/error-logs')}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Error Logs
          </button>
          
          <div className="flex items-center gap-3">
            <button className="px-4 py-2 text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors flex items-center gap-2">
              <Edit className="w-4 h-4" />
              Edit
            </button>
            <button className="px-4 py-2 text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export
            </button>
            <button className="px-4 py-2 text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors flex items-center gap-2">
              <Printer className="w-4 h-4" />
              Print
            </button>
          </div>
        </div>

        {/* Error Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Error Info */}
          <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-start gap-4">
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${errorLog.severity === 'Critical' ? 'bg-red-100 text-red-600' : errorLog.severity === 'Warning' ? 'bg-yellow-100 text-yellow-600' : 'bg-blue-100 text-blue-600'}`}>
                  {getCategoryIcon(errorLog.category)}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">{errorLog.title}</h3>
                  <p className="text-gray-600 mb-3">{errorLog.description}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {errorLog.timestamp}
                    </span>
                    <span className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      {errorLog.location}
                    </span>
                    <span>Flight: {errorLog.flightId}</span>
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getSeverityColor(errorLog.severity)}`}>
                  {getSeverityIcon(errorLog.severity)}
                  <span className="ml-2">{errorLog.severity}</span>
                </span>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(errorLog.status)}`}>
                  {errorLog.status}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <p className="text-xs text-gray-500 mb-1">Error Code</p>
                <p className="text-sm font-medium text-gray-900">{errorLog.errorCode}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Category</p>
                <p className="text-sm font-medium text-gray-900">{errorLog.category}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Occurrences</p>
                <p className="text-sm font-medium text-gray-900">{errorLog.occurrences}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Technician</p>
                <p className="text-sm font-medium text-gray-900">{errorLog.technician}</p>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Error Statistics</h3>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600 mb-1">First Occurrence</p>
                <p className="text-sm font-medium text-gray-900">{errorLog.firstOccurrence}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 mb-1">Last Occurrence</p>
                <p className="text-sm font-medium text-gray-900">{errorLog.lastOccurrence}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 mb-1">Affected Systems</p>
                <div className="space-y-1">
                  {errorLog.affectedSystems.map((system, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-red-400"></div>
                      <span className="text-sm text-gray-900">{system}</span>
                    </div>
                  ))}
                </div>
              </div>
              {errorLog.resolution && (
                <div>
                  <p className="text-sm text-gray-600 mb-1">Resolution</p>
                  <p className="text-sm text-gray-900">{errorLog.resolution}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Technical Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Detailed Information */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Settings className="w-5 h-5 text-blue-500" />
              Technical Details
            </h3>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-900 mb-2">Error Information</p>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Type:</span>
                    <span className="text-gray-900">{errorLog.detailedInfo.errorType}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subsystem:</span>
                    <span className="text-gray-900">{errorLog.detailedInfo.subsystem}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Component:</span>
                    <span className="text-gray-900">{errorLog.detailedInfo.component}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Firmware:</span>
                    <span className="text-gray-900">{errorLog.detailedInfo.firmwareVersion}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Hardware:</span>
                    <span className="text-gray-900">{errorLog.detailedInfo.hardwareRevision}</span>
                  </div>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-900 mb-2">Operating Conditions</p>
                <div className="space-y-2 text-sm">
                  {Object.entries(errorLog.detailedInfo.operatingConditions).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                      <span className="text-gray-900">{value}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-900 mb-2">Diagnostic Data</p>
                <div className="space-y-2 text-sm">
                  {Object.entries(errorLog.detailedInfo.diagnosticData).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                      <span className="text-gray-900">{value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Impact Analysis */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Target className="w-5 h-5 text-red-500" />
              Impact Analysis
            </h3>
            <div className="space-y-4">
              {Object.entries(errorLog.impactAnalysis).map(([category, impact]) => (
                <div key={category} className="border-l-4 border-red-400 pl-4">
                  <p className="text-sm font-medium text-gray-900 capitalize mb-1">
                    {category.replace(/([A-Z])/g, ' $1').trim()}
                  </p>
                  <p className="text-sm text-gray-700">{impact}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default ErrorLogsDetail;
