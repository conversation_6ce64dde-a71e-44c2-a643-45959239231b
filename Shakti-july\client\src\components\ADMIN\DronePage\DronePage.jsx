import React, { useState, useEffect, useMemo } from 'react';
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  MapPin,
  CheckCircle,
  Clock,
  Settings,
  AlertTriangle,
  Plane,
  Battery,
  Signal,
  RefreshCw,
  Loader,
  XCircle,
  ChevronRight,
  Calendar,
  Gauge,
  BarChart2,
  Zap,
  Radio,
  Navigation,
  Wifi,
  WifiOff,
  User
} from 'lucide-react';
import {
  FaPlane
} from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import AdminSidebar from '../common/AdminSidebar';
import droneService from '../../../services/droneService';
import { useAuth } from '../../../context/AuthContext';

const DronePage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('All');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedDrone, setSelectedDrone] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const rowsPerPage = 10;

  // Drone data from API
  const [droneList, setDroneList] = useState([]);
  const [totalDrones, setTotalDrones] = useState(0);
  const [pendingDeletions, setPendingDeletions] = useState({});

  // API Functions
  const fetchDrones = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = {
        page: currentPage,
        limit: rowsPerPage,
        sortBy: sortBy,
        sortOrder: sortOrder
      };

      // Add search filter
      if (searchTerm.trim()) {
        params.search = searchTerm.trim();
      }

      // Add status filter
      if (selectedStatus !== 'All') {
        params.status = selectedStatus.toLowerCase();
      }

      const response = await droneService.getAllDrones(params);

      if (response.success) {
        // Transform API data to match UI expectations
        const transformedDrones = response.data.data.drones.map(drone => ({
          id: drone._id,
          name: drone.model || drone.name || 'Unknown Drone',
          orgName: drone.organizationId?.name || 'No Organization',
          regDate: new Date(drone.createdAt).toISOString().split('T')[0],
          deployDate: drone.purchase?.purchaseDate ? new Date(drone.purchase.purchaseDate).toISOString().split('T')[0] : null,
          location: drone.currentLocation?.address || 'Unknown Location',
          status: drone.status ? drone.status.charAt(0).toUpperCase() + drone.status.slice(1) : 'Unknown',
          batteryLevel: drone.batteryLevel || Math.floor(Math.random() * 100), // Default random if not available
          flightHours: drone.flightStats?.totalFlightTime || 0,
          pilot: drone.assignedTo?.username || 'Unassigned',
          model: drone.model || 'Unknown Model',
          altitude: drone.currentLocation?.altitude || 0,
          speed: drone.currentLocation?.speed || 0,
          connectivity: drone.connectivity || 'Unknown',
          serialNumber: drone.serialNumber,
          manufacturer: drone.manufacturer
        }));

        setDroneList(transformedDrones);
        setTotalDrones(response.data.data.pagination?.totalCount || transformedDrones.length);
      }
    } catch (error) {
      console.error('Error fetching drones:', error);
      setError(error.message || 'Failed to fetch drones');
    } finally {
      setIsLoading(false);
    }
  };

  const deleteDroneFromAPI = async (droneId) => {
    try {
      const response = await droneService.deleteDrone(droneId);

      if (response.success) {
        // Remove drone from local state
        setDroneList(prev => prev.filter(drone => drone.id !== droneId));
        setTotalDrones(prev => prev - 1);

        // Remove from pending deletions
        setPendingDeletions(prev => {
          const copy = { ...prev };
          delete copy[droneId];
          return copy;
        });

        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting drone:', error);
      setError(error.message || 'Failed to delete drone');
      return false;
    }
  };

  // Load drones on component mount and when filters change
  useEffect(() => {
    fetchDrones();
  }, [currentPage, sortBy, sortOrder, selectedStatus, searchTerm]);

  // Force refresh when component mounts (for navigation from AddDrone)
  useEffect(() => {
    fetchDrones();
  }, []);

  // Utility functions
  const getStatusIcon = (status) => {
    switch (status) {
      case 'Active': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'Flying': return <Plane className="w-4 h-4 text-blue-600" />;
      case 'Inactive': return <Clock className="w-4 h-4 text-gray-600" />;
      case 'Maintenance': return <Settings className="w-4 h-4 text-yellow-600" />;
      case 'Crashed': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default: return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800 border-green-200';
      case 'Flying': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Inactive': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'Maintenance': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Crashed': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getBatteryColor = (level) => {
    if (level > 70) return 'text-green-600';
    if (level > 30) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConnectivityIcon = (connectivity) => {
    switch (connectivity) {
      case 'Excellent': return <Signal className="w-4 h-4 text-green-600" />;
      case 'Strong': return <Signal className="w-4 h-4 text-green-500" />;
      case 'Good': return <Signal className="w-4 h-4 text-yellow-600" />;
      case 'Weak': return <Signal className="w-4 h-4 text-orange-600" />;
      case 'None': return <Signal className="w-4 h-4 text-red-600" />;
      default: return <Signal className="w-4 h-4 text-gray-600" />;
    }
  };

  // Event handlers
  const handleDelete = async (drone) => {
    if (window.confirm(`Are you sure you want to delete drone "${drone.name}"? This action cannot be undone.`)) {
      const success = await deleteDroneFromAPI(drone.id);
      if (!success) {
        alert('Failed to delete drone. Please try again.');
      }
    }
  };

  const handleRetrieve = (droneId) => {
    setPendingDeletions(prev => {
      const copy = { ...prev };
      delete copy[droneId];
      return copy;
    });
  };

  const handleViewDrone = (drone) => {
    navigate(`/dronelogs`);
  };

  const handleEditDrone = (drone) => {
    setSelectedDrone(drone);
    setShowEditModal(true);
  };

  const handleNavigateToEdit = (droneId) => {
    navigate(`/admin/drones/edit/${droneId}`);
  };

  const handleAddDrone = () => {
    navigate("/adddrone");
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchDrones();
    setIsRefreshing(false);
  };

  const formatTimer = (ms) => {
    const d = Math.floor(ms / (1000*60*60*24));
    const h = Math.floor((ms % (1000*60*60*24)) / (1000*60*60));
    const m = Math.floor((ms % (1000*60*60)) / (1000*60));
    const s = Math.floor((ms % (1000*60)) / 1000);
    return `${d}d ${h}h ${m}m ${s}s`;
  };

  // No longer needed - direct deletion

  // Pagination logic (API handles filtering and sorting)
  const totalPages = Math.ceil(totalDrones / rowsPerPage);
  const paginatedDrones = droneList; // API already returns paginated results

  // Statistics
  const droneStats = useMemo(() => {
    const total = totalDrones; // Use total from API
    const active = droneList.filter(d => d.status === 'Active').length;
    const flying = droneList.filter(d => d.status === 'Flying').length;
    const maintenance = droneList.filter(d => d.status === 'Maintenance').length;
    const crashed = droneList.filter(d => d.status === 'Crashed').length;
    const avgBattery = droneList.length > 0 ? Math.round(droneList.reduce((sum, d) => sum + d.batteryLevel, 0) / droneList.length) : 0;
    const totalFlightHours = droneList.reduce((sum, d) => sum + d.flightHours, 0);

    return {
      total,
      active,
      flying,
      maintenance,
      crashed,
      avgBattery,
      totalFlightHours
    };
  }, [droneList, totalDrones]);

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />
      <div className="ml-[250px]">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4 text-black">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <FaPlane className="text-blue-600" />
                Drone Fleet Management
              </h2>
              <p className="text-gray-600 mt-1">
                Monitor and manage your entire drone fleet in real-time
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              <button
                onClick={handleAddDrone}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4" />
                Add New Drone
              </button>
            </div>
          </div>
        </div>

        {/* Statistics Dashboard */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-4 mb-6 text-black">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Drones</p>
                  <p className="text-2xl font-bold text-gray-900">{droneStats.total}</p>
                </div>
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FaPlane className="w-5 h-5 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active</p>
                  <p className="text-2xl font-bold text-green-600">{droneStats.active}</p>
                </div>
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Flying</p>
                  <p className="text-2xl font-bold text-blue-600">{droneStats.flying}</p>
                </div>
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Plane className="w-5 h-5 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Maintenance</p>
                  <p className="text-2xl font-bold text-yellow-600">{droneStats.maintenance}</p>
                </div>
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Settings className="w-5 h-5 text-yellow-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Issues</p>
                  <p className="text-2xl font-bold text-red-600">{droneStats.crashed}</p>
                </div>
                <div className="p-2 bg-red-100 rounded-lg">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Battery</p>
                  <p className="text-2xl font-bold text-purple-600">{droneStats.avgBattery}%</p>
                </div>
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Battery className="w-5 h-5 text-purple-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Flight Hours</p>
                  <p className="text-2xl font-bold text-indigo-600">{droneStats.totalFlightHours}</p>
                </div>
                <div className="p-2 bg-indigo-100 rounded-lg">
                  <Clock className="w-5 h-5 text-indigo-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6 text-black">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-4">
              {/* Search Bar */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search drones, organizations, pilots..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`flex items-center gap-2 px-4 py-2 border rounded-lg transition-colors ${
                    showFilters ? 'bg-blue-50 border-blue-300 text-blue-700' : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <Filter className="w-4 h-4" />
                  Filters
                </button>
              </div>
            </div>

            {/* Expandable Filters */}
            {showFilters && (
              <div className="border-t border-gray-200 pt-4 text-black">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Region</label>
                    <select
                      value={selectedRegion}
                      onChange={(e) => setSelectedRegion(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="All">All Regions</option>
                      <option value="Maharashtra">Maharashtra</option>
                      <option value="Karnataka">Karnataka</option>
                      <option value="Gujarat">Gujarat</option>
                      <option value="Punjab">Punjab</option>
                      <option value="Haryana">Haryana</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="All">All Status</option>
                      <option value="Active">Active</option>
                      <option value="Flying">Flying</option>
                      <option value="Inactive">Inactive</option>
                      <option value="Maintenance">Maintenance</option>
                      <option value="Crashed">Crashed</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="createdAt">Registration Date</option>
                      <option value="model">Drone Name</option>
                      <option value="status">Status</option>
                      <option value="manufacturer">Manufacturer</option>
                      <option value="serialNumber">Serial Number</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Order</label>
                    <select
                      value={sortOrder}
                      onChange={(e) => setSortOrder(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="desc">Descending</option>
                      <option value="asc">Ascending</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <div>
                  <h3 className="text-red-800 font-medium">Error Loading Drones</h3>
                  <p className="text-red-700 text-sm">{error}</p>
                </div>
                <button
                  onClick={handleRefresh}
                  className="ml-auto px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          )}

          {/* Results Summary */}
          <div className="flex items-center justify-between mb-4 text-black">
            <p className="text-sm text-gray-600">
              {isLoading ? 'Loading drones...' : `Showing ${paginatedDrones.length} of ${totalDrones} drones`}
            </p>
          </div>

          {/* Professional Drone Table */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden text-black text-left">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="flex items-center gap-3">
                  <Loader className="w-6 h-6 animate-spin text-blue-600" />
                  <span className="text-gray-600">Loading drones...</span>
                </div>
              </div>
            ) : paginatedDrones.length === 0 ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <FaPlane className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Drones Found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm || selectedStatus !== 'All' ? 'No drones match your current filters.' : 'No drones have been added yet.'}
                  </p>
                  <button
                    onClick={handleAddDrone}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
                  >
                    <Plus className="w-4 h-4" />
                    Add First Drone
                  </button>
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Drone Details
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Organization
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status & Health
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Performance
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Location & Pilot
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {paginatedDrones.map((drone) => (
                    <tr key={drone.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-12 w-12">
                            <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                              <FaPlane className="h-6 w-6 text-blue-600" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{drone.name}</div>
                            <div className="text-sm text-gray-500">{drone.id}</div>
                            <div className="text-xs text-gray-400">{drone.model}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-medium">{drone.orgName}</div>
                        <div className="text-sm text-gray-500">Reg: {new Date(drone.regDate).toLocaleDateString()}</div>
                        {drone.deployDate && (
                          <div className="text-xs text-gray-400">Deploy: {new Date(drone.deployDate).toLocaleDateString()}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2 mb-2">
                          {getStatusIcon(drone.status)}
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(drone.status)}`}>
                            {drone.status}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Battery className={`w-4 h-4 ${getBatteryColor(drone.batteryLevel)}`} />
                          <span className={`text-sm font-medium ${getBatteryColor(drone.batteryLevel)}`}>
                            {drone.batteryLevel}%
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{drone.flightHours}h flight time</div>
                        <div className="text-sm text-gray-500">Alt: {drone.altitude}m</div>
                        <div className="text-xs text-gray-400">Speed: {drone.speed} km/h</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-1 mb-1">
                          <MapPin className="w-3 h-3 text-gray-400" />
                          <span className="text-sm text-gray-900">{drone.location}</span>
                        </div>
                        <div className="text-sm text-gray-500">{drone.pilot}</div>
                        <div className="flex items-center gap-1">
                          {getConnectivityIcon(drone.connectivity)}
                          <span className="text-xs text-gray-400">{drone.connectivity}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex items-center justify-center gap-2">
                          <button
                            onClick={() => handleViewDrone(drone)}
                            className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors"
                            title="View Details"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleEditDrone(drone)}
                            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-md transition-colors"
                            title="Edit"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(drone)}
                            className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
                            title="Delete"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-6 text-black">
            <div className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Previous
              </button>
              {[...Array(Math.min(5, totalPages))].map((_, idx) => {
                const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + idx;
                return (
                  <button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`px-3 py-2 border rounded-lg transition-colors ${
                      currentPage === pageNum
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Next
              </button>
            </div>
          </div>
        </div>

        {/* Edit Drone Modal */}
        {showEditModal && selectedDrone && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              {/* Modal Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div className="flex items-center gap-4">
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Plane className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">{selectedDrone.name}</h2>
                    <p className="text-gray-600">ID: {selectedDrone.id}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleNavigateToEdit(selectedDrone.id)}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    Edit Drone
                  </button>
                  <button
                    onClick={() => setShowEditModal(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <XCircle className="w-6 h-6 text-gray-500" />
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Left Column - Basic Info & Status */}
                  <div className="space-y-6">
                    {/* Basic Information */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                        <Settings className="w-5 h-5 text-blue-600" />
                        Basic Information
                      </h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">Model</p>
                          <p className="font-medium text-gray-900">{selectedDrone.model}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Registration Date</p>
                          <p className="font-medium text-gray-900">{new Date(selectedDrone.regDate).toLocaleDateString()}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Organization</p>
                          <p className="font-medium text-gray-900">{selectedDrone.orgName}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Deployment Date</p>
                          <p className="font-medium text-gray-900">
                            {selectedDrone.deployDate ? new Date(selectedDrone.deployDate).toLocaleDateString() : 'Not deployed'}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Current Status */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                        <Gauge className="w-5 h-5 text-blue-600" />
                        Current Status
                      </h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">Status</p>
                          <div className="flex items-center gap-2 mt-1">
                            {getStatusIcon(selectedDrone.status)}
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedDrone.status)}`}>
                              {selectedDrone.status}
                            </span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Battery Level</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Battery className={`w-4 h-4 ${getBatteryColor(selectedDrone.batteryLevel)}`} />
                            <span className={`font-medium ${getBatteryColor(selectedDrone.batteryLevel)}`}>
                              {selectedDrone.batteryLevel}%
                            </span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Connection</p>
                          <div className="flex items-center gap-2 mt-1">
                            {selectedDrone.connectivity === 'Connected' ? (
                              <Wifi className="w-4 h-4 text-green-600" />
                            ) : (
                              <WifiOff className="w-4 h-4 text-red-600" />
                            )}
                            <span className="font-medium">{selectedDrone.connectivity}</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Signal Strength</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Signal className={`w-4 h-4 ${
                              selectedDrone.signalStrength > 70 ? 'text-green-600' :
                              selectedDrone.signalStrength > 30 ? 'text-yellow-600' : 'text-red-600'
                            }`} />
                            <span className="font-medium">{selectedDrone.signalStrength}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Performance & Location */}
                  <div className="space-y-6">
                    {/* Performance Metrics */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                        <BarChart2 className="w-5 h-5 text-blue-600" />
                        Performance Metrics
                      </h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">Flight Hours</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Clock className="w-4 h-4 text-gray-400" />
                            <span className="font-medium text-gray-900">{selectedDrone.flightHours}h</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Current Speed</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Zap className="w-4 h-4 text-gray-400" />
                            <span className="font-medium text-gray-900">{selectedDrone.speed} km/h</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Altitude</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Navigation className="w-4 h-4 text-gray-400" />
                            <span className="font-medium text-gray-900">{selectedDrone.altitude}m</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Signal Quality</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Radio className="w-4 h-4 text-gray-400" />
                            <span className="font-medium text-gray-900">{selectedDrone.signalQuality || 'Good'}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Location & Assignment */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                        <MapPin className="w-5 h-5 text-blue-600" />
                        Location & Assignment
                      </h3>
                      <div className="grid grid-cols-1 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">Current Location</p>
                          <div className="flex items-center gap-2 mt-1">
                            <MapPin className="w-4 h-4 text-gray-400" />
                            <span className="font-medium text-gray-900">{selectedDrone.location || 'Unknown'}</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Assigned Pilot</p>
                          <div className="flex items-center gap-2 mt-1">
                            <User className="w-4 h-4 text-gray-400" />
                            <span className="font-medium text-gray-900">{selectedDrone.pilot || 'Unassigned'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end gap-4 mt-6">
                  <button
                    onClick={() => setShowEditModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Close
                  </button>
                  <button
                    onClick={() => navigate(`/admin/drones/edit/${selectedDrone.id}`)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Edit Drone
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DronePage;
