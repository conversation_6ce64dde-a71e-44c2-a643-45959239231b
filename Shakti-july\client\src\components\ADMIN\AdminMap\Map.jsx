import { useState, useEffect } from 'react';
import MapView from './MapView';

import {
  Bell,
  Search,
  RefreshCw,
  Loader2,
  Filter,
  MapPin,
  Building,
  BarChart3
} from 'lucide-react';

import AdminSidebar from '../common/AdminSidebar';
import mapDataService from './MapDataService';
import { MapLoadingSpinner, MapErrorDisplay, SearchLoadingIndicator } from './MapLoadingComponents';

const Map = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalOrganizations: 0,
    activeOrganizations: 0,
    totalDrones: 0,
    activeDrones: 0
  });
  const [filters, setFilters] = useState({
    state: '',
    status: 'all'
  });

  // Initialize component
  useEffect(() => {
    document.body.style.overflow = "auto";
    document.body.style.display = "block";
    document.body.style.justifyContent = "unset";
    document.body.style.alignItems = "unset";
    document.body.style.height = "auto";
    document.body.style.background = "#f5f5f5";

    loadInitialData();

    // Subscribe to organization updates
    const unsubscribe = mapDataService.subscribe('organizations', () => {
      updateStats();
    });

    return () => unsubscribe();
  }, []);

  // Handle search with debounce
  useEffect(() => {
    if (searchQuery) {
      setIsSearching(true);
      const timer = setTimeout(() => {
        setIsSearching(false);
      }, 800);
      return () => clearTimeout(timer);
    }
  }, [searchQuery]);

  const loadInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await mapDataService.fetchOrganizations();
      updateStats();
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const updateStats = () => {
    const currentStats = mapDataService.getOrganizationStats();
    setStats(currentStats);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await mapDataService.fetchOrganizations(filters);
      updateStats();
    } catch (err) {
      setError(err.message);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  if (error) {
    return (
      <div className="min-h-screen w-full bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9]">
        <AdminSidebar />
        <div className="lg:pl-[250px] w-full h-screen">
          <MapErrorDisplay
            title="Failed to load map data"
            message={error}
            onRetry={loadInitialData}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9]">
      {/* Sidebar */}
      <AdminSidebar />

      {/* Main content - Full width with proper spacing */}
      <div className="lg:pl-[250px] w-full min-h-screen transition-all duration-300">
        {/* Header - Full width */}
        <div className="flex flex-col lg:flex-row items-center justify-between px-4 sm:px-6 py-4 bg-gradient-to-r from-[#91d0f5] to-[#7ab9e3] shadow-md gap-4 mt-0 lg:mt-0 w-full">
          <div className="flex flex-col sm:flex-row items-center gap-4 w-full lg:w-auto mt-8 lg:mt-0">
            <div className="relative w-full sm:w-80">
              <Search className="absolute left-3 top-2.5 text-gray-600" size={18} />
              <input
                type="text"
                placeholder="Search organizations, states, or IDs..."
                className="pl-10 pr-4 py-2 rounded-full border-none w-full focus:outline-none focus:ring-2 focus:ring-blue-300 transition-all text-black"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {isSearching && <SearchLoadingIndicator />}
            </div>

            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-black" />
              <select
                className="px-3 py-2 rounded-lg border-none text-sm focus:outline-none focus:ring-2 focus:ring-blue-300 text-black bg-white"
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
              </select>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="flex items-center justify-center bg-white text-blue-600 p-2 rounded-full shadow hover:bg-blue-50 transition-all disabled:opacity-50"
              title="Refresh data"
            >
              {isRefreshing ? <Loader2 size={18} className="animate-spin" /> : <RefreshCw size={18} />}
            </button>
            <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center text-blue-600 shadow hover:bg-blue-50 cursor-pointer transition-all">
              <Bell size={20} />
            </div>
          </div>
        </div>

        {/* Stats Section - Full width */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 px-4 sm:px-6 py-4 w-full">
          <StatCard
            title="Total Organizations"
            value={stats.totalOrganizations}
            icon={<Building className="w-5 h-5 text-blue-600" />}
            color="blue"
          />
          <StatCard
            title="Active Organizations"
            value={stats.activeOrganizations}
            icon={<Building className="w-5 h-5 text-green-600" />}
            color="green"
          />
          <StatCard
            title="Total Drones"
            value={stats.totalDrones}
            icon={<MapPin className="w-5 h-5 text-purple-600" />}
            color="purple"
          />
          <StatCard
            title="Active Drones"
            value={stats.activeDrones}
            icon={<BarChart3 className="w-5 h-5 text-amber-600" />}
            color="amber"
          />
        </div>

        {/* MapView - Full width and height */}
        <div className="px-4 sm:px-6 pb-6 w-full">
          <div className="w-full h-[calc(100vh-280px)] overflow-hidden rounded-xl shadow-lg">
            {isLoading ? (
              <MapLoadingSpinner text="Loading organizations and map data..." />
            ) : (
              <MapView searchQuery={searchQuery} filters={filters} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Stats Card Component
const StatCard = ({ title, value, icon, color }) => {
  const colorClasses = {
    blue: 'bg-gradient-to-br from-blue-50 to-blue-100 border-l-4 border-blue-500',
    green: 'bg-gradient-to-br from-green-50 to-green-100 border-l-4 border-green-500',
    purple: 'bg-gradient-to-br from-purple-50 to-purple-100 border-l-4 border-purple-500',
    amber: 'bg-gradient-to-br from-amber-50 to-amber-100 border-l-4 border-amber-500'
  };

  return (
    <div className={`${colorClasses[color]} p-3 sm:p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 w-full`}>
      <div className="flex justify-between items-start">
        <div>
          <h4 className="text-xs sm:text-sm font-medium text-black mb-1">{title}</h4>
          <span className="text-lg sm:text-xl md:text-2xl font-bold text-black">{value}</span>
        </div>
        <div className="p-1.5 sm:p-2 rounded-lg bg-white shadow-sm">
          {icon}
        </div>
      </div>
    </div>
  );
};

export default Map;
