const express = require('express');
const { body, param, query } = require('express-validator');
const router = express.Router();

// Import middleware
const orgAuth = require('../middleware/orgAuth');
const validation = require('../middleware/validation');

// Import controllers
const orgPortalController = require('../controllers/organizationPortalController');

// Validation schemas
const addDroneValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Drone name must be between 2 and 100 characters'),

  body('model')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Model must be between 2 and 100 characters'),

  body('manufacturer')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Manufacturer must be between 2 and 100 characters'),

  body('serialNumber')
    .trim()
    .isLength({ min: 5, max: 50 })
    .withMessage('Serial number must be between 5 and 50 characters'),

  body('registrationNumber')
    .trim()
    .isLength({ min: 5, max: 50 })
    .withMessage('Registration number must be between 5 and 50 characters'),

  body('specifications.type')
    .optional()
    .isIn(['quadcopter', 'hexacopter', 'octocopter', 'fixed-wing', 'hybrid', 'other'])
    .withMessage('Invalid drone type'),

  body('specifications.weight')
    .optional()
    .isFloat({ min: 0.1, max: 25 })
    .withMessage('Weight must be between 0.1 and 25 kg'),

  body('specifications.maxPayload')
    .optional()
    .isFloat({ min: 0, max: 10 })
    .withMessage('Max payload must be between 0 and 10 kg'),

  body('specifications.maxFlightTime')
    .optional()
    .isInt({ min: 1, max: 300 })
    .withMessage('Max flight time must be between 1 and 300 minutes'),

  body('specifications.maxRange')
    .optional()
    .isFloat({ min: 0.1, max: 100 })
    .withMessage('Max range must be between 0.1 and 100 km'),

  body('specifications.maxAltitude')
    .optional()
    .isInt({ min: 1, max: 500 })
    .withMessage('Max altitude must be between 1 and 500 meters'),

  body('specifications.maxSpeed')
    .optional()
    .isInt({ min: 1, max: 200 })
    .withMessage('Max speed must be between 1 and 200 km/h'),

  body('specifications.batteryCapacity')
    .optional()
    .isInt({ min: 100, max: 50000 })
    .withMessage('Battery capacity must be between 100 and 50000 mAh'),

  body('purchase.purchaseDate')
    .isISO8601()
    .withMessage('Purchase date must be a valid date'),

  body('purchase.purchasePrice')
    .isFloat({ min: 0 })
    .withMessage('Purchase price must be a positive number'),

  body('purchase.vendor')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Vendor name must be between 2 and 100 characters')
];

const updateDroneValidation = [
  param('droneId')
    .isMongoId()
    .withMessage('Invalid drone ID'),

  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Drone name must be between 2 and 100 characters'),

  body('status')
    .optional()
    .isIn(['active', 'inactive', 'maintenance', 'retired', 'lost'])
    .withMessage('Invalid status')
];

const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('status')
    .optional()
    .isIn(['active', 'inactive', 'maintenance', 'retired', 'lost'])
    .withMessage('Invalid status filter')
];

// Routes

// GET /api/org-portal/dashboard - Get organization dashboard data
router.get('/dashboard',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  orgPortalController.getDashboardData
);

// GET /api/org-portal/drones - Get organization's drones
router.get('/drones',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  queryValidation,
  validation.handleValidationErrors,
  orgPortalController.getDrones
);

// GET /api/org-portal/drones/:droneId - Get specific drone
router.get('/drones/:droneId',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  param('droneId').isMongoId().withMessage('Invalid drone ID'),
  validation.handleValidationErrors,
  orgPortalController.getDroneById
);

// POST /api/org-portal/drones - Add new drone
router.post('/drones',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  // Simplified validation - only check required fields
  body('name').trim().isLength({ min: 1 }).withMessage('Drone name is required'),
  body('model').trim().isLength({ min: 1 }).withMessage('Model is required'),
  body('manufacturer').trim().isLength({ min: 1 }).withMessage('Manufacturer is required'),
  validation.handleValidationErrors,
  orgPortalController.addDrone
);

// PUT /api/org-portal/drones/:droneId - Update drone
router.put('/drones/:droneId',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  updateDroneValidation,
  validation.handleValidationErrors,
  orgPortalController.updateDrone
);

// DELETE /api/org-portal/drones/:droneId - Remove drone
router.delete('/drones/:droneId',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  param('droneId').isMongoId().withMessage('Invalid drone ID'),
  validation.handleValidationErrors,
  orgPortalController.removeDrone
);

// GET /api/org-portal/statistics - Get organization statistics
router.get('/statistics',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  orgPortalController.getStatistics
);

// GET /api/org-portal/analytics - Get organization analytics
router.get('/analytics',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  orgPortalController.getAnalytics
);

// POST /api/org-portal/drones/:droneId/notes - Add note to drone
router.post('/drones/:droneId/notes',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  param('droneId').isMongoId().withMessage('Invalid drone ID'),
  body('content').trim().isLength({ min: 1, max: 1000 }).withMessage('Note content is required and cannot exceed 1000 characters'),
  body('type').optional().isIn(['general', 'maintenance', 'incident', 'upgrade']).withMessage('Invalid note type'),
  validation.handleValidationErrors,
  orgPortalController.addDroneNote
);

// PATCH /api/org-portal/drones/:droneId/status - Update drone status
router.patch('/drones/:droneId/status',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  param('droneId').isMongoId().withMessage('Invalid drone ID'),
  body('status').isIn(['active', 'inactive', 'maintenance', 'retired', 'lost']).withMessage('Invalid status'),
  validation.handleValidationErrors,
  orgPortalController.updateDroneStatus
);

// GET /api/org-portal/map-data - Get map data for organization
router.get('/map-data',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  orgPortalController.getMapData
);

// POST /api/org-portal/initialize - Initialize organization data (called once when org first logs in)
router.post('/initialize',
  orgAuth.authenticate,
  orgAuth.authorize(['org']),
  orgPortalController.initializeOrganizationData
);

// GET /api/org-portal/test - Test endpoint
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Organization Portal API is working!',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
