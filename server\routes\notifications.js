const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');

// Import controllers
const notificationController = require('../controllers/notificationController');

// Import middleware
const auth = require('../middleware/auth');
const validation = require('../middleware/validation');

// Validation middleware
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('category')
    .optional()
    .isIn(['all', 'admin', 'operations', 'maintenance', 'safety', 'system', 'user_management'])
    .withMessage('Invalid category'),
  query('priority')
    .optional()
    .isIn(['all', 'low', 'medium', 'high', 'critical'])
    .withMessage('Invalid priority'),
  query('isRead')
    .optional()
    .isBoolean()
    .withMessage('isRead must be a boolean'),
  query('isArchived')
    .optional()
    .isBoolean()
    .withMessage('isArchived must be a boolean'),
  query('status')
    .optional()
    .isIn(['all', 'read', 'unread', 'archived'])
    .withMessage('Invalid status'),
  query('dateRange')
    .optional()
    .isIn(['all', 'today', 'yesterday', 'week', 'month'])
    .withMessage('Invalid date range'),
  query('organization')
    .optional()
    .isString()
    .withMessage('Organization must be a string'),
  query('type')
    .optional()
    .isIn([
      'all',
      'organization_created',
      'organization_updated', 
      'organization_deleted',
      'organization_restored',
      'drone_created',
      'drone_updated',
      'drone_deleted',
      'drone_status_changed',
      'individual_created',
      'individual_updated',
      'individual_deleted',
      'user_created',
      'user_updated',
      'system_alert',
      'maintenance_due',
      'battery_low',
      'mission_completed',
      'geofence_breach',
      'weather_alert'
    ])
    .withMessage('Invalid notification type'),
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'priority', 'type', 'category'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  query('search')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters')
];

const bulkActionValidation = [
  body('notificationIds')
    .isArray({ min: 1 })
    .withMessage('notificationIds must be a non-empty array'),
  body('notificationIds.*')
    .isMongoId()
    .withMessage('Each notification ID must be a valid MongoDB ObjectId')
];

// Routes

// GET /api/notifications - Get all notifications with filtering and pagination
router.get('/',
  auth.authenticate,
  auth.authorize(['admin']),
  queryValidation,
  validation.handleValidationErrors,
  notificationController.getAllNotifications
);

// GET /api/notifications/stats - Get notification statistics
router.get('/stats',
  auth.authenticate,
  auth.authorize(['admin']),
  notificationController.getNotificationStats
);

// PUT /api/notifications/:id/read - Mark notification as read
router.put('/:id/read',
  auth.authenticate,
  auth.authorize(['admin']),
  param('id').isMongoId().withMessage('Invalid notification ID'),
  validation.handleValidationErrors,
  notificationController.markAsRead
);

// PUT /api/notifications/read-all - Mark all notifications as read
router.put('/read-all',
  auth.authenticate,
  auth.authorize(['admin']),
  notificationController.markAllAsRead
);

// PUT /api/notifications/:id/archive - Archive notification
router.put('/:id/archive',
  auth.authenticate,
  auth.authorize(['admin']),
  param('id').isMongoId().withMessage('Invalid notification ID'),
  validation.handleValidationErrors,
  notificationController.archiveNotification
);

// DELETE /api/notifications/:id - Delete notification
router.delete('/:id',
  auth.authenticate,
  auth.authorize(['admin']),
  param('id').isMongoId().withMessage('Invalid notification ID'),
  validation.handleValidationErrors,
  notificationController.deleteNotification
);

// POST /api/notifications/bulk/read - Bulk mark as read
router.post('/bulk/read',
  auth.authenticate,
  auth.authorize(['admin']),
  bulkActionValidation,
  validation.handleValidationErrors,
  notificationController.bulkMarkAsRead
);

// POST /api/notifications/bulk/archive - Bulk archive
router.post('/bulk/archive',
  auth.authenticate,
  auth.authorize(['admin']),
  bulkActionValidation,
  validation.handleValidationErrors,
  notificationController.bulkArchive
);

// POST /api/notifications/bulk/delete - Bulk delete
router.post('/bulk/delete',
  auth.authenticate,
  auth.authorize(['admin']),
  bulkActionValidation,
  validation.handleValidationErrors,
  notificationController.bulkDelete
);

module.exports = router;
