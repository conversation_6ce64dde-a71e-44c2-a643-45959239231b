require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Organization = require('../models/Organization');
const Individual = require('../models/Individual');
const Drone = require('../models/Drone');

// Sample Organizations
const sampleOrganizations = [
  {
    name: '<PERSON><PERSON>',
    displayName: 'Salam <PERSON> Agricultural Services',
    description: 'Leading agricultural drone services provider',
    type: 'private',
    contact: {
      primaryEmail: '<EMAIL>',
      phone: '+************',
      website: 'https://salamkisan.com'
    },
    address: {
      street: '123 Agricultural Complex, Sector 15',
      city: 'New Delhi',
      state: 'Delhi',
      country: 'India',
      postalCode: '110001'
    },
    registration: {
      registrationNumber: 'REG-SALAM-001',
      registrationDate: new Date('2023-01-15'),
      taxId: 'GSTIN123456789',
      licenseNumber: 'LIC-AGRI-001'
    },
    primaryContact: {
      name: '<PERSON><PERSON>',
      designation: 'CEO',
      email: 'r<PERSON><PERSON>@salamkisan.com',
      phone: '+************'
    },
    status: 'active',
    isVerified: true
  },
  {
    name: 'Test Organization',
    displayName: 'Test Organization Ltd.',
    description: 'Test organization for development',
    type: 'government',
    contact: {
      primaryEmail: '<EMAIL>',
      phone: '+************'
    },
    address: {
      street: '456 Government Complex',
      city: 'Mumbai',
      state: 'Maharashtra',
      country: 'India',
      postalCode: '400001'
    },
    registration: {
      registrationNumber: 'REG-TEST-002',
      registrationDate: new Date('2023-02-20'),
      taxId: 'GSTIN987654321'
    },
    primaryContact: {
      name: 'Priya Sharma',
      designation: 'Director',
      email: '<EMAIL>',
      phone: '+************'
    },
    status: 'active',
    isVerified: true
  }
];

// Sample Individuals
const sampleIndividuals = [
  {
    fullName: 'John Doe',
    gender: 'male',
    dateOfBirth: new Date('1990-01-15'),
    contact: {
      primaryEmail: '<EMAIL>',
      phone: '9876543210',
      alternativePhone: '8765432109'
    },
    address: {
      street: '123 Main Street, Sector 15',
      city: 'New Delhi',
      state: 'Delhi',
      country: 'India',
      postalCode: '110001'
    },
    documents: {
      panNumber: '**********',
      aadharNumber: '123456789012',
      idProofPath: 'id-proof-john.pdf',
      kycDocumentPath: 'kyc-john.pdf'
    },
    status: 'approved',
    isVerified: true
  },
  {
    fullName: 'Jane Smith',
    gender: 'female',
    dateOfBirth: new Date('1985-03-20'),
    contact: {
      primaryEmail: '<EMAIL>',
      phone: '9876543211'
    },
    address: {
      street: '456 Oak Avenue, Block B',
      city: 'Mumbai',
      state: 'Maharashtra',
      country: 'India',
      postalCode: '400001'
    },
    documents: {
      panNumber: '**********',
      aadharNumber: '************',
      idProofPath: 'id-proof-jane.pdf'
    },
    status: 'pending'
  }
];

// Enhanced Users with proper organization references
const enhancedUsers = [
  {
    username: 'admin',
    email: '<EMAIL>',
    password: 'Admin123!',
    role: 'admin',
    profile: {
      adminLevel: 'super'
    }
  },
  {
    username: 'salamkisan',
    email: '<EMAIL>',
    password: 'Org123!',
    role: 'org',
    profile: {
      organizationName: 'Salam Kisan'
    }
  },
  {
    username: 'testorg',
    email: '<EMAIL>',
    password: 'Test123!',
    role: 'org',
    profile: {
      organizationName: 'Test Organization'
    }
  },
  {
    username: 'maintenance',
    email: '<EMAIL>',
    password: 'Maint123!',
    role: 'maintenance',
    profile: {
      department: 'Quality Control',
      certifications: ['Drone Maintenance', 'Quality Assurance']
    }
  }
];

const setupLocalDatabase = async () => {
  try {
    console.log('🌱 Setting up local MongoDB database...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await User.deleteMany({});
    await Organization.deleteMany({});
    await Individual.deleteMany({});
    await Drone.deleteMany({});
    console.log('✅ Existing data cleared');

    // Create admin user first
    console.log('👤 Creating admin user...');
    const adminUser = new User({
      ...enhancedUsers[0],
      password: await bcrypt.hash(enhancedUsers[0].password, 12)
    });
    await adminUser.save();
    console.log('✅ Admin user created');

    // Create organizations
    console.log('🏢 Creating organizations...');
    const createdOrgs = [];
    for (const orgData of sampleOrganizations) {
      const org = new Organization({
        ...orgData,
        createdBy: adminUser._id,
        lastModifiedBy: adminUser._id
      });
      await org.save();
      createdOrgs.push(org);
      console.log(`✅ Created organization: ${org.name}`);
    }

    // Create organization users with proper references
    console.log('👥 Creating organization users...');
    for (let i = 1; i < enhancedUsers.length - 1; i++) { // Skip admin and maintenance
      const userData = enhancedUsers[i];
      const org = createdOrgs.find(o => o.name === userData.profile.organizationName);
      
      if (org) {
        const user = new User({
          ...userData,
          password: await bcrypt.hash(userData.password, 12),
          profile: {
            ...userData.profile,
            organizationId: org._id
          }
        });
        await user.save();
        console.log(`✅ Created user: ${user.username} for ${org.name}`);
      }
    }

    // Create maintenance user
    console.log('🔧 Creating maintenance user...');
    const maintenanceUser = new User({
      ...enhancedUsers[enhancedUsers.length - 1],
      password: await bcrypt.hash(enhancedUsers[enhancedUsers.length - 1].password, 12)
    });
    await maintenanceUser.save();
    console.log('✅ Maintenance user created');

    // Create individuals
    console.log('👤 Creating individuals...');
    for (const individualData of sampleIndividuals) {
      const individual = new Individual({
        ...individualData,
        createdBy: adminUser._id,
        lastModifiedBy: adminUser._id,
        verifiedBy: individualData.status === 'approved' ? adminUser._id : undefined,
        verificationDate: individualData.status === 'approved' ? new Date() : undefined
      });
      await individual.save();
      console.log(`✅ Created individual: ${individual.fullName}`);
    }

    console.log('\n🎉 Local database setup complete!');
    console.log('\n📋 Available Login Credentials:');
    console.log('================================');
    console.log('👑 Admin Login:');
    console.log('   Username: admin');
    console.log('   Password: Admin123!');
    console.log('\n🏢 Organization Logins:');
    console.log('   Username: salamkisan');
    console.log('   Password: Org123!');
    console.log('   Username: testorg');
    console.log('   Password: Test123!');
    console.log('\n🔧 Maintenance Login:');
    console.log('   Username: maintenance');
    console.log('   Password: Maint123!');
    console.log('\n📊 Database Contents:');
    console.log(`   Organizations: ${createdOrgs.length}`);
    console.log(`   Individuals: ${sampleIndividuals.length}`);
    console.log(`   Users: ${enhancedUsers.length}`);
    console.log('\n🚀 Ready to use! Start your server with: npm run dev');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔒 Database connection closed');
  }
};

// Run the setup
setupLocalDatabase();
