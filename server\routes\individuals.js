const express = require('express');
const { body, param, validationResult } = require('express-validator');
const router = express.Router();
// const individualController = require('../controllers/individualController');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { sendSuccess, sendError } = require('../utils/response');

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return sendValidationError(res, errors.array());
  }
  next();
};

// Validation rules for creating individual
const createIndividualValidation = [
  body('fullName')
    .notEmpty()
    .withMessage('Full name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters'),

  body('gender')
    .notEmpty()
    .withMessage('Gender is required')
    .isIn(['male', 'female', 'other'])
    .withMessage('Invalid gender'),

  body('dateOfBirth')
    .isISO8601()
    .withMessage('Invalid date of birth format')
    .custom((value) => {
      const today = new Date();
      const birthDate = new Date(value);
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 18 || age > 100) {
        throw new Error('Age must be between 18 and 100 years');
      }
      return true;
    }),

  body('contact.primaryEmail')
    .isEmail()
    .withMessage('Please enter a valid email address')
    .normalizeEmail(),

  body('contact.phone')
    .matches(/^[6-9]\d{9}$/)
    .withMessage('Please enter a valid 10-digit mobile number'),

  body('contact.alternativePhone')
    .optional()
    .matches(/^[6-9]\d{9}$/)
    .withMessage('Please enter a valid 10-digit mobile number'),

  body('address.street')
    .notEmpty()
    .withMessage('Address is required')
    .isLength({ max: 200 })
    .withMessage('Address cannot exceed 200 characters'),

  body('address.city')
    .notEmpty()
    .withMessage('City is required')
    .isLength({ max: 50 })
    .withMessage('City cannot exceed 50 characters'),

  body('address.state')
    .notEmpty()
    .withMessage('State is required')
    .isLength({ max: 50 })
    .withMessage('State cannot exceed 50 characters'),

  body('address.postalCode')
    .matches(/^\d{6}$/)
    .withMessage('Please enter a valid 6-digit postal code'),

  body('documents.panNumber')
    .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/)
    .withMessage('Please enter a valid PAN number (e.g., **********)'),

  body('documents.aadharNumber')
    .matches(/^\d{12}$/)
    .withMessage('Please enter a valid 12-digit Aadhar number'),

  body('documents.idProofPath')
    .notEmpty()
    .withMessage('ID proof document is required'),

  body('status')
    .optional()
    .isIn(['pending', 'approved', 'rejected', 'suspended'])
    .withMessage('Invalid status')
];

// Validation rules for updating individual
const updateIndividualValidation = [
  param('id')
    .isMongoId()
    .withMessage('Invalid individual ID format'),

  body('fullName')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters'),

  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Invalid gender'),

  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Invalid date of birth format'),

  body('contact.primaryEmail')
    .optional()
    .isEmail()
    .withMessage('Please enter a valid email address')
    .normalizeEmail(),

  body('contact.phone')
    .optional()
    .matches(/^[6-9]\d{9}$/)
    .withMessage('Please enter a valid 10-digit mobile number'),

  body('status')
    .optional()
    .isIn(['pending', 'approved', 'rejected', 'suspended'])
    .withMessage('Invalid status')
];

// Temporary simple routes for testing
router.get('/', authenticateToken, requireRole(['admin']), (req, res) => {
  return sendSuccess(res, { individuals: [], pagination: { totalCount: 0 } }, 'Individuals retrieved successfully');
});

router.get('/stats/overview', authenticateToken, requireRole(['admin']), (req, res) => {
  return sendSuccess(res, {
    totalIndividuals: 0,
    pendingIndividuals: 0,
    approvedIndividuals: 0,
    rejectedIndividuals: 0,
    suspendedIndividuals: 0,
    verifiedIndividuals: 0
  }, 'Individual statistics retrieved successfully');
});

router.post('/', authenticateToken, requireRole(['admin']), (req, res) => {
  // Simple success response for now
  const mockIndividual = {
    _id: '507f1f77bcf86cd799439011',
    fullName: req.body.fullName,
    status: 'pending',
    createdAt: new Date().toISOString()
  };

  return sendSuccess(res, { individual: mockIndividual }, 'Individual created successfully', 201);
});

module.exports = router;
