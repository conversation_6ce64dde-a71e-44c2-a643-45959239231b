import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom"; // <-- Import useNavigate
import "./login.css";
import logo from '../assets/logo.png';
import { useAuth } from "../context/AuthContext"; // ✅ Import useAuth
import authService from "../services/authService"; // Import auth service
import { FaEye, FaEyeSlash } from "react-icons/fa"; //  Added icons

const Login = () => {
  const [showForm, setShowForm] = useState(false);
  const [cursorStyle, setCursorStyle] = useState({ display: "none" });
  const [loginType, setLoginType] = useState(""); // <-- Track who clicked login
  const [formData, setFormData] = useState({
    username: "",
    password: ""
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false); // 👁️ NEW STATE
  const navigate = useNavigate(); // <-- Hook from React Router
  const { login } = useAuth(); // ✅ Correctly call useAuth() inside the component

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError("");
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!formData.username.trim() || !formData.password.trim()) {
      setError("Please enter both username and password");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Call backend API for login
      const response = await authService.login(
        formData.username.trim(),
        formData.password,
        loginType // Pass role for validation
      );

      if (response.success) {
        // Update auth context with user data
        login(response.user);

        // Navigate based on user role
        const userRole = response.user.role;
        if (userRole === "admin") {
          navigate("/admindashboard");
        } else if (userRole === "org") {
          navigate("/orgdashboard");
        } else if (userRole === "maintenance") {
          navigate("/qc-dashboard");
        } else {
          navigate("/"); // Fallback
        }
      }
    } catch (error) {
      console.error("Login error:", error);
      setError(error.message || "Login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const cursor = document.getElementById("cursor");
    const container = document.querySelector(".container");

    const handleMove = (e) => {
      const rect = container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      setCursorStyle({
        display: "block",
        left: `${x}px`,
        top: `${y}px`
      });
    };

    const leave = () => setCursorStyle({ display: "none" });

    container.addEventListener("mousemove", handleMove);
    container.addEventListener("mouseleave", leave);

    return () => {
      container.removeEventListener("mousemove", handleMove);
      container.removeEventListener("mouseleave", leave);
    };
  }, [showForm]);

  return (
    <div className="login-body">
      <div className="container">
        <div className="custom-cursor" id="cursor" style={cursorStyle}></div>
        <img src={logo} alt="Logo" className="logo" />
        <h1 className="title">S.H.A.K.T.I</h1>
        <p className="subtitle">Safety High Accuracy Aerial Kinematic Tracking Integration</p>

        {!showForm && (
          <div className="buttons" id="buttonSection">
            <button onClick={() => { setShowForm(true); setLoginType("admin"); }}>Admin Login</button>
            <button onClick={() => { setShowForm(true); setLoginType("org"); }}>Organization Login</button>
            <button onClick={() => { setShowForm(true); setLoginType("maintenance"); }}>Maintenance Login</button>
          </div>
        )}

        {showForm && (
          <div className="auth-form" id="authForm">
            <h2>Sign In as {loginType === 'admin' ? 'Admin' : loginType === 'org' ? 'Organization' : 'Maintenance'}</h2>

            {error && (
              <div className="error-message" style={{
                color: '#ff4444',
                backgroundColor: '#ffebee',
                padding: '10px',
                borderRadius: '5px',
                marginBottom: '15px',
                fontSize: '14px',
                textAlign: 'center'
              }}>
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <input
                type="text"
                name="username"
                placeholder="Username or Email"
                value={formData.username}
                onChange={handleInputChange}
                disabled={loading}
                required
              />

              {/* Password field with eye toggle */}
              <div style={{ position: "relative" }}>
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  placeholder="Password"
                  value={formData.password}
                  onChange={handleInputChange}
                  disabled={loading}
                  required
                  style={{ width: "100%", paddingRight: "40px" }}
                />
                <span
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: "absolute",
                    right: "10px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    cursor: "pointer",
                    color: "#555"
                  }}
                >
                  {showPassword ? <FaEyeSlash size={18} /> : <FaEye size={18} />}
                </span>
              </div>

              <button
                type="submit"
                className="styled-btn"
                disabled={loading}
                style={{
                  opacity: loading ? 0.7 : 1,
                  cursor: loading ? 'not-allowed' : 'pointer'
                }}
              >
                {loading ? 'Signing In...' : 'Submit'}
              </button>
            </form>

            <button
              type="button"
              onClick={() => {
                setShowForm(false);
                setError("");
                setFormData({ username: "", password: "" });
              }}
              style={{
                background: 'none',
                border: 'none',
                color: '#666',
                textDecoration: 'underline',
                cursor: 'pointer',
                marginTop: '10px',
                fontSize: '14px'
              }}
            >
              Back to role selection
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Login;
