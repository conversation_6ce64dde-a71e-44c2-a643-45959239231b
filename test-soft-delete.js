// Test script to demonstrate soft delete functionality
const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

// Test credentials (admin user)
const testCredentials = {
  username: 'admin',
  password: 'admin123'
};

let authToken = '';

async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, testCredentials);
    authToken = response.data.data.token;
    console.log('✅ Login successful');
    return true;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.message || error.message);
    return false;
  }
}

async function getOrganizations() {
  try {
    const response = await axios.get(`${API_BASE}/organizations`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    return response.data.data.organizations;
  } catch (error) {
    console.error('❌ Failed to get organizations:', error.response?.data?.message || error.message);
    return [];
  }
}

async function softDeleteOrganization(orgId) {
  try {
    const response = await axios.delete(`${API_BASE}/organizations/${orgId}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log(`✅ Organization ${orgId} soft deleted successfully`);
    return response.data.data;
  } catch (error) {
    console.error('❌ Failed to delete organization:', error.response?.data?.message || error.message);
    return null;
  }
}

async function restoreOrganization(orgId) {
  try {
    const response = await axios.post(`${API_BASE}/organizations/${orgId}/restore`, {}, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log(`✅ Organization ${orgId} restored successfully`);
    return response.data.data;
  } catch (error) {
    console.error('❌ Failed to restore organization:', error.response?.data?.message || error.message);
    return null;
  }
}

async function testSoftDeleteSystem() {
  console.log('🧪 Testing Soft Delete System for Organizations\n');

  // Step 1: Login
  console.log('1. Logging in as admin...');
  const loginSuccess = await login();
  if (!loginSuccess) return;

  // Step 2: Get current organizations
  console.log('\n2. Getting current organizations...');
  const organizations = await getOrganizations();
  console.log(`📊 Found ${organizations.length} organizations`);

  // Find an active organization to test with
  const activeOrg = organizations.find(org => !org.isDeleted);
  if (!activeOrg) {
    console.log('❌ No active organizations found to test with');
    return;
  }

  console.log(`🎯 Testing with organization: ${activeOrg.name} (ID: ${activeOrg._id})`);

  // Step 3: Soft delete the organization
  console.log('\n3. Performing soft delete...');
  const deleteResult = await softDeleteOrganization(activeOrg._id);
  if (!deleteResult) return;

  // Step 4: Verify organization is marked as deleted
  console.log('\n4. Verifying soft delete...');
  const updatedOrgs = await getOrganizations();
  const deletedOrg = updatedOrgs.find(org => org._id === activeOrg._id);
  
  if (deletedOrg && deletedOrg.isDeleted) {
    console.log(`✅ Organization is marked as deleted`);
    console.log(`⏰ Days until permanent delete: ${deletedOrg.daysUntilPermanentDelete}`);
    console.log(`📅 Deleted at: ${deletedOrg.deletedAt}`);
    console.log(`🗑️ Permanent delete at: ${deletedOrg.permanentDeleteAt}`);
  } else {
    console.log('❌ Organization was not properly marked as deleted');
    return;
  }

  // Step 5: Restore the organization
  console.log('\n5. Restoring organization...');
  const restoreResult = await restoreOrganization(activeOrg._id);
  if (!restoreResult) return;

  // Step 6: Verify organization is restored
  console.log('\n6. Verifying restore...');
  const finalOrgs = await getOrganizations();
  const restoredOrg = finalOrgs.find(org => org._id === activeOrg._id);
  
  if (restoredOrg && !restoredOrg.isDeleted) {
    console.log(`✅ Organization successfully restored`);
    console.log(`📊 Status: ${restoredOrg.isVerified ? 'Approved' : 'Pending'}`);
  } else {
    console.log('❌ Organization was not properly restored');
    return;
  }

  console.log('\n🎉 Soft Delete System Test Completed Successfully!');
  console.log('\n📋 Summary:');
  console.log('   ✅ Soft organization as deleted with 10-day grace period');
  console.log('   ✅  organizations remain visible to admin with timer');
  console.log('   ✅ Restore functionality works properly');
  console.log('   ✅ All functionality preserved in same table view');
}

// Run the test
testSoftDeleteSystem().catch(console.error);
