import React, { useState } from 'react';
import {
  Alert<PERSON>riangle,
  CheckCircle2,
  Clock,
  Wifi,
  Battery,
  Wrench,
  Cloud,
  MapPin,
  Download,
  Eye,
  Archive,
  Trash2,
  MoreVertical,
  Check,
  X,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

const NotificationList = ({
  notifications,
  onNotificationClick,
  onMarkAsRead,
  onArchive,
  onDelete,
  onBulkAction,
  isLoading = false,
  currentPage = 1,
  totalPages = 1,
  totalCount = 0,
  pageSize = 20,
  onPageChange
}) => {
  const [selectedNotifications, setSelectedNotifications] = useState(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Icon mapping
  const iconMap = {
    AlertTriangle: AlertTriangle,
    CheckCircle2: CheckCircle2,
    Clock: Clock,
    Wifi: Wifi,
    Battery: Battery,
    Wrench: Wrench,
    Cloud: Cloud,
    MapPin: MapPin,
    Download: Download
  };

  // Color mapping
  const colorMap = {
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      text: 'text-red-600',
      dot: 'bg-red-500'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      text: 'text-orange-600',
      dot: 'bg-orange-500'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-600',
      dot: 'bg-yellow-500'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      text: 'text-green-600',
      dot: 'bg-green-500'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      text: 'text-blue-600',
      dot: 'bg-blue-500'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      text: 'text-purple-600',
      dot: 'bg-purple-500'
    }
  };

  // Use server-side pagination - no need for client-side slicing
  const currentNotifications = notifications;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalCount);

  // Selection handlers
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedNotifications(new Set(currentNotifications.map(n => n.id)));
    } else {
      setSelectedNotifications(new Set());
    }
  };

  const handleSelectNotification = (notificationId, checked) => {
    const newSelected = new Set(selectedNotifications);
    if (checked) {
      newSelected.add(notificationId);
    } else {
      newSelected.delete(notificationId);
    }
    setSelectedNotifications(newSelected);
  };

  // Bulk actions
  const handleBulkMarkAsRead = () => {
    onBulkAction('markAsRead', Array.from(selectedNotifications));
    setSelectedNotifications(new Set());
    setShowBulkActions(false);
  };

  const handleBulkArchive = () => {
    onBulkAction('archive', Array.from(selectedNotifications));
    setSelectedNotifications(new Set());
    setShowBulkActions(false);
  };

  const handleBulkDelete = () => {
    onBulkAction('delete', Array.from(selectedNotifications));
    setSelectedNotifications(new Set());
    setShowBulkActions(false);
  };

  // Format time
  const formatTime = (timestamp) => {
    const now = new Date();
    const date = new Date(timestamp); // Ensure timestamp is a Date object
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return date.toLocaleDateString();
  };

  // Get priority badge
  const getPriorityBadge = (priority) => {
    const badges = {
      critical: { label: 'Critical', color: 'bg-red-100 text-red-800' },
      high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
      medium: { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      low: { label: 'Low', color: 'bg-green-100 text-green-800' }
    };
    
    const badge = badges[priority] || badges.low;
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${badge.color}`}>
        {badge.label}
      </span>
    );
  };

  if (isLoading) {
    return <NotificationListSkeleton />;
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      {/* Header with bulk actions */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                checked={selectedNotifications.size === currentNotifications.length && currentNotifications.length > 0}
                onChange={(e) => handleSelectAll(e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm text-gray-600">
                {selectedNotifications.size > 0 ? (
                  `${selectedNotifications.size} selected`
                ) : (
                  `${notifications.length} notifications`
                )}
              </span>
            </div>
            
            {selectedNotifications.size > 0 && (
              <div className="flex items-center gap-2">
                <button
                  onClick={handleBulkMarkAsRead}
                  className="flex items-center gap-2 px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <Check className="w-4 h-4" />
                  Mark as Read
                </button>
                <button
                  onClick={handleBulkArchive}
                  className="flex items-center gap-2 px-3 py-1 text-sm bg-gray-50 text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Archive className="w-4 h-4" />
                  Archive
                </button>
                <button
                  onClick={handleBulkDelete}
                  className="flex items-center gap-2 px-3 py-1 text-sm bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  Delete
                </button>
              </div>
            )}
          </div>
          
          <div className="text-sm text-gray-500">
            Page {currentPage} of {totalPages}
          </div>
        </div>
      </div>

      {/* Notification List */}
      <div className="divide-y divide-gray-200">
        {currentNotifications.length === 0 ? (
          <div className="p-12 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle2 className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
            <p className="text-gray-500">Try adjusting your filters or check back later.</p>
          </div>
        ) : (
          currentNotifications.map((notification) => {
            const IconComponent = iconMap[notification.icon] || AlertTriangle;
            const colors = colorMap[notification.color] || colorMap.blue;
            
            return (
              <div
                key={notification._id || notification.id}
                className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
                  !notification.isRead ? 'bg-blue-50/30' : ''
                }`}
                onClick={() => onNotificationClick(notification)}
              >
                <div className="flex items-start gap-4">
                  {/* Selection checkbox */}
                  <input
                    type="checkbox"
                    checked={selectedNotifications.has(notification._id || notification.id)}
                    onChange={(e) => {
                      e.stopPropagation();
                      handleSelectNotification(notification._id || notification.id, e.target.checked);
                    }}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1"
                  />

                  {/* Icon */}
                  <div className={`w-10 h-10 ${colors.bg} ${colors.border} border rounded-lg flex items-center justify-center flex-shrink-0`}>
                    <IconComponent className={`w-5 h-5 ${colors.text}`} />
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className={`text-sm font-medium ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'} truncate`}>
                            {notification.title}
                          </h4>
                          {!notification.isRead && (
                            <div className={`w-2 h-2 ${colors.dot} rounded-full flex-shrink-0`}></div>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                          {notification.message}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>{notification.relatedEntity?.entityName || 'System'}</span>
                          <span>•</span>
                          <span>{notification.category}</span>
                          <span>•</span>
                          <span>{notification.triggeredBy?.username || 'System'}</span>
                          <span>•</span>
                          <span>{formatTime(notification.createdAt || notification.timestamp)}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 flex-shrink-0">
                        {getPriorityBadge(notification.priority)}
                        <div className="relative">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              // Handle individual actions menu
                            }}
                            className="p-1 hover:bg-gray-100 rounded transition-colors"
                          >
                            <MoreVertical className="w-4 h-4 text-gray-400" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && onPageChange && (
        <div className="p-4 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing {startIndex + 1} to {endIndex} of {totalCount} notifications
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => onPageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            <span className="px-3 py-2 text-sm">
              {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Loading skeleton
const NotificationListSkeleton = () => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
      </div>
      <div className="divide-y divide-gray-200">
        {[...Array(5)].map((_, index) => (
          <div key={index} className="p-4">
            <div className="flex items-start gap-4">
              <div className="w-4 h-4 bg-gray-200 rounded animate-pulse mt-1"></div>
              <div className="w-10 h-10 bg-gray-200 rounded-lg animate-pulse"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-full mb-1 animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3 mb-2 animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse"></div>
              </div>
              <div className="h-6 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default NotificationList;
