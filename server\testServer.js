require('dotenv').config();
const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:5173',
  credentials: true
}));
app.use(express.json());

// Simple authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret');
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({
      success: false,
      message: 'Invalid or expired token'
    });
  }
};

// In-memory storage for testing
let drones = [];
let droneIdCounter = 1;

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Login endpoint for testing
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Simple test credentials
  if (email === '<EMAIL>' && password === 'admin123') {
    const token = jwt.sign(
      { 
        _id: 'test-admin-id',
        email: '<EMAIL>',
        role: 'admin',
        profile: {
          organizationId: 'test-org-id'
        }
      },
      process.env.JWT_SECRET || 'default_secret',
      { expiresIn: '7d' }
    );
    
    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token,
        user: {
          _id: 'test-admin-id',
          email: '<EMAIL>',
          role: 'admin',
          profile: {
            organizationId: 'test-org-id'
          }
        }
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Create drone endpoint
app.post('/api/drones', authenticateToken, (req, res) => {
  try {
    console.log('📥 Received drone creation request:', JSON.stringify(req.body, null, 2));
    
    const droneData = req.body;
    
    // Basic validation
    const requiredFields = ['name', 'serialNumber', 'registrationNumber', 'model', 'manufacturer'];
    const missingFields = requiredFields.filter(field => !droneData[field]);
    
    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Missing required fields: ${missingFields.join(', ')}`
      });
    }
    
    // Check for duplicate serial number
    const existingDrone = drones.find(drone => drone.serialNumber === droneData.serialNumber);
    if (existingDrone) {
      return res.status(409).json({
        success: false,
        message: 'Drone with this serial number already exists'
      });
    }
    
    // Create new drone
    const newDrone = {
      _id: `drone-${droneIdCounter++}`,
      ...droneData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: req.user._id
    };
    
    drones.push(newDrone);
    
    console.log('✅ Drone created successfully:', newDrone._id);
    
    res.status(201).json({
      success: true,
      message: 'Drone created successfully',
      data: newDrone
    });
    
  } catch (error) {
    console.error('❌ Error creating drone:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get all drones endpoint
app.get('/api/drones', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Drones retrieved successfully',
    data: drones,
    total: drones.length
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Test server running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
  console.log('🔑 Test credentials: <EMAIL> / admin123');
});
