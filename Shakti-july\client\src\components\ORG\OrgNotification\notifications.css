/* Professional Notification Page Styling */

/* Prevent horizontal overflow */
* {
  box-sizing: border-box;
}

html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

.notification-page-container {
  max-width: 100vw;
  overflow-x: hidden;
}

/* Responsive container fixes */
.notification-page-main {
  max-width: 100%;
  overflow-x: hidden;
}

.notification-page-header {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* Notification Card Animations */
.notification-card {
  animation: slideInUp 0.3s ease-out;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.notification-list-item {
  animation: slideInRight 0.3s ease-out;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-list-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

/* Priority Indicators */
.priority-critical {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.priority-high {
  border-left: 4px solid #f97316;
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.priority-medium {
  border-left: 4px solid #eab308;
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
}

.priority-low {
  border-left: 4px solid #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

/* Unread notification indicator */
.notification-unread {
  position: relative;
}

.notification-unread::before {
  content: '';
  position: absolute;
  top: 12px;
  right: 12px;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Icon animations */
.notification-icon {
  transition: all 0.3s ease;
}

.notification-card:hover .notification-icon {
  transform: scale(1.1);
}

/* Button hover effects */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 10px 20px -5px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: white;
  border: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-1px);
  box-shadow: 0 10px 20px -5px rgba(239, 68, 68, 0.4);
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Stats card animations */
.stats-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  border: 1px solid #e5e7eb;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.stats-card:hover .stats-icon {
  transform: scale(1.2) rotate(5deg);
}

.stats-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Filter animations */
.filter-section {
  animation: slideInUp 0.4s ease-out;
  background: white;
  border: 1px solid #e5e7eb;
}

.filter-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-button:hover {
  transform: scale(1.05);
}

.filter-button.active {
  animation: bounce 0.6s ease-out;
}

/* Notification components white background */
.notification-stats-container,
.notification-filters-container,
.notification-actions-container {
  background: white !important;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Ensure all notification elements have white backgrounds */
.notification-card,
.notification-list-item,
.notification-card > *,
.notification-list-item > * {
  background: white !important;
}

/* Override any dark backgrounds */
[class*="notification"],
[class*="notification"] *,
.notification-page-container *,
.notification-page-container [class*="bg-"],
.notification-page-container [class*="bg-gray"],
.notification-page-container [class*="bg-slate"] {
  background: white !important;
}

/* Ensure notification page has light background */
.notification-page-container {
  background: #f9fafb !important;
}

/* Force all notification cards to be white */
div[class*="notification-card"],
div[class*="notification-list-item"],
.notification-card,
.notification-list-item {
  background-color: white !important;
  background: white !important;
}

/* Target specific notification card elements */
.notification-page-container .notification-card,
.notification-page-container .notification-list-item,
.notification-page-container div[class*="bg-"],
.notification-page-container div[class*="rounded"] {
  background: white !important;
  background-color: white !important;
}

/* Override Tailwind classes that might be causing dark backgrounds */
.bg-gray-800,
.bg-gray-900,
.bg-slate-800,
.bg-slate-900,
.bg-gray-700,
.bg-slate-700 {
  background: white !important;
  background-color: white !important;
}

/* Comprehensive white background enforcement */
.notification-card,
.notification-list-item {
  background: white !important;
  background-color: white !important;
}

/* Force white background on all children */
.notification-card *,
.notification-list-item * {
  background: transparent !important;
  background-color: transparent !important;
}

/* Target all possible notification elements */
[class*="notification-card"],
[class*="notification-list-item"],
div[class*="notification-card"],
div[class*="notification-list-item"] {
  background: white !important;
  background-color: white !important;
}

/* Override any Tailwind utility classes */
.notification-page-container [class*="bg-gray"],
.notification-page-container [class*="bg-slate"],
.notification-page-container .bg-gray-800,
.notification-page-container .bg-gray-900,
.notification-page-container .bg-slate-800,
.notification-page-container .bg-slate-900 {
  background: white !important;
  background-color: white !important;
}

/* Search input enhancements */
.search-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-input:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Notification type specific styling */
.notification-emergency {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.notification-emergency:hover {
  animation: shake 0.5s ease-in-out;
}

.notification-warning {
  border-left: 4px solid #f97316;
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.notification-success {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.notification-info {
  border-left: 4px solid #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
  .notification-card:hover,
  .notification-list-item:hover {
    transform: none;
  }
  
  .btn-primary:hover,
  .btn-secondary:hover,
  .btn-danger:hover {
    transform: none;
  }

  .notification-page-main {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  .notification-page-header {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .notification-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }
}

/* Tablet specific fixes */
@media (min-width: 769px) and (max-width: 1024px) {
  .notification-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Large screen optimizations */
@media (min-width: 1025px) {
  .notification-page-main {
    margin-left: 18rem; /* 72 in Tailwind = 18rem */
  }
}

/* Custom scrollbar */
.notification-scroll::-webkit-scrollbar {
  width: 6px;
}

.notification-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.notification-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 3px;
}

.notification-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

/* Accessibility improvements */
.notification-card:focus,
.notification-list-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Force white backgrounds for all notification components */
.notification-card,
.notification-list-item,
.notification-card *,
.notification-list-item * {
  background: white !important;
  border-color: #e5e7eb !important;
  color: #111827 !important;
}

/* Specific overrides for notification elements */
.notification-card > div,
.notification-list-item > div,
.notification-card .p-4,
.notification-list-item .p-4 {
  background: white !important;
}

/* Dark mode support disabled - keeping white theme */
@media (prefers-color-scheme: dark) {
  .notification-page-container {
    background: #f9fafb;
  }

  .notification-card,
  .notification-list-item {
    background: white !important;
    border-color: #e5e7eb !important;
    color: #111827 !important;
  }
}

/* FINAL OVERRIDE - This should force all notification elements to be white */
.notification-page-container .notification-card,
.notification-page-container .notification-list-item {
  background-color: white !important;
}

.notification-page-container {
  background: #f9fafb !important;
}

/* Specific targeting for notification cards */
.notification-page-container .notification-card,
.notification-page-container .notification-list-item,
.notification-page-container div[class*="notification"],
.notification-page-container div[class*="rounded"],
.notification-page-container div[class*="shadow"] {
  background: white !important;
  background-color: white !important;
  color: #000000 !important;
}

/* Ensure all text is visible with proper colors */
.notification-page-container,
.notification-page-container *,
.notification-card,
.notification-card *,
.notification-list-item,
.notification-list-item *,
.notification-stats-container,
.notification-stats-container *,
.notification-filters-container,
.notification-filters-container *,
.notification-actions-container,
.notification-actions-container * {
  color: #000000 !important;
}

/* Restore icon colors */
.text-blue-600,
.text-blue-500 {
  color: #2563eb !important;
}

.text-green-600,
.text-green-500 {
  color: #16a34a !important;
}

.text-red-600,
.text-red-500 {
  color: #dc2626 !important;
}

.text-yellow-600,
.text-yellow-500 {
  color: #ca8a04 !important;
}

.text-purple-600,
.text-purple-500 {
  color: #9333ea !important;
}

.text-orange-600,
.text-orange-500 {
  color: #ea580c !important;
}

.text-gray-600 {
  color: #4b5563 !important;
}

.text-gray-700 {
  color: #374151 !important;
}

.text-gray-500 {
  color: #6b7280 !important;
}

.text-gray-900 {
  color: #111827 !important;
}

/* Override any light text colors */
.text-white,
.text-gray-100,
.text-gray-200 {
  color: #000000 !important;
}

/* Restore background colors for stat cards */
.bg-blue-100 {
  background-color: #dbeafe !important;
}

.bg-green-100 {
  background-color: #dcfce7 !important;
}

.bg-red-100 {
  background-color: #fee2e2 !important;
}

.bg-yellow-100 {
  background-color: #fef3c7 !important;
}

.bg-purple-100 {
  background-color: #f3e8ff !important;
}

.bg-orange-100 {
  background-color: #fed7aa !important;
}

.bg-gray-50 {
  background-color: #f9fafb !important;
}

.bg-gray-100 {
  background-color: #f3f4f6 !important;
}

/* Restore border colors */
.border-blue-200 {
  border-color: #bfdbfe !important;
}

.border-gray-200 {
  border-color: #e5e7eb !important;
}

.border-gray-100 {
  border-color: #f3f4f6 !important;
}

/* Restore progress bar colors */
.bg-blue-500 {
  background-color: #3b82f6 !important;
}

.bg-green-500 {
  background-color: #22c55e !important;
}

.bg-red-500 {
  background-color: #ef4444 !important;
}

.bg-yellow-500 {
  background-color: #eab308 !important;
}

.bg-purple-500 {
  background-color: #a855f7 !important;
}

.bg-orange-500 {
  background-color: #f97316 !important;
}

/* Notification priority border colors */
.border-l-red-500 {
  border-left-color: #ef4444 !important;
}

.border-l-yellow-500 {
  border-left-color: #eab308 !important;
}

.border-l-blue-500 {
  border-left-color: #3b82f6 !important;
}

.border-l-green-500 {
  border-left-color: #22c55e !important;
}

/* Status badge colors */
.bg-red-50 {
  background-color: #fef2f2 !important;
}

.bg-yellow-50 {
  background-color: #fffbeb !important;
}

.bg-blue-50 {
  background-color: #eff6ff !important;
}

.bg-green-50 {
  background-color: #f0fdf4 !important;
}

/* Hover effects with colors */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb !important;
}

.hover\:bg-blue-50:hover {
  background-color: #eff6ff !important;
}

.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Button colors */
.bg-blue-600 {
  background-color: #2563eb !important;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8 !important;
}

/* Ring colors for focus states */
.ring-blue-500 {
  --tw-ring-color: #3b82f6 !important;
}

/* Force remove any grayscale or color filters */
.notification-page-container,
.notification-page-container *,
.notification-stats-container,
.notification-stats-container *,
.notification-card,
.notification-card *,
.notification-list-item,
.notification-list-item * {
  filter: none !important;
  -webkit-filter: none !important;
  -moz-filter: none !important;
  -ms-filter: none !important;
  -o-filter: none !important;
}

/* Ensure proper color inheritance */
.notification-page-container {
  color-scheme: normal !important;
}

/* Force icon colors to be visible */
svg {
  filter: none !important;
}

/* Specific icon color overrides */
.lucide {
  filter: none !important;
}

/* Progress bar colors */
.w-full.bg-gray-200 {
  background-color: #e5e7eb !important;
}

/* Ensure all colored elements maintain their colors */
[class*="text-blue"] {
  color: #3b82f6 !important;
}

[class*="text-green"] {
  color: #22c55e !important;
}

[class*="text-red"] {
  color: #ef4444 !important;
}

[class*="text-yellow"] {
  color: #eab308 !important;
}

[class*="text-purple"] {
  color: #a855f7 !important;
}

[class*="text-orange"] {
  color: #f97316 !important;
}
