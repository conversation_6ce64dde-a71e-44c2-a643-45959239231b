import React, { useState } from 'react';
import QCLayout from '../common/QCLayout';
import {
  Package,
  Plus,
  Search,
  Filter,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Edit,
  Eye,
  ShoppingCart,
  Trash2,
  BarChart3,
  Calendar,
  DollarSign,
  Truck,
  MapPin,
  Clock
} from 'lucide-react';

const PartsInventory = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [partsInventory, setPartsInventory] = useState([]);

  // Initialize with sample parts inventory data
  React.useEffect(() => {
    if (partsInventory.length === 0) {
      setPartsInventory([
    {
      id: 'PART-001',
      name: 'Battery Pack LiPo 6S',
      category: 'Power Systems',
      sku: 'BAT-6S-5000',
      description: '6S LiPo battery pack, 5000mAh capacity for extended flight time',
      currentStock: 8,
      minStock: 5,
      maxStock: 20,
      unitCost: 125.00,
      totalValue: 1000.00,
      supplier: 'PowerTech Solutions',
      location: 'Warehouse A - Shelf 3',
      lastRestocked: '2024-01-10',
      nextReorder: '2024-02-15',
      status: 'In Stock',
      usageRate: 'High',
      compatibleDrones: ['DRN-001', 'DRN-003', 'DRN-005'],
      leadTime: '7 days',
      warranty: '12 months'
    },
    {
      id: 'PART-002',
      name: 'Carbon Fiber Propeller Set',
      category: 'Propulsion',
      sku: 'PROP-CF-1045',
      description: '10x4.5 carbon fiber propellers, balanced set of 4',
      currentStock: 3,
      minStock: 8,
      maxStock: 24,
      unitCost: 45.00,
      totalValue: 135.00,
      supplier: 'AeroComponents Ltd',
      location: 'Warehouse B - Shelf 1',
      lastRestocked: '2023-12-20',
      nextReorder: '2024-01-20',
      status: 'Low Stock',
      usageRate: 'Medium',
      compatibleDrones: ['DRN-002', 'DRN-004'],
      leadTime: '14 days',
      warranty: '6 months'
    },
    {
      id: 'PART-003',
      name: 'Gimbal Camera Assembly',
      category: 'Imaging',
      sku: 'CAM-GIM-4K',
      description: '4K camera with 3-axis gimbal stabilization',
      currentStock: 2,
      minStock: 2,
      maxStock: 6,
      unitCost: 850.00,
      totalValue: 1700.00,
      supplier: 'VisionTech Systems',
      location: 'Secure Storage - Cabinet 2',
      lastRestocked: '2024-01-05',
      nextReorder: '2024-03-01',
      status: 'In Stock',
      usageRate: 'Low',
      compatibleDrones: ['DRN-005'],
      leadTime: '21 days',
      warranty: '24 months'
    },
    {
      id: 'PART-004',
      name: 'Flight Controller Board',
      category: 'Electronics',
      sku: 'FC-ADV-V2',
      description: 'Advanced flight controller with GPS and IMU',
      currentStock: 0,
      minStock: 3,
      maxStock: 10,
      unitCost: 320.00,
      totalValue: 0.00,
      supplier: 'FlightSystems Pro',
      location: 'Warehouse A - Shelf 5',
      lastRestocked: '2023-11-15',
      nextReorder: '2024-01-16',
      status: 'Out of Stock',
      usageRate: 'Low',
      compatibleDrones: ['DRN-001', 'DRN-002', 'DRN-003'],
      leadTime: '10 days',
      warranty: '18 months'
    },
    {
      id: 'PART-005',
      name: 'Motor Bearing Set',
      category: 'Propulsion',
      sku: 'BEAR-SET-2212',
      description: 'High-precision bearings for 2212 motors',
      currentStock: 15,
      minStock: 10,
      maxStock: 30,
      unitCost: 25.00,
      totalValue: 375.00,
      supplier: 'Precision Parts Co',
      location: 'Warehouse B - Shelf 3',
      lastRestocked: '2024-01-12',
      nextReorder: '2024-04-12',
      status: 'In Stock',
      usageRate: 'Medium',
      compatibleDrones: ['DRN-001', 'DRN-002', 'DRN-003', 'DRN-004'],
      leadTime: '5 days',
      warranty: '12 months'
    },
    {
      id: 'PART-006',
      name: 'Landing Gear Assembly',
      category: 'Mechanical',
      sku: 'GEAR-LAND-ADJ',
      description: 'Adjustable landing gear with shock absorption',
      currentStock: 6,
      minStock: 4,
      maxStock: 12,
      unitCost: 180.00,
      totalValue: 1080.00,
      supplier: 'MechTech Industries',
      location: 'Warehouse A - Shelf 2',
      lastRestocked: '2024-01-08',
      nextReorder: '2024-03-08',
      status: 'In Stock',
      usageRate: 'Low',
      compatibleDrones: ['DRN-003', 'DRN-005'],
      leadTime: '12 days',
      warranty: '18 months'
    }
      ]);
    }
  }, [partsInventory.length]);

  // Form state for adding new part
  const [newPart, setNewPart] = useState({
    name: '',
    category: 'Power Systems',
    sku: '',
    description: '',
    currentStock: '',
    minStock: '',
    maxStock: '',
    unitCost: '',
    supplier: '',
    location: '',
    leadTime: '',
    warranty: '',
    usageRate: 'Medium'
  });

  // Add new part function
  const handleAddPart = (e) => {
    e.preventDefault();
    const newPartData = {
      id: `PART-${String(partsInventory.length + 1).padStart(3, '0')}`,
      ...newPart,
      currentStock: parseInt(newPart.currentStock),
      minStock: parseInt(newPart.minStock),
      maxStock: parseInt(newPart.maxStock),
      unitCost: parseFloat(newPart.unitCost),
      totalValue: parseInt(newPart.currentStock) * parseFloat(newPart.unitCost),
      lastRestocked: new Date().toISOString().split('T')[0],
      nextReorder: '',
      status: parseInt(newPart.currentStock) === 0 ? 'Out of Stock' :
              parseInt(newPart.currentStock) <= parseInt(newPart.minStock) ? 'Low Stock' : 'In Stock',
      compatibleDrones: []
    };
    setPartsInventory([...partsInventory, newPartData]);
    setNewPart({
      name: '',
      category: 'Power Systems',
      sku: '',
      description: '',
      currentStock: '',
      minStock: '',
      maxStock: '',
      unitCost: '',
      supplier: '',
      location: '',
      leadTime: '',
      warranty: '',
      usageRate: 'Medium'
    });
    setShowAddModal(false);
  };

  // Update stock function
  const handleUpdateStock = (id, newStock) => {
    setPartsInventory(partsInventory.map(part => {
      if (part.id === id) {
        const updatedPart = {
          ...part,
          currentStock: parseInt(newStock),
          totalValue: parseInt(newStock) * part.unitCost,
          status: parseInt(newStock) === 0 ? 'Out of Stock' :
                  parseInt(newStock) <= part.minStock ? 'Low Stock' : 'In Stock'
        };
        return updatedPart;
      }
      return part;
    }));
  };

  // Delete part function
  const handleDeletePart = (id) => {
    setPartsInventory(partsInventory.filter(part => part.id !== id));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'In Stock': return 'bg-green-100 text-green-700';
      case 'Low Stock': return 'bg-yellow-100 text-yellow-700';
      case 'Out of Stock': return 'bg-red-100 text-red-700';
      case 'Reorder': return 'bg-orange-100 text-orange-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getUsageRateColor = (rate) => {
    switch (rate) {
      case 'High': return 'text-red-600';
      case 'Medium': return 'text-yellow-600';
      case 'Low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getStockLevel = (current, min, max) => {
    const percentage = (current / max) * 100;
    if (current === 0) return { level: 'empty', color: 'bg-red-500' };
    if (current <= min) return { level: 'low', color: 'bg-yellow-500' };
    if (percentage >= 80) return { level: 'high', color: 'bg-green-500' };
    return { level: 'medium', color: 'bg-blue-500' };
  };

  const filteredParts = partsInventory.filter(part => {
    const matchesSearch = part.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         part.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         part.category.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = filterCategory === 'all' || part.category.toLowerCase() === filterCategory.toLowerCase();
    const matchesStatus = filterStatus === 'all' || part.status.toLowerCase().replace(' ', '') === filterStatus.toLowerCase();
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const totalValue = filteredParts.reduce((sum, part) => sum + part.totalValue, 0);
  const lowStockItems = filteredParts.filter(part => part.currentStock <= part.minStock).length;
  const outOfStockItems = filteredParts.filter(part => part.currentStock === 0).length;

  const headerActions = (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search parts..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
          onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
          onBlur={(e) => e.target.style.boxShadow = 'none'}
        />
      </div>
      
      <select
        value={filterCategory}
        onChange={(e) => setFilterCategory(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Categories</option>
        <option value="power systems">Power Systems</option>
        <option value="propulsion">Propulsion</option>
        <option value="imaging">Imaging</option>
        <option value="electronics">Electronics</option>
        <option value="mechanical">Mechanical</option>
      </select>

      <select
        value={filterStatus}
        onChange={(e) => setFilterStatus(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Status</option>
        <option value="instock">In Stock</option>
        <option value="lowstock">Low Stock</option>
        <option value="outofstock">Out of Stock</option>
      </select>

      <button
        onClick={() => setShowAddModal(true)}
        className="px-4 py-2 text-white rounded-lg transition-all duration-150 hover:shadow-lg hover:-translate-y-0.5 flex items-center gap-2"
        style={{backgroundColor: '#e0e7ff'}}
        onMouseEnter={(e) => e.target.style.backgroundColor = '#c7d2fe'}
        onMouseLeave={(e) => e.target.style.backgroundColor = '#e0e7ff'}
      >
        <Plus className="w-4 h-4 text-blue-600" />
        <span className="text-blue-700 font-medium">Add Part</span>
      </button>
    </div>
  );

  return (
    <QCLayout
      title="Parts Inventory"
      subtitle="Manage drone parts and components inventory"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Inventory Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Parts</p>
                <p className="text-2xl font-bold text-gray-900">{filteredParts.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Package className="w-3 h-3" />
                  Active items
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Package className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">${totalValue.toLocaleString()}</p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <TrendingUp className="w-3 h-3" />
                  +8% this month
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <DollarSign className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Low Stock</p>
                <p className="text-2xl font-bold text-gray-900">{lowStockItems}</p>
                <p className="text-sm text-yellow-600 flex items-center gap-1 mt-1">
                  <AlertTriangle className="w-3 h-3" />
                  Need reorder
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fffbeb'}}>
                <AlertTriangle className="w-6 h-6 text-orange-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Out of Stock</p>
                <p className="text-2xl font-bold text-gray-900">{outOfStockItems}</p>
                <p className="text-sm text-red-600 flex items-center gap-1 mt-1">
                  <AlertTriangle className="w-3 h-3" />
                  Urgent action
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fef2f2'}}>
                <AlertTriangle className="w-6 h-6 text-red-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Parts Inventory Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Parts Inventory</h3>
                <p className="text-sm text-gray-600 mt-1">Track and manage all drone parts and components</p>
              </div>
              <div className="flex items-center gap-2">
                <button className="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md transition-colors flex items-center gap-1">
                  <ShoppingCart className="w-4 h-4" />
                  Reorder Report
                </button>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Part Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock Level
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Value
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Supplier
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredParts.map((part) => {
                  const stockLevel = getStockLevel(part.currentStock, part.minStock, part.maxStock);
                  return (
                    <tr key={part.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-start gap-3">
                          <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                            <Package className="w-5 h-5 text-blue-500" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="text-sm font-medium text-gray-900">{part.name}</h4>
                              <span className={`text-xs font-medium ${getUsageRateColor(part.usageRate)}`}>
                                {part.usageRate} Usage
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{part.description}</p>
                            <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                              <span>SKU: {part.sku}</span>
                              <span className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {part.location}
                              </span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="font-medium text-gray-900">{part.currentStock}</span>
                            <span className="text-gray-500">/ {part.maxStock}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full transition-all duration-300 ${stockLevel.color}`}
                              style={{width: `${Math.min((part.currentStock / part.maxStock) * 100, 100)}%`}}
                            />
                          </div>
                          <div className="text-xs text-gray-500">
                            Min: {part.minStock} | Max: {part.maxStock}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">${part.totalValue.toLocaleString()}</div>
                          <div className="text-sm text-gray-600">${part.unitCost} each</div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{part.supplier}</div>
                          <div className="text-sm text-gray-600 flex items-center gap-1">
                            <Truck className="w-3 h-3" />
                            {part.leadTime}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(part.status)}`}>
                          {part.status}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            min="0"
                            value={part.currentStock}
                            onChange={(e) => handleUpdateStock(part.id, e.target.value)}
                            className="w-16 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                            title="Update Stock"
                          />
                          <button
                            className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                            title="View Details"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                            onClick={() => handleDeletePart(part.id)}
                            title="Delete Part"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add Part Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Add New Part</h3>
                  <button
                    onClick={() => setShowAddModal(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <form onSubmit={handleAddPart} className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Part Name*</label>
                    <input
                      type="text"
                      required
                      value={newPart.name}
                      onChange={(e) => setNewPart({...newPart, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Battery Pack LiPo 6S"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Category*</label>
                    <select
                      required
                      value={newPart.category}
                      onChange={(e) => setNewPart({...newPart, category: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="Power Systems">Power Systems</option>
                      <option value="Propulsion">Propulsion</option>
                      <option value="Imaging">Imaging</option>
                      <option value="Electronics">Electronics</option>
                      <option value="Mechanical">Mechanical</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">SKU*</label>
                    <input
                      type="text"
                      required
                      value={newPart.sku}
                      onChange={(e) => setNewPart({...newPart, sku: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., BAT-6S-5000"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Usage Rate*</label>
                    <select
                      required
                      value={newPart.usageRate}
                      onChange={(e) => setNewPart({...newPart, usageRate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="Low">Low</option>
                      <option value="Medium">Medium</option>
                      <option value="High">High</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Current Stock*</label>
                    <input
                      type="number"
                      min="0"
                      required
                      value={newPart.currentStock}
                      onChange={(e) => setNewPart({...newPart, currentStock: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 8"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Min Stock*</label>
                    <input
                      type="number"
                      min="0"
                      required
                      value={newPart.minStock}
                      onChange={(e) => setNewPart({...newPart, minStock: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 5"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Max Stock*</label>
                    <input
                      type="number"
                      min="1"
                      required
                      value={newPart.maxStock}
                      onChange={(e) => setNewPart({...newPart, maxStock: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 20"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Unit Cost*</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      required
                      value={newPart.unitCost}
                      onChange={(e) => setNewPart({...newPart, unitCost: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 125.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Supplier*</label>
                    <input
                      type="text"
                      required
                      value={newPart.supplier}
                      onChange={(e) => setNewPart({...newPart, supplier: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., PowerTech Solutions"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location*</label>
                    <input
                      type="text"
                      required
                      value={newPart.location}
                      onChange={(e) => setNewPart({...newPart, location: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Warehouse A - Shelf 3"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Lead Time*</label>
                    <input
                      type="text"
                      required
                      value={newPart.leadTime}
                      onChange={(e) => setNewPart({...newPart, leadTime: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 7 days"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Warranty*</label>
                    <input
                      type="text"
                      required
                      value={newPart.warranty}
                      onChange={(e) => setNewPart({...newPart, warranty: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., 12 months"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description*</label>
                    <textarea
                      required
                      rows={3}
                      value={newPart.description}
                      onChange={(e) => setNewPart({...newPart, description: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Describe the part..."
                    />
                  </div>
                </div>

                <div className="flex items-center justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Add Part
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </QCLayout>
  );
};

export default PartsInventory;
