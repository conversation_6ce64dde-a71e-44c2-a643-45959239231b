import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import QCLayout from '../common/QCLayout';
import {
  ArrowLeft,
  Award,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  MapPin,
  Star,
  Target,
  Camera,
  Zap,
  Edit,
  Download,
  Printer,
  TrendingUp,
  BarChart3
} from 'lucide-react';

const QualityAssessmentDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [assessment, setAssessment] = useState(null);
  const [loading, setLoading] = useState(true);

  // Sample assessment data
  useEffect(() => {
    setTimeout(() => {
      const sampleAssessment = {
        id: id || 'QA-2024-001',
        droneId: 'DRN-001',
        droneName: 'Surveyor Alpha',
        assessor: '<PERSON>',
        date: '2024-01-15',
        time: '14:30',
        location: 'Quality Lab A',
        missionType: 'Aerial Survey',
        overallRating: 9.2,
        criteria: {
          imageQuality: { 
            score: 9.5, 
            notes: 'Excellent clarity and resolution. Sharp focus throughout flight.',
            details: {
              resolution: '4K UHD',
              sharpness: 'Excellent',
              colorAccuracy: 'High',
              noiseLevel: 'Minimal',
              stabilization: 'Perfect'
            }
          },
          flightStability: { 
            score: 9.0, 
            notes: 'Smooth flight path with minimal drift. Excellent hover stability.',
            details: {
              hoverStability: 'Excellent',
              windResistance: 'Good',
              pathAccuracy: 'High',
              vibrationLevel: 'Low',
              responseTime: 'Fast'
            }
          },
          dataAccuracy: { 
            score: 9.3, 
            notes: 'GPS coordinates highly accurate. Telemetry data consistent.',
            details: {
              gpsAccuracy: '±1.2m',
              altitudeAccuracy: '±0.5m',
              speedAccuracy: '±0.2 km/h',
              headingAccuracy: '±1°',
              timestampSync: 'Perfect'
            }
          },
          systemReliability: { 
            score: 8.8, 
            notes: 'Minor communication lag observed during mission.',
            details: {
              uptime: '100%',
              errorRate: '0.1%',
              responseTime: '120ms avg',
              failsafeFunction: 'Operational',
              redundancy: 'Active'
            }
          },
          missionCompletion: { 
            score: 9.5, 
            notes: 'All waypoints covered successfully. Mission objectives met.',
            details: {
              waypointAccuracy: '100%',
              coverageArea: '100%',
              dataCompleteness: '99.8%',
              timeEfficiency: '95%',
              objectivesMet: '100%'
            }
          }
        },
        strengths: [
          'Exceptional image quality with 4K resolution',
          'Precise navigation and waypoint accuracy',
          'Complete data capture with minimal loss',
          'Excellent weather adaptation capabilities',
          'Superior battery performance throughout mission'
        ],
        improvements: [
          'Communication system optimization needed',
          'Response time could be improved in high-wind conditions',
          'Data processing speed enhancement recommended'
        ],
        recommendations: [
          'Continue current maintenance schedule',
          'Monitor communication system performance',
          'Consider firmware update for improved response time',
          'Implement advanced wind compensation algorithms'
        ],
        nextAssessment: '2024-02-15',
        missionDetails: {
          duration: '45 minutes',
          area: '2.5 km²',
          altitude: '120m',
          weather: 'Clear, 15°C, 8 km/h wind',
          objectives: 'Construction site survey and progress monitoring'
        },
        performanceMetrics: {
          efficiency: '94%',
          accuracy: '98.5%',
          reliability: '96.8%',
          safety: '100%',
          costEffectiveness: '92%'
        }
      };
      setAssessment(sampleAssessment);
      setLoading(false);
    }, 500);
  }, [id]);

  const getRatingColor = (rating) => {
    if (rating >= 9) return 'text-green-600';
    if (rating >= 8) return 'text-blue-600';
    if (rating >= 7) return 'text-yellow-600';
    if (rating >= 6) return 'text-orange-600';
    return 'text-red-600';
  };

  const getRatingBadgeColor = (rating) => {
    if (rating >= 9) return 'bg-green-100 text-green-700 border-green-200';
    if (rating >= 8) return 'bg-blue-100 text-blue-700 border-blue-200';
    if (rating >= 7) return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    if (rating >= 6) return 'bg-orange-100 text-orange-700 border-orange-200';
    return 'bg-red-100 text-red-700 border-red-200';
  };

  const getStarRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating / 2);
    const hasHalfStar = (rating % 2) >= 1;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />);
    }
    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-4 h-4 fill-yellow-200 text-yellow-400" />);
    }
    const remainingStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-4 h-4 text-gray-300" />);
    }
    return stars;
  };

  const headerActions = (
    <div className="flex items-center gap-3">
      <button
        onClick={() => navigate('/qc-inspections/quality-assessment')}
        className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
      >
        <ArrowLeft className="w-4 h-4" />
        Back to List
      </button>
      <button className="px-4 py-2 text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors flex items-center gap-2">
        <Edit className="w-4 h-4" />
        Edit
      </button>
      <button className="px-4 py-2 text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors flex items-center gap-2">
        <Download className="w-4 h-4" />
        Export
      </button>
      <button className="px-4 py-2 text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors flex items-center gap-2">
        <Printer className="w-4 h-4" />
        Print
      </button>
    </div>
  );

  if (loading) {
    return (
      <QCLayout title="Loading..." subtitle="Please wait">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </QCLayout>
    );
  }

  if (!assessment) {
    return (
      <QCLayout title="Assessment Not Found" subtitle="The requested assessment could not be found">
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Assessment not found</p>
          <button
            onClick={() => navigate('/qc-inspections/quality-assessment')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Assessments
          </button>
        </div>
      </QCLayout>
    );
  }

  return (
    <QCLayout
      title={`Quality Assessment - ${assessment.id}`}
      subtitle={`${assessment.droneName} (${assessment.droneId}) - ${assessment.missionType}`}
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Assessment Overview */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 rounded-xl flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Award className="w-8 h-8 text-blue-500" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 mb-2">{assessment.id}</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Assessor:</span>
                    <span className="font-medium">{assessment.assessor}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Date:</span>
                    <span className="font-medium">{assessment.date} at {assessment.time}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Location:</span>
                    <span className="font-medium">{assessment.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Target className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Mission:</span>
                    <span className="font-medium">{assessment.missionType}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-center">
              <div className={`text-3xl font-bold ${getRatingColor(assessment.overallRating)} mb-2`}>
                {assessment.overallRating}
              </div>
              <div className="flex items-center justify-center gap-1 mb-2">
                {getStarRating(assessment.overallRating)}
              </div>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getRatingBadgeColor(assessment.overallRating)}`}>
                {assessment.overallRating >= 9 ? 'Excellent' : 
                 assessment.overallRating >= 8 ? 'Good' : 
                 assessment.overallRating >= 7 ? 'Fair' : 'Poor'}
              </span>
              <p className="text-sm text-gray-500 mt-2">Next: {assessment.nextAssessment}</p>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
              <BarChart3 className="w-5 h-5 text-blue-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Performance Metrics</h3>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{assessment.performanceMetrics.efficiency}</p>
              <p className="text-sm text-gray-600">Efficiency</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">{assessment.performanceMetrics.accuracy}</p>
              <p className="text-sm text-gray-600">Accuracy</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-purple-600">{assessment.performanceMetrics.reliability}</p>
              <p className="text-sm text-gray-600">Reliability</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-orange-600">{assessment.performanceMetrics.safety}</p>
              <p className="text-sm text-gray-600">Safety</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-indigo-600">{assessment.performanceMetrics.costEffectiveness}</p>
              <p className="text-sm text-gray-600">Cost Effectiveness</p>
            </div>
          </div>
        </div>

        {/* Quality Criteria Detailed Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Image Quality */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Camera className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Image Quality</h3>
                <div className="flex items-center gap-2">
                  <span className={`text-xl font-bold ${getRatingColor(assessment.criteria.imageQuality.score)}`}>
                    {assessment.criteria.imageQuality.score}
                  </span>
                  <div className="flex items-center gap-1">
                    {getStarRating(assessment.criteria.imageQuality.score)}
                  </div>
                </div>
              </div>
            </div>
            <p className="text-sm text-gray-700 mb-4">{assessment.criteria.imageQuality.notes}</p>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-gray-600">Resolution:</span>
                <span className="font-medium ml-2">{assessment.criteria.imageQuality.details.resolution}</span>
              </div>
              <div>
                <span className="text-gray-600">Sharpness:</span>
                <span className="font-medium ml-2">{assessment.criteria.imageQuality.details.sharpness}</span>
              </div>
              <div>
                <span className="text-gray-600">Color Accuracy:</span>
                <span className="font-medium ml-2">{assessment.criteria.imageQuality.details.colorAccuracy}</span>
              </div>
              <div>
                <span className="text-gray-600">Noise Level:</span>
                <span className="font-medium ml-2">{assessment.criteria.imageQuality.details.noiseLevel}</span>
              </div>
            </div>
          </div>

          {/* Flight Stability */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Target className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Flight Stability</h3>
                <div className="flex items-center gap-2">
                  <span className={`text-xl font-bold ${getRatingColor(assessment.criteria.flightStability.score)}`}>
                    {assessment.criteria.flightStability.score}
                  </span>
                  <div className="flex items-center gap-1">
                    {getStarRating(assessment.criteria.flightStability.score)}
                  </div>
                </div>
              </div>
            </div>
            <p className="text-sm text-gray-700 mb-4">{assessment.criteria.flightStability.notes}</p>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-gray-600">Hover Stability:</span>
                <span className="font-medium ml-2">{assessment.criteria.flightStability.details.hoverStability}</span>
              </div>
              <div>
                <span className="text-gray-600">Wind Resistance:</span>
                <span className="font-medium ml-2">{assessment.criteria.flightStability.details.windResistance}</span>
              </div>
              <div>
                <span className="text-gray-600">Path Accuracy:</span>
                <span className="font-medium ml-2">{assessment.criteria.flightStability.details.pathAccuracy}</span>
              </div>
              <div>
                <span className="text-gray-600">Response Time:</span>
                <span className="font-medium ml-2">{assessment.criteria.flightStability.details.responseTime}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default QualityAssessmentDetail;
