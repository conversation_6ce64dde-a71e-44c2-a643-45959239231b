import React, { useState } from 'react';
import QCLayout from '../common/QCLayout';
import {
  Server,
  Database,
  Wifi,
  HardDrive,
  Cpu,
  MemoryStick,
  Monitor,
  Settings,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Activity,
  Zap,
  Globe,
  Shield,
  Clock,
  BarChart3,
  TrendingUp,
  Download,
  Upload
} from 'lucide-react';

const SystemConfiguration = () => {
  const [settings, setSettings] = useState({
    // Server Configuration
    serverName: 'QC-SERVER-01',
    serverEnvironment: 'production',
    maxConnections: 1000,
    connectionTimeout: 30,
    requestTimeout: 60,
    maxRequestSize: 100,
    enableCompression: true,
    compressionLevel: 6,
    
    // Database Configuration
    databaseHost: 'localhost',
    databasePort: 5432,
    databaseName: 'shakti_qc',
    connectionPoolSize: 20,
    queryTimeout: 30,
    enableQueryLogging: true,
    backupSchedule: 'daily',
    
    // Performance Settings
    cacheEnabled: true,
    cacheSize: 512,
    cacheTTL: 3600,
    enableCDN: true,
    cdnProvider: 'cloudflare',
    enableLoadBalancing: false,
    
    // Storage Configuration
    storageType: 'local',
    storagePath: '/var/lib/shakti-qc/data',
    maxStorageSize: 1000,
    enableStorageEncryption: true,
    storageRetentionDays: 365,
    enableAutoCleanup: true,
    
    // Network Configuration
    enableHTTPS: true,
    httpsPort: 443,
    httpPort: 80,
    enableCORS: true,
    allowedOrigins: 'https://shakti-drones.com',
    enableRateLimit: true,
    rateLimitRequests: 100,
    rateLimitWindow: 60,
    
    // Monitoring & Logging
    enableSystemMonitoring: true,
    monitoringInterval: 60,
    enablePerformanceLogging: true,
    logLevel: 'info',
    logRetentionDays: 30,
    enableAlerts: true,
    alertThresholds: {
      cpuUsage: 80,
      memoryUsage: 85,
      diskUsage: 90,
      responseTime: 5000
    }
  });

  const [systemStatus, setSystemStatus] = useState({
    cpuUsage: 45,
    memoryUsage: 62,
    diskUsage: 38,
    networkLatency: 12,
    activeConnections: 156,
    uptime: '15 days, 8 hours',
    lastBackup: '2024-01-15 02:00:00',
    systemHealth: 'Healthy'
  });

  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setUnsavedChanges(true);
  };

  const handleNestedInputChange = (parent, field, value) => {
    setSettings(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }));
    setUnsavedChanges(true);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setUnsavedChanges(false);
      setLastSaved(new Date().toLocaleString());
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setSettings({
      serverName: 'QC-SERVER-01',
      serverEnvironment: 'production',
      maxConnections: 1000,
      connectionTimeout: 30,
      requestTimeout: 60,
      maxRequestSize: 100,
      enableCompression: true,
      compressionLevel: 6,
      databaseHost: 'localhost',
      databasePort: 5432,
      databaseName: 'shakti_qc',
      connectionPoolSize: 20,
      queryTimeout: 30,
      enableQueryLogging: true,
      backupSchedule: 'daily',
      cacheEnabled: true,
      cacheSize: 512,
      cacheTTL: 3600,
      enableCDN: true,
      cdnProvider: 'cloudflare',
      enableLoadBalancing: false,
      storageType: 'local',
      storagePath: '/var/lib/shakti-qc/data',
      maxStorageSize: 1000,
      enableStorageEncryption: true,
      storageRetentionDays: 365,
      enableAutoCleanup: true,
      enableHTTPS: true,
      httpsPort: 443,
      httpPort: 80,
      enableCORS: true,
      allowedOrigins: 'https://shakti-drones.com',
      enableRateLimit: true,
      rateLimitRequests: 100,
      rateLimitWindow: 60,
      enableSystemMonitoring: true,
      monitoringInterval: 60,
      enablePerformanceLogging: true,
      logLevel: 'info',
      logRetentionDays: 30,
      enableAlerts: true,
      alertThresholds: {
        cpuUsage: 80,
        memoryUsage: 85,
        diskUsage: 90,
        responseTime: 5000
      }
    });
    setUnsavedChanges(false);
  };

  const getHealthColor = (health) => {
    switch (health) {
      case 'Healthy': return 'text-green-600';
      case 'Warning': return 'text-yellow-600';
      case 'Critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getUsageColor = (usage) => {
    if (usage >= 90) return 'bg-red-500';
    if (usage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const headerActions = (
    <div className="flex items-center gap-3">
      {unsavedChanges && (
        <div className="flex items-center gap-2 text-amber-600 bg-amber-50 px-3 py-1 rounded-lg border border-amber-200">
          <AlertTriangle className="w-4 h-4" />
          <span className="text-sm font-medium">Unsaved Changes</span>
        </div>
      )}
      
      {lastSaved && (
        <div className="text-sm text-gray-500">
          Last saved: {lastSaved}
        </div>
      )}

      <div className="flex items-center gap-2 px-3 py-1 bg-white border border-gray-200 rounded-lg">
        <Activity className="w-4 h-4 text-green-500" />
        <span className="text-sm font-medium text-gray-700">System:</span>
        <span className={`text-sm font-bold ${getHealthColor(systemStatus.systemHealth)}`}>
          {systemStatus.systemHealth}
        </span>
      </div>

      <button
        onClick={handleReset}
        className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
      >
        <RefreshCw className="w-4 h-4" />
        Reset
      </button>

      <button
        onClick={handleSave}
        disabled={saving || !unsavedChanges}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
      >
        <Save className="w-4 h-4" />
        {saving ? 'Saving...' : 'Save Changes'}
      </button>
    </div>
  );

  return (
    <QCLayout
      title="System Configuration"
      subtitle="Configure server, database, and system performance settings"
      actions={headerActions}
    >
      <div className="space-y-8">
        {/* System Status Overview */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Activity className="w-5 h-5 text-blue-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">System Status</h3>
                <p className="text-sm text-gray-600 mt-1">Real-time system performance and health metrics</p>
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="relative w-20 h-20 mx-auto mb-3">
                  <div className="w-20 h-20 rounded-full bg-gray-200">
                    <div 
                      className={`w-20 h-20 rounded-full ${getUsageColor(systemStatus.cpuUsage)} flex items-center justify-center`}
                      style={{
                        background: `conic-gradient(${getUsageColor(systemStatus.cpuUsage).replace('bg-', '#')} ${systemStatus.cpuUsage * 3.6}deg, #e5e7eb 0deg)`
                      }}
                    >
                      <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center">
                        <span className="text-sm font-bold text-gray-900">{systemStatus.cpuUsage}%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-center gap-2 mb-1">
                  <Cpu className="w-4 h-4 text-blue-500" />
                  <h4 className="text-sm font-semibold text-gray-900">CPU Usage</h4>
                </div>
                <p className="text-xs text-gray-600">Current processor load</p>
              </div>

              <div className="text-center">
                <div className="relative w-20 h-20 mx-auto mb-3">
                  <div className="w-20 h-20 rounded-full bg-gray-200">
                    <div 
                      className={`w-20 h-20 rounded-full ${getUsageColor(systemStatus.memoryUsage)} flex items-center justify-center`}
                      style={{
                        background: `conic-gradient(${getUsageColor(systemStatus.memoryUsage).replace('bg-', '#')} ${systemStatus.memoryUsage * 3.6}deg, #e5e7eb 0deg)`
                      }}
                    >
                      <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center">
                        <span className="text-sm font-bold text-gray-900">{systemStatus.memoryUsage}%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-center gap-2 mb-1">
                  <MemoryStick className="w-4 h-4 text-green-500" />
                  <h4 className="text-sm font-semibold text-gray-900">Memory Usage</h4>
                </div>
                <p className="text-xs text-gray-600">RAM utilization</p>
              </div>

              <div className="text-center">
                <div className="relative w-20 h-20 mx-auto mb-3">
                  <div className="w-20 h-20 rounded-full bg-gray-200">
                    <div 
                      className={`w-20 h-20 rounded-full ${getUsageColor(systemStatus.diskUsage)} flex items-center justify-center`}
                      style={{
                        background: `conic-gradient(${getUsageColor(systemStatus.diskUsage).replace('bg-', '#')} ${systemStatus.diskUsage * 3.6}deg, #e5e7eb 0deg)`
                      }}
                    >
                      <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center">
                        <span className="text-sm font-bold text-gray-900">{systemStatus.diskUsage}%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-center gap-2 mb-1">
                  <HardDrive className="w-4 h-4 text-purple-500" />
                  <h4 className="text-sm font-semibold text-gray-900">Disk Usage</h4>
                </div>
                <p className="text-xs text-gray-600">Storage utilization</p>
              </div>

              <div className="text-center">
                <div className="relative w-20 h-20 mx-auto mb-3">
                  <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-lg font-bold text-gray-900">{systemStatus.networkLatency}</div>
                      <div className="text-xs text-gray-600">ms</div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-center gap-2 mb-1">
                  <Wifi className="w-4 h-4 text-orange-500" />
                  <h4 className="text-sm font-semibold text-gray-900">Network Latency</h4>
                </div>
                <p className="text-xs text-gray-600">Response time</p>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{systemStatus.activeConnections}</div>
                  <div className="text-sm text-gray-600">Active Connections</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{systemStatus.uptime}</div>
                  <div className="text-sm text-gray-600">System Uptime</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{systemStatus.lastBackup}</div>
                  <div className="text-sm text-gray-600">Last Backup</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Server Configuration */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <Server className="w-5 h-5 text-green-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Server Configuration</h3>
                <p className="text-sm text-gray-600 mt-1">Configure server settings and connection parameters</p>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Server Name</label>
                <input
                  type="text"
                  value={settings.serverName}
                  onChange={(e) => handleInputChange('serverName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter server name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Environment</label>
                <select
                  value={settings.serverEnvironment}
                  onChange={(e) => handleInputChange('serverEnvironment', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="development">Development</option>
                  <option value="staging">Staging</option>
                  <option value="production">Production</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Connections</label>
                <input
                  type="number"
                  min="100"
                  max="10000"
                  value={settings.maxConnections}
                  onChange={(e) => handleInputChange('maxConnections', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Connection Timeout (seconds)</label>
                <input
                  type="number"
                  min="10"
                  max="300"
                  value={settings.connectionTimeout}
                  onChange={(e) => handleInputChange('connectionTimeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Request Timeout (seconds)</label>
                <input
                  type="number"
                  min="30"
                  max="600"
                  value={settings.requestTimeout}
                  onChange={(e) => handleInputChange('requestTimeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Request Size (MB)</label>
                <input
                  type="number"
                  min="1"
                  max="1000"
                  value={settings.maxRequestSize}
                  onChange={(e) => handleInputChange('maxRequestSize', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Server Features</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.enableCompression}
                    onChange={(e) => handleInputChange('enableCompression', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Enable Compression</div>
                    <div className="text-xs text-gray-500">Compress responses to reduce bandwidth</div>
                  </div>
                </label>
                {settings.enableCompression && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Compression Level (1-9)</label>
                    <input
                      type="number"
                      min="1"
                      max="9"
                      value={settings.compressionLevel}
                      onChange={(e) => handleInputChange('compressionLevel', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Database Configuration */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fef3c7'}}>
                <Database className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Database Configuration</h3>
                <p className="text-sm text-gray-600 mt-1">Configure database connection and performance settings</p>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Database Host</label>
                <input
                  type="text"
                  value={settings.databaseHost}
                  onChange={(e) => handleInputChange('databaseHost', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="localhost or IP address"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Database Port</label>
                <input
                  type="number"
                  min="1"
                  max="65535"
                  value={settings.databasePort}
                  onChange={(e) => handleInputChange('databasePort', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Database Name</label>
                <input
                  type="text"
                  value={settings.databaseName}
                  onChange={(e) => handleInputChange('databaseName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Database name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Connection Pool Size</label>
                <input
                  type="number"
                  min="5"
                  max="100"
                  value={settings.connectionPoolSize}
                  onChange={(e) => handleInputChange('connectionPoolSize', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Query Timeout (seconds)</label>
                <input
                  type="number"
                  min="10"
                  max="300"
                  value={settings.queryTimeout}
                  onChange={(e) => handleInputChange('queryTimeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Backup Schedule</label>
                <select
                  value={settings.backupSchedule}
                  onChange={(e) => handleInputChange('backupSchedule', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="hourly">Hourly</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Database Features</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.enableQueryLogging}
                    onChange={(e) => handleInputChange('enableQueryLogging', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Enable Query Logging</div>
                    <div className="text-xs text-gray-500">Log all database queries for debugging</div>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default SystemConfiguration;
