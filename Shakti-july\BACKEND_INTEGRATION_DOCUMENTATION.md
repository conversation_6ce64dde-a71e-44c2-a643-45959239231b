# Backend Integration Documentation

## Overview
The Enhanced Admin Map has been successfully integrated with the backend database to fetch and display real data for organizations and individuals. The system now uses live data from the MongoDB database while maintaining fallback support for mock data during development.

## Backend Integration Features

### 🔄 **Hybrid Data Loading**
- **Primary**: Fetches real data from backend APIs
- **Fallback**: Uses mock data if backend is unavailable
- **Caching**: Implements 5-minute cache to reduce API calls
- **Real-time**: Live data updates with connection status

### 🏢 **Organization Data Integration**
- **API Endpoint**: `/api/organizations`
- **Service**: `organizationService.getAllOrganizations()`
- **Data Transformation**: Backend data → Map display format
- **Features**: Search, filtering, pagination support

### 👥 **Individual Data Integration**
- **API Endpoint**: `/api/individuals`
- **Service**: `individualService.getAllIndividuals()`
- **Data Transformation**: Backend data → Map display format
- **Features**: Search, filtering, status management

## Data Transformation

### Organization Data Mapping
```javascript
Backend Data → Map Format:
{
  _id: "507f1f77bcf86cd799439011"           → id: "507f1f77bcf86cd799439011"
  name: "Salam Kisan"                       → name: "Salam Kisan"
  registration.registrationNumber           → orgId: "REG-123456"
  address.state: "Maharashtra"              → state: "Maharashtra"
  address.city: "Pune"                      → district: "Pune"
  allocatedDrones: 10                       → totalDrones: 10
  status: "active"                          → status: "active"
  contact.primaryEmail                      → email: "<EMAIL>"
  contact.phone                             → phone: "+91-9876543210"
  address.coordinates                       → position: [lat, lng]
}
```

### Individual Data Mapping
```javascript
Backend Data → Map Format:
{
  _id: "507f1f77bcf86cd799439012"           → id: "507f1f77bcf86cd799439012"
  fullName: "John Doe"                      → fullName: "John Doe"
  gender: "male"                            → gender: "male"
  dateOfBirth: "1990-01-15"                 → age: 34 (calculated)
  address.state: "Karnataka"               → state: "Karnataka"
  address.city: "Bangalore"                → city: "Bangalore"
  allocatedDrones: 2                        → totalDrones: 2
  status: "approved"                        → status: "approved"
  contact.primaryEmail                      → email: "<EMAIL>"
  contact.phone                             → phone: "+91-9876543210"
  address.coordinates                       → position: [lat, lng]
}
```

## Smart Coordinate Handling

### 🗺️ **Location Resolution**
1. **Primary**: Uses `address.coordinates` from database
2. **Secondary**: Maps state names to default coordinates
3. **Fallback**: Uses India center coordinates

### 📍 **State Coordinate Mapping**
```javascript
const stateCoordinates = {
  'Maharashtra': [19.7515, 75.7139],
  'Karnataka': [15.3173, 75.7139],
  'Gujarat': [23.0225, 72.5714],
  'Rajasthan': [27.0238, 74.2179],
  'Delhi': [28.7041, 77.1025],
  'Tamil Nadu': [11.1271, 78.6569],
  'Uttar Pradesh': [26.8467, 80.9462]
};
```

## Caching Strategy

### ⚡ **Performance Optimization**
- **Cache Duration**: 5 minutes per data type
- **Cache Keys**: `organizations`, `individuals`
- **Cache Validation**: Timestamp-based expiry
- **Smart Refresh**: Only fetches when cache expires or filters change

### 🔄 **Cache Management**
```javascript
// Cache structure
this.cache = {
  organizations: [],
  individuals: [],
  dronesByOrg: {},
  dronesByIndividual: {},
  lastFetch: {
    organizations: null,
    individuals: null
  }
};
```

## Error Handling & Fallbacks

### 🛡️ **Robust Error Management**
1. **Network Errors**: Graceful fallback to mock data
2. **API Errors**: Detailed error logging with fallback
3. **Data Validation**: Handles missing or invalid data
4. **User Experience**: Seamless operation regardless of backend status

### 📊 **Fallback Data**
- **Organizations**: 5 sample organizations with realistic data
- **Individuals**: 5 sample individuals with diverse profiles
- **Drones**: Mock drone fleets for each entity
- **Locations**: Distributed across different Indian states

## API Integration Details

### 🔌 **Service Layer**
- **organizationService**: Handles all organization API calls
- **individualService**: Manages individual data operations
- **api.js**: Centralized axios configuration with auth tokens

### 🔐 **Authentication**
- **JWT Tokens**: Automatic token injection via axios interceptors
- **Optional Auth**: Works with both authenticated and public access
- **Token Management**: Stored in localStorage with automatic refresh

## Real-time Features

### 🔄 **Live Updates**
- **Data Refresh**: Manual refresh button with loading states
- **Connection Status**: Real-time connection indicator
- **Auto-refresh**: Periodic data updates (configurable)
- **Change Detection**: Notifies subscribers of data changes

### 📡 **Subscription System**
```javascript
// Subscribe to data updates
const unsubscribe = mapDataService.subscribe('organizations', (data) => {
  console.log('Organizations updated:', data);
});
```

## Statistics Integration

### 📈 **Combined Metrics**
- **Organization Stats**: Total, active, drone counts
- **Individual Stats**: Total, approved, allocated drones
- **Combined View**: Unified statistics across both entity types
- **Real-time Updates**: Statistics refresh with data changes

## Development & Production

### 🔧 **Development Mode**
- **Mock Data**: Available when backend is unavailable
- **Console Logging**: Detailed API call information
- **Error Tolerance**: Continues operation with fallback data

### 🚀 **Production Ready**
- **Error Handling**: Comprehensive error management
- **Performance**: Optimized with caching and lazy loading
- **Scalability**: Supports large datasets with pagination
- **Monitoring**: Connection status and performance metrics

## Testing the Integration

### ✅ **Verification Steps**
1. **Backend Running**: Ensure server is running on port 5000
2. **API Access**: Test `/api/organizations` and `/api/individuals`
3. **Frontend Connection**: Check browser console for API calls
4. **Data Display**: Verify real data appears on map
5. **Fallback Testing**: Stop backend to test mock data fallback

### 🔍 **Console Monitoring**
```javascript
// Look for these console messages:
"Fetching organizations from backend..."
"✅ Fetched 5 organizations from backend"
"Using cached organizations data"
"Backend fetch failed, using mock data"
```

## Future Enhancements

### 🚀 **Planned Features**
1. **Real Drone API**: Integration with actual drone management endpoints
2. **WebSocket Updates**: Real-time data streaming
3. **Advanced Filtering**: Database-level filtering and sorting
4. **Bulk Operations**: Multi-entity operations via API
5. **Analytics API**: Advanced statistics and reporting
6. **Geospatial Queries**: Location-based filtering and search

## Configuration

### ⚙️ **Environment Variables**
```env
VITE_API_URL=http://localhost:5000/api  # Backend API base URL
```

### 🔧 **Service Configuration**
```javascript
// Cache timeout (milliseconds)
this.cacheTimeout = 5 * 60 * 1000; // 5 minutes

// API request timeout
timeout: 10000 // 10 seconds
```

The enhanced map now provides a seamless experience with real backend data while maintaining reliability through intelligent fallback mechanisms and caching strategies.
