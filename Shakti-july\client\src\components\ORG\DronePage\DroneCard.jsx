import React from 'react';
import {
  MapPin,
  Battery,
  Signal,
  Thermometer,
  Clock,
  Activity,
  Eye,
  Settings,
  Plane,
  Zap,
  CheckCircle2,
  AlertT<PERSON>gle,
  <PERSON>ch,
  XCircle
} from 'lucide-react';

const DroneCard = ({ drone, isSelected, onSelect, onView }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'Maintenance':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Crashed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'Approval Pending':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Active':
        return <CheckCircle2 className="w-4 h-4" />;
      case 'Inactive':
        return <XCircle className="w-4 h-4" />;
      case 'Maintenance':
        return <Wrench className="w-4 h-4" />;
      case 'Crashed':
        return <AlertTriangle className="w-4 h-4" />;
      case 'Approval Pending':
        return <Clock className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getBatteryColor = (level) => {
    if (level > 60) return 'text-green-500';
    if (level > 30) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getEfficiencyColor = (efficiency) => {
    if (efficiency >= 90) return 'text-green-600';
    if (efficiency >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className={`bg-white rounded-xl shadow-sm border-2 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${
      isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300'
    }`}>
      {/* Card Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <input
                type="checkbox"
                checked={isSelected}
                onChange={() => onSelect(drone.id)}
                className="absolute top-0 left-0 w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 z-10"
              />
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center ml-6">
                <Plane className="w-6 h-6 text-white" />
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 text-lg">{drone.name}</h3>
              <p className="text-sm text-gray-500">{drone.id}</p>
              <p className="text-xs text-gray-400">{drone.model}</p>
            </div>
          </div>
          
          <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(drone.status)}`}>
            {getStatusIcon(drone.status)}
            {drone.status}
          </div>
        </div>
      </div>

      {/* Card Body */}
      <div className="p-4 space-y-4">
        {/* Location and Pilot */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <MapPin className="w-4 h-4 text-gray-400" />
            <span className="text-gray-600">Location:</span>
            <span className="font-medium text-gray-900">{drone.location || 'Not assigned'}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Activity className="w-4 h-4 text-gray-400" />
            <span className="text-gray-600">Pilot:</span>
            <span className="font-medium text-gray-900">{drone.pilot || 'Not assigned'}</span>
          </div>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-1">
              <Battery className={`w-4 h-4 ${getBatteryColor(drone.batteryLevel)}`} />
              <span className="text-xs text-gray-600">Battery</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    drone.batteryLevel > 60 ? 'bg-green-500' :
                    drone.batteryLevel > 30 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${drone.batteryLevel}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-gray-900">{drone.batteryLevel}%</span>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-1">
              <Zap className="w-4 h-4 text-blue-500" />
              <span className="text-xs text-gray-600">Efficiency</span>
            </div>
            <div className="flex items-center justify-between">
              <span className={`text-sm font-medium ${getEfficiencyColor(drone.efficiency)}`}>
                {drone.efficiency.toFixed(1)}%
              </span>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-1">
              <Signal className="w-4 h-4 text-green-500" />
              <span className="text-xs text-gray-600">Signal</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">{drone.signalStrength}%</span>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-1">
              <Thermometer className="w-4 h-4 text-orange-500" />
              <span className="text-xs text-gray-600">Temp</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">{drone.temperature}°C</span>
            </div>
          </div>
        </div>

        {/* Flight Stats */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">{drone.flightHours.toFixed(1)}</div>
              <div className="text-xs text-gray-600">Flight Hours</div>
            </div>
            <div>
              <div className="text-lg font-bold text-indigo-600">{drone.missions}</div>
              <div className="text-xs text-gray-600">Missions</div>
            </div>
          </div>
        </div>
      </div>

      {/* Card Footer */}
      <div className="px-4 py-3 bg-gray-50 rounded-b-xl border-t border-gray-100">
        <div className="flex items-center justify-between">
          <div className="text-xs text-gray-500">
            {drone.deployDate ? `Deployed: ${drone.deployDate}` : 'Not deployed'}
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onView}
              className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
              title="View Details"
            >
              <Eye className="w-4 h-4" />
            </button>
            <button
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200"
              title="Settings"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DroneCard;
