const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  // Basic notification information
  title: {
    type: String,
    required: [true, 'Notification title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  message: {
    type: String,
    required: [true, 'Notification message is required'],
    trim: true,
    maxlength: [1000, 'Message cannot exceed 1000 characters']
  },
  type: {
    type: String,
    required: [true, 'Notification type is required'],
    enum: [
      'organization_created',
      'organization_updated',
      'organization_deleted',
      'organization_restored',
      'drone_created',
      'drone_updated',
      'drone_deleted',
      'drone_status_changed',
      'individual_created',
      'individual_updated',
      'individual_deleted',
      'user_created',
      'user_updated',
      'system_alert',
      'system_test',
      'maintenance_due',
      'battery_low',
      'mission_completed',
      'geofence_breach',
      'weather_alert'
    ]
  },
  category: {
    type: String,
    required: [true, 'Notification category is required'],
    enum: ['admin', 'operations', 'maintenance', 'safety', 'system', 'user_management']
  },
  priority: {
    type: String,
    required: [true, 'Notification priority is required'],
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  
  // Status and visibility
  isRead: {
    type: Boolean,
    default: false
  },
  isArchived: {
    type: Boolean,
    default: false
  },
  
  // Related entities
  relatedEntity: {
    entityType: {
      type: String,
      enum: ['organization', 'drone', 'individual', 'user', 'system'],
      required: false
    },
    entityId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false
    },
    entityName: {
      type: String,
      required: false,
      trim: true
    }
  },
  
  // User who triggered the action (if applicable)
  triggeredBy: {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: false
    },
    username: {
      type: String,
      trim: true
    },
    role: {
      type: String,
      enum: ['admin', 'org', 'individual', 'system']
    }
  },
  
  // Additional metadata
  metadata: {
    action: {
      type: String,
      required: false // e.g., 'created', 'updated', 'deleted', 'status_changed'
    },
    previousValues: {
      type: mongoose.Schema.Types.Mixed // Store previous values for updates
    },
    newValues: {
      type: mongoose.Schema.Types.Mixed // Store new values for updates
    },
    location: {
      type: String,
      trim: true
    },
    ipAddress: {
      type: String,
      trim: true
    },
    userAgent: {
      type: String,
      trim: true
    }
  },
  
  // Icon and styling
  icon: {
    type: String,
    default: 'Bell'
  },
  color: {
    type: String,
    enum: ['blue', 'green', 'yellow', 'red', 'purple', 'orange', 'gray'],
    default: 'blue'
  },
  
  // Timestamps
  readAt: {
    type: Date,
    default: null
  },
  archivedAt: {
    type: Date,
    default: null
  },
  expiresAt: {
    type: Date,
    default: function() {
      // Auto-expire notifications after 30 days
      return new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
notificationSchema.index({ createdAt: -1 });
notificationSchema.index({ isRead: 1, createdAt: -1 });
notificationSchema.index({ category: 1, createdAt: -1 });
notificationSchema.index({ priority: 1, createdAt: -1 });
notificationSchema.index({ 'relatedEntity.entityType': 1, 'relatedEntity.entityId': 1 });
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Virtual for time ago
notificationSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diff = now - this.createdAt;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  return `${days}d ago`;
});

// Static methods
notificationSchema.statics.createNotification = async function(data) {
  const notification = new this(data);
  await notification.save();
  
  // Emit real-time notification (will be implemented with WebSocket)
  if (global.io) {
    global.io.emit('new_notification', notification);
  }
  
  return notification;
};

notificationSchema.statics.getUnreadCount = async function() {
  return await this.countDocuments({ isRead: false, isArchived: false });
};

notificationSchema.statics.markAllAsRead = async function() {
  const result = await this.updateMany(
    { isRead: false, isArchived: false },
    { 
      isRead: true, 
      readAt: new Date() 
    }
  );
  return result;
};

// Instance methods
notificationSchema.methods.markAsRead = async function() {
  if (!this.isRead) {
    this.isRead = true;
    this.readAt = new Date();
    await this.save();
  }
  return this;
};

notificationSchema.methods.archive = async function() {
  if (!this.isArchived) {
    this.isArchived = true;
    this.archivedAt = new Date();
    await this.save();
  }
  return this;
};

// Pre-save middleware
notificationSchema.pre('save', function(next) {
  // Set icon and color based on type and priority
  if (this.isNew) {
    this.setIconAndColor();
  }
  next();
});

// Method to set icon and color based on type and priority
notificationSchema.methods.setIconAndColor = function() {
  const typeConfig = {
    'organization_created': { icon: 'Building2', color: 'green' },
    'organization_updated': { icon: 'Building2', color: 'blue' },
    'organization_deleted': { icon: 'Building2', color: 'red' },
    'organization_restored': { icon: 'Building2', color: 'green' },
    'drone_created': { icon: 'Plane', color: 'green' },
    'drone_updated': { icon: 'Plane', color: 'blue' },
    'drone_deleted': { icon: 'Plane', color: 'red' },
    'drone_status_changed': { icon: 'Activity', color: 'orange' },
    'individual_created': { icon: 'User', color: 'green' },
    'individual_updated': { icon: 'User', color: 'blue' },
    'individual_deleted': { icon: 'User', color: 'red' },
    'user_created': { icon: 'UserPlus', color: 'green' },
    'user_updated': { icon: 'UserCheck', color: 'blue' },
    'system_alert': { icon: 'AlertTriangle', color: 'red' },
    'maintenance_due': { icon: 'Wrench', color: 'orange' },
    'battery_low': { icon: 'Battery', color: 'yellow' },
    'mission_completed': { icon: 'CheckCircle2', color: 'green' },
    'geofence_breach': { icon: 'MapPin', color: 'red' },
    'weather_alert': { icon: 'Cloud', color: 'purple' }
  };
  
  const config = typeConfig[this.type] || { icon: 'Bell', color: 'blue' };
  this.icon = config.icon;
  this.color = config.color;
  
  // Override color based on priority for critical alerts
  if (this.priority === 'critical') {
    this.color = 'red';
  }
};

module.exports = mongoose.model('Notification', notificationSchema);
