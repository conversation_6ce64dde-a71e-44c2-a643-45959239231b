import { Loader2, MapPin, Building, AlertCircle } from 'lucide-react';

// Skeleton loader for organization cards
export const OrganizationCardSkeleton = () => (
  <div className="bg-white/80 shadow border rounded-lg p-3 mb-4 animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
    <div className="flex items-center mb-2">
      <div className="w-4 h-4 bg-gray-200 rounded mr-2"></div>
      <div className="h-3 bg-gray-200 rounded w-1/3"></div>
    </div>
    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
  </div>
);

// Skeleton loader for organization list
export const OrganizationListSkeleton = () => (
  <div className="w-1/4 h-full bg-white/90 backdrop-blur-md p-4 overflow-y-auto shadow-md">
    <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
    {[1, 2, 3, 4, 5].map((i) => (
      <OrganizationCardSkeleton key={i} />
    ))}
  </div>
);

// Skeleton loader for drone list
export const DroneListSkeleton = () => (
  <div className="space-y-3">
    {[1, 2, 3, 4, 5].map((i) => (
      <div key={i} className="bg-white/80 shadow border rounded-lg p-2 animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
      </div>
    ))}
  </div>
);

// Map skeleton loader
export const MapSkeleton = () => (
  <div className="flex-1 bg-gray-200 animate-pulse relative">
    <div className="absolute inset-0 flex items-center justify-center">
      <div className="text-center">
        <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-500">Loading map...</p>
      </div>
    </div>
  </div>
);

// Full map view skeleton
export const MapViewSkeleton = () => (
  <div className="flex w-full h-screen overflow-hidden">
    <OrganizationListSkeleton />
    <MapSkeleton />
  </div>
);

// Organization details skeleton
export const OrganizationDetailsSkeleton = () => (
  <div className="w-1/4 h-full bg-white/90 backdrop-blur-md p-4 overflow-y-auto shadow-md animate-pulse">
    <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
    <div className="flex items-center mb-2">
      <div className="w-4 h-4 bg-gray-200 rounded mr-2"></div>
      <div className="h-4 bg-gray-200 rounded w-1/3"></div>
    </div>
    <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
    
    {/* Filter skeleton */}
    <div className="h-10 bg-gray-200 rounded w-full mb-4"></div>
    
    {/* Drone list title */}
    <div className="h-6 bg-gray-200 rounded w-1/2 mb-3"></div>
    
    {/* Drone list */}
    <DroneListSkeleton />
  </div>
);

// Loading spinner for map operations
export const MapLoadingSpinner = ({ text = 'Loading...' }) => (
  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg p-6 flex items-center gap-3 shadow-lg">
      <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
      <span className="text-gray-700 font-medium">{text}</span>
    </div>
  </div>
);

// Error display for map components
export const MapErrorDisplay = ({
  title = 'Failed to load map data',
  message = 'Please try again later',
  onRetry = null,
  icon = null
}) => (
  <div className="flex items-center justify-center h-full bg-white">
    <div className="text-center p-8 max-w-md">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        {icon || <AlertCircle className="w-8 h-8 text-red-600" />}
      </div>
      <h3 className="text-lg font-semibold text-black mb-2">{title}</h3>
      <p className="text-black mb-4">{message}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      )}
    </div>
  </div>
);

// Empty state for organization list
export const EmptyOrganizationList = ({ 
  title = 'No organizations found', 
  message = 'Try adjusting your search or filters' 
}) => (
  <div className="w-1/4 h-full bg-white/90 backdrop-blur-md p-4 flex items-center justify-center">
    <div className="text-center">
      <Building className="w-12 h-12 text-gray-600 mx-auto mb-3" />
      <h3 className="text-lg font-semibold text-black mb-2">{title}</h3>
      <p className="text-black text-sm">{message}</p>
    </div>
  </div>
);

// Empty state for drone list
export const EmptyDroneList = ({
  title = 'No drones found',
  message = 'No drones match the current filter'
}) => (
  <div className="text-center py-8">
    <MapPin className="w-12 h-12 text-gray-600 mx-auto mb-3" />
    <h3 className="text-lg font-semibold text-black mb-2">{title}</h3>
    <p className="text-black text-sm">{message}</p>
  </div>
);

// Search loading indicator
export const SearchLoadingIndicator = () => (
  <div className="absolute right-3 top-2.5">
    <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
  </div>
);

// Map overlay loading
export const MapOverlayLoading = ({ message = 'Updating map data...' }) => (
  <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-3 flex items-center gap-2 z-50">
    <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
    <span className="text-sm text-gray-700">{message}</span>
  </div>
);

// Connection status indicator
export const ConnectionStatus = ({ isConnected = true, lastUpdate = null }) => (
  <div className="absolute bottom-4 right-4 bg-white rounded-lg shadow-lg p-2 flex items-center gap-2 z-50">
    <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
    <span className="text-xs text-black font-medium">
      {isConnected ? 'Connected' : 'Disconnected'}
      {lastUpdate && ` • ${lastUpdate}`}
    </span>
  </div>
);

// Stats loading skeleton
export const MapStatsLoading = () => (
  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 p-4">
    {[1, 2, 3, 4].map((i) => (
      <div key={i} className="bg-white rounded-lg shadow p-4 animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-8 bg-gray-200 rounded w-1/2"></div>
      </div>
    ))}
  </div>
);
