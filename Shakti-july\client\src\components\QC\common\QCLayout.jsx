import React from 'react';
import QCSidebar from './QCSidebar';
import QCHeader from './QCHeader';

const QCLayout = ({ children, title, subtitle, actions }) => {
  return (
    <div className="flex h-screen bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
      <QCSidebar />

      {/* Main Content */}
      <main className="flex-1 ml-0 lg:ml-72 transition-all duration-300 flex flex-col h-full">
        <QCHeader title={title} subtitle={subtitle} actions={actions} />

        {/* Page Content */}
        <div className="flex-1 p-4 lg:p-6 overflow-y-auto">
          {children}
        </div>
      </main>
    </div>
  );
};

export default QCLayout;
