import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import { useNavigate } from "react-router-dom";
import {
  MapPin,
  Building,
  Phone,
  Mail,
  Calendar,
  Users,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react";
import mapDataService from './MapDataService';
import {
  OrganizationListSkeleton,
  MapSkeleton,
  EmptyOrganizationList,
  MapOverlayLoading,
  ConnectionStatus
} from './MapLoadingComponents';

// Create dynamic organization icon based on status
const createOrganizationIcon = (status, activeDrones, totalDrones) => {
  const colors = {
    active: '#10b981',
    pending: '#f59e0b',
    inactive: '#6b7280'
  };

  const color = colors[status] || colors.inactive;
  const percentage = totalDrones > 0 ? (activeDrones / totalDrones) * 100 : 0;

  return new L.DivIcon({
    html: `
      <div style="
        background-color: ${color};
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      ">
        <div style="
          width: 16px;
          height: 16px;
          background-color: white;
          border-radius: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
          <div style="
            width: 8px;
            height: 8px;
            background-color: ${color};
            border-radius: 1px;
          "></div>
        </div>
        ${percentage > 0 ? `
          <div style="
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 12px;
            height: 12px;
            background-color: ${percentage > 70 ? '#10b981' : percentage > 30 ? '#f59e0b' : '#ef4444'};
            border: 2px solid white;
            border-radius: 50%;
            font-size: 8px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
          ">${Math.round(percentage)}</div>
        ` : ''}
      </div>
    `,
    className: 'custom-org-icon',
    iconSize: [32, 32],
    iconAnchor: [16, 16],
    popupAnchor: [0, -16],
  });
};

const MapView = ({ searchQuery = '', filters = {} }) => {
  const navigate = useNavigate();
  const [organizations, setOrganizations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Load organizations data
  useEffect(() => {
    loadOrganizations();

    // Subscribe to organization updates
    const unsubscribe = mapDataService.subscribe('organizations', (updatedOrgs) => {
      setOrganizations(updatedOrgs);
      setLastUpdate(new Date().toLocaleTimeString());
    });

    return () => unsubscribe();
  }, []);

  // Handle search and filters
  useEffect(() => {
    if (searchQuery || filters.status !== 'all') {
      filterOrganizations();
    } else {
      loadOrganizations();
    }
  }, [searchQuery, filters]);

  const loadOrganizations = async () => {
    try {
      setIsLoading(true);
      const orgs = await mapDataService.fetchOrganizations(filters);
      setOrganizations(orgs);
      setLastUpdate(new Date().toLocaleTimeString());
    } catch (error) {
      console.error('Failed to load organizations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterOrganizations = async () => {
    setIsUpdating(true);
    try {
      let filteredOrgs = mapDataService.searchOrganizations(searchQuery);

      if (filters.status !== 'all') {
        filteredOrgs = filteredOrgs.filter(org => org.status === filters.status);
      }

      setOrganizations(filteredOrgs);
      setLastUpdate(new Date().toLocaleTimeString());
    } catch (error) {
      console.error('Failed to filter organizations:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleOrganizationClick = (org) => {
    setSelectedOrg(org);
    if (org.name === "Salam Kisan") {
      navigate("/orgstatsmap");
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'pending': return <Clock className="w-4 h-4 text-amber-600" />;
      case 'inactive': return <AlertCircle className="w-4 h-4 text-gray-600" />;
      default: return <Building className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50';
      case 'pending': return 'text-amber-600 bg-amber-50';
      case 'inactive': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (isLoading) {
    return (
      <div className="flex w-full h-full overflow-hidden">
        <OrganizationListSkeleton />
        <MapSkeleton />
      </div>
    );
  }

  if (organizations.length === 0) {
    return (
      <div className="flex w-full h-full overflow-hidden">
        <div className="w-full lg:w-1/4 h-1/3 lg:h-full bg-white/90 backdrop-blur-md p-4 flex items-center justify-center">
          <div className="text-center">
            <Building className="w-12 h-12 text-gray-600 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-black mb-2">No organizations found</h3>
            <p className="text-black text-sm">{searchQuery ? "Try adjusting your search query" : "No organizations available"}</p>
          </div>
        </div>
        <div className="flex-1 bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <MapPin className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <p className="text-black text-lg">Select an organization to view on map</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col lg:flex-row w-full h-full overflow-hidden relative">
      {/* Loading overlay */}
      {isUpdating && <MapOverlayLoading message="Updating organization data..." />}

      {/* Left Sidebar - Organization List */}
      <div className="w-full lg:w-1/4 h-1/3 lg:h-full bg-white/90 backdrop-blur-md p-3 sm:p-4 overflow-y-auto shadow-md text-left">
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <h2 className="text-lg sm:text-xl font-bold text-black">Organizations</h2>
          <span className="text-xs sm:text-sm text-black bg-white/80 px-2 py-1 rounded-full border">
            {organizations.length}
          </span>
        </div>

        <div className="space-y-2 sm:space-y-3">
          {organizations.map((org) => (
            <div
              key={org.id}
              className={`bg-white shadow border rounded-lg p-2 sm:p-3 text-black transition-all duration-200 cursor-pointer hover:scale-[1.02] hover:shadow-lg ${
                selectedOrg?.id === org.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
              }`}
              onClick={() => handleOrganizationClick(org)}
            >
              <div className="flex items-start justify-between mb-1 sm:mb-2">
                <h3 className="text-xs sm:text-sm font-semibold text-black truncate">{org.name}</h3>
                <div className={`px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-[10px] sm:text-xs font-medium ${getStatusColor(org.status)}`}>
                  {org.status}
                </div>
              </div>

              <p className="text-[10px] sm:text-xs text-black mb-1 sm:mb-2 truncate">{org.orgId}</p>

              <div className="flex items-center text-[10px] sm:text-xs text-black mb-1 sm:mb-2">
                <MapPin className="w-2.5 h-2.5 sm:w-3 sm:h-3 mr-1" />
                <span className="truncate">{org.state}, {org.district}</span>
              </div>

              <div className="flex items-center justify-between text-[10px] sm:text-xs">
                <div className="flex items-center text-black">
                  <Activity className="w-2.5 h-2.5 sm:w-3 sm:h-3 mr-1" />
                  <span>{org.activeDrones}/{org.totalDrones} Active</span>
                </div>
                <div className="flex items-center text-black">
                  <Calendar className="w-2.5 h-2.5 sm:w-3 sm:h-3 mr-1" />
                  {new Date(org.registrationDate).getFullYear()}
                </div>
              </div>

              {org.name === "Salam Kisan" && (
                <div className="mt-1 sm:mt-2 text-[10px] sm:text-xs text-blue-600 font-medium">
                  Click to view detailed map →
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Right Map Container */}
      <div className="flex-1 relative h-2/3 lg:h-full">
        <MapContainer
          center={[20.5937, 78.9629]}
          zoom={6}
          style={{ height: "100%", width: "100%" }}
          className="z-0"
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />

          {organizations.map((org) => (
            <Marker
              key={org.id}
              position={org.position}
              icon={createOrganizationIcon(org.status, org.activeDrones, org.totalDrones)}
              eventHandlers={{
                click: () => handleOrganizationClick(org),
              }}
            >
              <Popup className="custom-popup">
                <div className="p-2 min-w-[250px]">
                  <div className="flex items-center gap-2 mb-3">
                    {getStatusIcon(org.status)}
                    <h3 className="font-bold text-gray-800">{org.name}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(org.status)}`}>
                      {org.status}
                    </span>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Organization ID:</span>
                      <span className="font-medium">{org.orgId}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Location:</span>
                      <span className="font-medium">{org.state}, {org.district}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Drones:</span>
                      <span className="font-medium">{org.totalDrones}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Active Drones:</span>
                      <span className="font-medium text-green-600">{org.activeDrones}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Contact Person:</span>
                      <span className="font-medium">{org.contactPerson}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Phone className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-600">{org.phone}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Mail className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-600">{org.email}</span>
                    </div>
                  </div>

                  {org.name === "Salam Kisan" && (
                    <button
                      onClick={() => navigate("/orgstatsmap")}
                      className="w-full mt-3 px-3 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                    >
                      View Detailed Map
                    </button>
                  )}
                </div>
              </Popup>
            </Marker>
          ))}
        </MapContainer>

        {/* Connection Status */}
        <ConnectionStatus isConnected={true} lastUpdate={lastUpdate} />
      </div>
    </div>
  );
};

export default MapView;
