const axios = require('axios');

// Test Organization Portal Backend Integration
const BASE_URL = 'http://localhost:5000/api';

// Mock organization user token (you'll need to get a real token)
const ORG_TOKEN = 'your-org-token-here';

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${ORG_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

async function testOrganizationPortal() {
  console.log('🧪 Testing Organization Portal Backend Integration...\n');

  try {
   
    console.log('1️⃣ Testing Organization Initialization...');
    try {
      const initResponse = await api.post('/org-portal/initialize');
      console.log('✅ Organization initialized:', initResponse.data.success);
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.message?.includes('already initialized')) {
        console.log('✅ Organization already initialized');
      } else {
        console.log('❌ Initialization failed:', error.response?.data?.message || error.message);
      }
    }

    // Test 2: Get Dashboard Data
    console.log('\n2️⃣ Testing Dashboard Data...');
    try {
      const dashboardResponse = await api.get('/org-portal/dashboard');
      console.log('✅ Dashboard data retrieved:', dashboardResponse.data.success);
      console.log('   - Organization:', dashboardResponse.data.data?.organization?.name);
      console.log('   - Total Drones:', dashboardResponse.data.data?.statistics?.totalDrones || 0);
    } catch (error) {
      console.log('❌ Dashboard data failed:', error.response?.data?.message || error.message);
    }

    // Test 3: Get Drones List
    console.log('\n3️⃣ Testing Drones List...');
    try {
      const dronesResponse = await api.get('/org-portal/drones');
      console.log('✅ Drones list retrieved:', dronesResponse.data.success);
      console.log('   - Total Drones:', dronesResponse.data.data?.total || 0);
      console.log('   - Drones in response:', dronesResponse.data.data?.drones?.length || 0);
    } catch (error) {
      console.log('❌ Drones list failed:', error.response?.data?.message || error.message);
    }

    // Test 4: Add New Drone
    console.log('\n4️⃣ Testing Add Drone...');
    const testDroneData = {
      name: 'Test Drone Alpha',
      model: 'DJI Phantom 4 Pro',
      manufacturer: 'DJI',
      serialNumber: `TEST-${Date.now()}`,
      registrationNumber: `REG-TEST-${Date.now()}`,
      specifications: {
        type: 'quadcopter',
        weight: 1.4,
        maxPayload: 0.5,
        maxFlightTime: 30,
        maxRange: 7,
        maxAltitude: 120,
        maxSpeed: 72,
        batteryCapacity: 5870,
        cameraResolution: '4K',
        hasGimbal: true,
        hasGPS: true,
        hasObstacleAvoidance: true
      },
      purchase: {
        purchaseDate: new Date().toISOString(),
        purchasePrice: 1500,
        vendor: 'DJI Store'
      },
      currentLocation: {
        latitude: 19.0760,
        longitude: 72.8777,
        altitude: 0
      }
    };

    try {
      const addDroneResponse = await api.post('/org-portal/drones', testDroneData);
      console.log('✅ Drone added successfully:', addDroneResponse.data.success);
      console.log('   - Drone ID:', addDroneResponse.data.data?.drone?._id);
      console.log('   - Drone Name:', addDroneResponse.data.data?.drone?.name);

      // Test 5: Get Specific Drone
      if (addDroneResponse.data.data?.drone?._id) {
        console.log('\n5️⃣ Testing Get Specific Drone...');
        try {
          const droneId = addDroneResponse.data.data.drone._id;
          const getDroneResponse = await api.get(`/org-portal/drones/${droneId}`);
          console.log('✅ Specific drone retrieved:', getDroneResponse.data.success);
          console.log('   - Drone Name:', getDroneResponse.data.data?.drone?.name);
        } catch (error) {
          console.log('❌ Get specific drone failed:', error.response?.data?.message || error.message);
        }

        // Test 6: Update Drone Status
        console.log('\n6️⃣ Testing Update Drone Status...');
        try {
          const droneId = addDroneResponse.data.data.drone._id;
          const updateStatusResponse = await api.patch(`/org-portal/drones/${droneId}/status`, {
            status: 'maintenance'
          });
          console.log('✅ Drone status updated:', updateStatusResponse.data.success);
        } catch (error) {
          console.log('❌ Update drone status failed:', error.response?.data?.message || error.message);
        }

        // Test 7: Add Drone Note
        console.log('\n7️⃣ Testing Add Drone Note...');
        try {
          const droneId = addDroneResponse.data.data.drone._id;
          const addNoteResponse = await api.post(`/org-portal/drones/${droneId}/notes`, {
            content: 'Test note added via API',
            type: 'general'
          });
          console.log('✅ Drone note added:', addNoteResponse.data.success);
        } catch (error) {
          console.log('❌ Add drone note failed:', error.response?.data?.message || error.message);
        }
      }
    } catch (error) {
      console.log('❌ Add drone failed:', error.response?.data?.message || error.message);
    }

    // Test 8: Get Statistics
    console.log('\n8️⃣ Testing Statistics...');
    try {
      const statsResponse = await api.get('/org-portal/statistics');
      console.log('✅ Statistics retrieved:', statsResponse.data.success);
      console.log('   - Total Drones:', statsResponse.data.data?.totalDrones || 0);
      console.log('   - Active Drones:', statsResponse.data.data?.activeDrones || 0);
    } catch (error) {
      console.log('❌ Statistics failed:', error.response?.data?.message || error.message);
    }

    // Test 9: Get Analytics
    console.log('\n9️⃣ Testing Analytics...');
    try {
      const analyticsResponse = await api.get('/org-portal/analytics');
      console.log('✅ Analytics retrieved:', analyticsResponse.data.success);
    } catch (error) {
      console.log('❌ Analytics failed:', error.response?.data?.message || error.message);
    }

    // Test 10: Get Map Data
    console.log('\n🔟 Testing Map Data...');
    try {
      const mapDataResponse = await api.get('/org-portal/map-data');
      console.log('✅ Map data retrieved:', mapDataResponse.data.success);
      console.log('   - Drones on map:', mapDataResponse.data.data?.drones?.length || 0);
    } catch (error) {
      console.log('❌ Map data failed:', error.response?.data?.message || error.message);
    }

    console.log('\n🎉 Organization Portal Backend Integration Test Complete!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Helper function to get organization token (you'll need to implement this)
async function getOrganizationToken() {
  try {
    // This is a placeholder - you'll need to implement actual login
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    if (loginResponse.data.success) {
      return loginResponse.data.data.token;
    }
    throw new Error('Login failed');
  } catch (error) {
    console.log('❌ Failed to get organization token:', error.message);
    console.log('⚠️  Please update the ORG_TOKEN variable with a valid token');
    return null;
  }
}

// Run the test
if (require.main === module) {
  console.log('🚀 Starting Organization Portal Backend Test...');
  console.log('⚠️  Make sure the server is running on http://localhost:5000');
  console.log('⚠️  Update the ORG_TOKEN variable with a valid organization token\n');
  
  testOrganizationPortal().catch(console.error);
}

module.exports = { testOrganizationPortal };
