/* Professional OrgMap Styling */

/* Custom Drone Icon Styling */
.custom-drone-icon {
  background: transparent !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  border-radius: 50% !important;
}

/* Enhanced Popup Styling */
.custom-popup .leaflet-popup-content-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0;
}

.custom-popup .leaflet-popup-content {
  margin: 0;
  padding: 0;
  border-radius: 12px;
  overflow: hidden;
}

.custom-popup .leaflet-popup-tip {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Map Container Enhancements */
.leaflet-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Professional Scrollbar for Drone List */
.drone-list-container {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E0 #F7FAFC;
}

.drone-list-container::-webkit-scrollbar {
  width: 6px;
}

.drone-list-container::-webkit-scrollbar-track {
  background: #F7FAFC;
  border-radius: 3px;
}

.drone-list-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #CBD5E0, #A0AEC0);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.drone-list-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #A0AEC0, #718096);
}

/* Floating Controls Animation */
.floating-controls {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Drone Card Hover Effects */
.drone-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drone-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Status Badge Animations */
.status-badge {
  position: relative;
  overflow: hidden;
}

.status-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.status-badge:hover::before {
  left: 100%;
}

/* Progress Bar Enhancements */
.progress-bar {
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Map Controls Styling */
.map-controls {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
}

/* Live Indicator Pulse */
.live-indicator {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .drone-list-sidebar {
    width: 20rem;
  }
}

@media (max-width: 768px) {
  .drone-list-sidebar {
    width: 18rem;
  }
  
  .floating-controls {
    scale: 0.9;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .custom-popup .leaflet-popup-content-wrapper {
    background: #1f2937;
    color: white;
  }
  
  .custom-popup .leaflet-popup-tip {
    background: #1f2937;
  }
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Signal Strength Indicator */
.signal-bars {
  display: flex;
  gap: 1px;
  align-items: flex-end;
}

.signal-bar {
  width: 2px;
  background: currentColor;
  border-radius: 1px;
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.signal-bar.active {
  opacity: 1;
}

.signal-bar:nth-child(1) { height: 4px; }
.signal-bar:nth-child(2) { height: 6px; }
.signal-bar:nth-child(3) { height: 8px; }
.signal-bar:nth-child(4) { height: 10px; }
.signal-bar:nth-child(5) { height: 12px; }
