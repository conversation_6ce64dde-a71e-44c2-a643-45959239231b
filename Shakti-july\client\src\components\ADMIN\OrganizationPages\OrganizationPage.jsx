import React, { useEffect, useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell, LineChart, Line } from 'recharts';
// EditOrganizationModal component will be defined inline

import {
    LayoutDashboard,
    MapPinned,
    Building,
    Boxes,
    Truck,
    Bot,
    Bell,
    LogOut,
    Eye,
    Edit2,
    Trash2,
    X,
    Check,
    RotateCw,
    Search,
    Filter,
    Download,
    Plus,
    TrendingUp,
    Users,
    Activity,
    Calendar,
    MapPin,
    Phone,
    Mail,
    Globe,
    ChevronDown,
    ChevronUp,
    MoreVertical
} from 'lucide-react';
import AdminSidebar from '../common/AdminSidebar';
import organizationService from '../../../services/organizationService';

const OrganizationPage = () => {
    const navigate = useNavigate();

    // Enhanced state management
    const [orgs, setOrgs] = useState([]);
    const [pendingOrgs, setPendingOrgs] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [stats, setStats] = useState({
        totalOrganizations: 0,
        activeOrganizations: 0,
        pendingOrganizations: 0,
        verifiedOrganizations: 0
    });

    // Additional state for UI controls
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('All');
    const [stateFilter, setStateFilter] = useState('All');
    const [sortBy, setSortBy] = useState('name');
    const [sortOrder, setSortOrder] = useState('asc');
    const [selectedOrg, setSelectedOrg] = useState(null);
    const [showFilters, setShowFilters] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [editingOrg, setEditingOrg] = useState(null);
    const [showEditModal, setShowEditModal] = useState(false);

    // Fetch organizations data
    useEffect(() => {
        fetchOrganizations();
        fetchStats();
    }, []);

    const fetchOrganizations = async () => {
        try {
            setLoading(true);
            const response = await organizationService.getAllOrganizations({
                page: currentPage,
                limit: itemsPerPage,
                sortBy: sortBy,
                sortOrder: sortOrder,
                search: searchTerm,
                status: statusFilter !== 'All' ? statusFilter.toLowerCase() : undefined
            });

            if (response.success) {
                const organizations = response.data.organizations.map(org => ({
                    id: org._id,
                    org: org.name,
                    state: org.address?.state || 'N/A',
                    registered: new Date(org.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    }),
                    drones: org.allocatedDrones || 0,
                    status: org.isDeleted ? `Deleted (${org.daysUntilPermanentDelete || 0} days left)` : (org.isVerified ? 'Approved' : (org.status === 'suspended' ? 'Rejected' : 'Pending')),
                    contact: org.contact?.primaryEmail || 'N/A',
                    phone: org.contact?.phone || 'N/A',
                    website: org.contact?.website || 'N/A',
                    address: `${org.address?.city || ''}, ${org.address?.state || ''}`.trim().replace(/^,|,$/, '') || 'N/A',
                    category: org.type || 'N/A',
                    activeDeployments: org.statistics?.activeDrones || 0,
                    totalMissions: org.statistics?.totalFlightHours || 0,
                    // Soft delete fields
                    isDeleted: org.isDeleted || false,
                    deletedAt: org.deletedAt,
                    permanentDeleteAt: org.permanentDeleteAt,
                    daysUntilPermanentDelete: org.daysUntilPermanentDelete,
                    canRestore: org.canRestore,
                    deleteTimer: org.deleteTimer,
                    rawData: org // Keep original data for detailed view
                }));

                setOrgs(organizations);

                // Separate pending organizations
                const pending = organizations.filter(org => org.status === 'Pending');
                setPendingOrgs(pending.map(org => ({
                    id: org.id,
                    name: org.org,
                    state: org.state,
                    date: org.registered,
                    drones: org.drones,
                    contact: org.contact,
                    category: org.category,
                    documents: ["License", "Insurance", "Registration"] // Mock for now
                })));
            }
        } catch (error) {
            console.error('Error fetching organizations:', error);
            setError(error.message);
        } finally {
            setLoading(false);
        }
    };

    const fetchStats = async () => {
        try {
            const response = await organizationService.getOrganizationStats();
            if (response.success) {
                // Handle both direct data and nested stats structure
                const statsData = response.data.stats || response.data;
                setStats(statsData);
            }
        } catch (error) {
            console.error('Error fetching stats:', error);
        }
    };

    // Analytics data
    const analyticsData = useMemo(() => {
        const totalOrgs = stats.totalOrganizations || orgs.length;
        const approvedOrgs = stats.verifiedOrganizations || orgs.filter(org => org.status === 'Approved').length;
        const rejectedOrgs = orgs.filter(org => org.status === 'Rejected').length;
        const pendingCount = stats.pendingOrganizations || pendingOrgs.length;
        const totalDrones = stats.totalDrones || orgs.reduce((sum, org) => sum + org.drones, 0);
        const activeDrones = orgs.reduce((sum, org) => sum + (org.activeDeployments || 0), 0);
        const allocatedDrones = stats.totalAllocatedDrones || 0;

        return {
            totalOrgs,
            approvedOrgs,
            rejectedOrgs,
            pendingCount,
            totalDrones,
            activeDrones,
            allocatedDrones,
            approvalRate: totalOrgs > 0 ? ((approvedOrgs / totalOrgs) * 100).toFixed(1) : 0
        };
    }, [orgs, pendingOrgs, stats]);

    // Organization action handlers
    const handleApproveOrg = async (orgId) => {
        try {
            await organizationService.verifyOrganization(orgId);
            await fetchOrganizations(); // Refresh data
            await fetchStats();
        } catch (error) {
            console.error('Error approving organization:', error);
            setError(error.message);
        }
    };

    const handleRejectOrg = async (orgId) => {
        try {
            await organizationService.updateOrganizationStatus(orgId, 'suspended');
            await fetchOrganizations(); // Refresh data
            await fetchStats();
        } catch (error) {
            console.error('Error rejecting organization:', error);
            setError(error.message);
        }
    };

    const handleDeleteOrg = async (orgId) => {
        try {
            const response = await organizationService.deleteOrganization(orgId);

            // Refresh data to show the updated organization with delete timer
            await fetchOrganizations();
            await fetchStats();
        } catch (error) {
            console.error('Error deleting organization:', error);
            setError(error.message);
        }
    };

    const handleEditOrg = (org) => {
        setEditingOrg(org);
        setShowEditModal(true);
    };

    const handleUpdateOrg = async (updatedData) => {
        try {
            await organizationService.updateOrganization(editingOrg._id, updatedData);
            setShowEditModal(false);
            setEditingOrg(null);
            await fetchOrganizations();
            await fetchStats();
        } catch (error) {
            console.error('Error updating organization:', error);
            setError(error.message);
        }
    };

    const handleRestoreOrg = async (orgId) => {
        if (window.confirm('Are you sure you want to restore this organization? It will be reactivated immediately.')) {
            try {
                await organizationService.restoreOrganization(orgId);

                // Show success message
                alert('Organization restored successfully! It is now active again.');

                // Refresh data to show the restored organization
                await fetchOrganizations();
                await fetchStats();
            } catch (error) {
                console.error('Error restoring organization:', error);
                setError(error.message);
            }
        }
    };

    // Refresh data when filters change
    useEffect(() => {
        if (!loading) {
            fetchOrganizations();
        }
    }, [currentPage, sortBy, sortOrder, searchTerm, statusFilter]);

    const topOrg = useMemo(() => {
        const topPerformer = orgs
            .filter(org => org.status === 'Approved')
            .sort((a, b) => (b.totalMissions || 0) - (a.totalMissions || 0))[0];

        return topPerformer ? {
            name: topPerformer.org,
            deployments: topPerformer.activeDeployments || 0,
            totalMissions: topPerformer.totalMissions || 0,
            regions: [
                { name: 'Active', value: topPerformer.activeDeployments || 0 },
                { name: 'Completed', value: (topPerformer.totalMissions || 0) - (topPerformer.activeDeployments || 0) },
                { name: 'Total Drones', value: topPerformer.drones },
            ]
        } : null;
    }, [orgs]);

    // Enhanced filtering and sorting logic
    const filteredAndSortedOrgs = useMemo(() => {
        let filtered = orgs.filter(org => {
            const matchesSearch = org.org.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                org.state.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                org.category.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesStatus = statusFilter === 'All' || org.status === statusFilter;
            const matchesState = stateFilter === 'All' || org.state === stateFilter;

            return matchesSearch && matchesStatus && matchesState;
        });

        // Sort the filtered results
        filtered.sort((a, b) => {
            let aValue, bValue;

            switch (sortBy) {
                case 'name':
                    aValue = a.org.toLowerCase();
                    bValue = b.org.toLowerCase();
                    break;
                case 'state':
                    aValue = a.state.toLowerCase();
                    bValue = b.state.toLowerCase();
                    break;
                case 'drones':
                    aValue = a.drones;
                    bValue = b.drones;
                    break;
                case 'registered':
                    aValue = new Date(a.registered);
                    bValue = new Date(b.registered);
                    break;
                case 'missions':
                    aValue = a.totalMissions || 0;
                    bValue = b.totalMissions || 0;
                    break;
                default:
                    aValue = a.org.toLowerCase();
                    bValue = b.org.toLowerCase();
            }

            if (sortOrder === 'asc') {
                return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
            } else {
                return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
            }
        });

        return filtered;
    }, [orgs, searchTerm, statusFilter, stateFilter, sortBy, sortOrder]);

    // Pagination logic
    const paginatedOrgs = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        return filteredAndSortedOrgs.slice(startIndex, startIndex + itemsPerPage);
    }, [filteredAndSortedOrgs, currentPage, itemsPerPage]);

    const totalPages = Math.ceil(filteredAndSortedOrgs.length / itemsPerPage);

    // Get unique states for filter dropdown
    const uniqueStates = useMemo(() => {
        return [...new Set(orgs.map(org => org.state))].sort();
    }, [orgs]);

    // Timer logic is now handled by the backend - organizations are fetched with delete timer info

    const handleApprove = (orgId) => {
        const approvedOrg = pendingOrgs.find(org => org.id === orgId);
        if (approvedOrg) {
            const newOrg = {
                id: approvedOrg.id,
                org: approvedOrg.name,
                state: approvedOrg.state,
                registered: approvedOrg.date,
                drones: approvedOrg.drones || 0,
                status: 'Approved',
                contact: approvedOrg.contact,
                category: approvedOrg.category,
                activeDeployments: 0,
                totalMissions: 0
            };
            setOrgs(prev => [...prev, newOrg]);
            setPendingOrgs(prev => prev.filter(org => org.id !== orgId));
        }
    };

    const handleReject = (orgId) => {
        const rejectedOrg = pendingOrgs.find(org => org.id === orgId);
        if (rejectedOrg) {
            const newOrg = {
                id: rejectedOrg.id,
                org: rejectedOrg.name,
                state: rejectedOrg.state,
                registered: rejectedOrg.date,
                drones: rejectedOrg.drones || 0,
                status: 'Rejected',
                contact: rejectedOrg.contact,
                category: rejectedOrg.category,
                activeDeployments: 0,
                totalMissions: 0
            };
            setOrgs(prev => [...prev, newOrg]);
            setPendingOrgs(prev => prev.filter(org => org.id !== orgId));
        }
    };

    const handleSort = (field) => {
        if (sortBy === field) {
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
        } else {
            setSortBy(field);
            setSortOrder('asc');
        }
    };

    // Loading state
    if (loading) {
        return (
            <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
                <AdminSidebar />
                <div className="lg:pl-[250px] w-full">
                    <div className="flex items-center justify-center min-h-screen">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p className="text-gray-600">Loading organizations...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
                <AdminSidebar />
                <div className="lg:pl-[250px] w-full">
                    <div className="flex items-center justify-center min-h-screen">
                        <div className="text-center">
                            <div className="text-red-500 text-6xl mb-4">⚠️</div>
                            <h2 className="text-2xl font-bold text-gray-800 mb-2">Error Loading Data</h2>
                            <p className="text-gray-600 mb-4">{error}</p>
                            <button
                                onClick={() => {
                                    setError(null);
                                    fetchOrganizations();
                                    fetchStats();
                                }}
                                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
            <AdminSidebar />

            {/* Main Content */}
            <div className="lg:pl-[250px] w-full">
                {/* Header Section */}
                <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        <div>
                            <h2 className="text-2xl font-bold text-gray-900 text-left flex items-center gap-3">
                                <Building className="text-blue-600" />
                                Organizations
                            </h2>
                            <p className="text-gray-600 mt-1">Manage and monitor all registered organizations</p>
                        </div>
                        <div className="flex items-center gap-3">
                            <button
                                onClick={() => {
                                    console.log('OrganizationPage: Navigating to organization form');
                                    navigate('/organizationform');
                                }}
                                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                <Plus size={18} />
                                <span className="hidden sm:inline">Add Organization</span>
                            </button>
                            <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                <Download size={18} />
                                <span className="hidden sm:inline">Export</span>
                            </button>
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                                <Bell size={18} />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Analytics Cards */}
                <div className="px-4 lg:px-6 py-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Organizations</p>
                                    <p className="text-3xl font-bold text-gray-900 mt-2">{analyticsData.totalOrgs}</p>
                                </div>
                                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <Building className="w-6 h-6 text-blue-600" />
                                </div>
                            </div>
                            <div className="flex items-center mt-4 text-sm">
                                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                                <span className="text-green-600 font-medium">+12%</span>
                                <span className="text-gray-500 ml-1">from last month</span>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Approved</p>
                                    <p className="text-3xl font-bold text-green-600 mt-2">{analyticsData.approvedOrgs}</p>
                                </div>
                                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <Check className="w-6 h-6 text-green-600" />
                                </div>
                            </div>
                            <div className="flex items-center mt-4 text-sm">
                                <span className="text-gray-500">Approval Rate: </span>
                                <span className="text-green-600 font-medium ml-1">{analyticsData.approvalRate}%</span>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Pending Approval</p>
                                    <p className="text-3xl font-bold text-orange-600 mt-2">{analyticsData.pendingCount}</p>
                                </div>
                                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <Activity className="w-6 h-6 text-orange-600" />
                                </div>
                            </div>
                            <div className="flex items-center mt-4 text-sm">
                                <span className="text-gray-500">Requires attention</span>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Drones</p>
                                    <p className="text-3xl font-bold text-purple-600 mt-2">{analyticsData.totalDrones}</p>
                                </div>
                                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <Bot className="w-6 h-6 text-purple-600" />
                                </div>
                            </div>
                            <div className="flex items-center mt-4 text-sm">
                                <span className="text-gray-500">Active: </span>
                                <span className="text-purple-600 font-medium ml-1">{analyticsData.activeDrones}</span>
                            </div>
                        </div>

                        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Allocated Drones</p>
                                    <p className="text-3xl font-bold text-indigo-600 mt-2">{analyticsData.allocatedDrones || 0}</p>
                                </div>
                                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                                    <Bot className="w-6 h-6 text-indigo-600" />
                                </div>
                            </div>
                            <div className="flex items-center mt-4 text-sm">
                                <span className="text-gray-500">To Organizations & Individuals</span>
                            </div>
                        </div>
                    </div>

                    {/* Search and Filter Section */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6 text-black">
                        <div className="flex flex-col lg:flex-row gap-4">
                            <div className="flex-1 relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                <input
                                    type="text"
                                    placeholder="Search organizations, states, or categories..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                            <div className="flex flex-wrap gap-2">
                                <select
                                    value={statusFilter}
                                    onChange={(e) => setStatusFilter(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    <option value="All">All Status</option>
                                    <option value="Approved">Approved</option>
                                    <option value="Rejected">Rejected</option>
                                </select>
                                <select
                                    value={stateFilter}
                                    onChange={(e) => setStateFilter(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                    <option value="All">All States</option>
                                    {uniqueStates.map(state => (
                                        <option key={state} value={state}>{state}</option>
                                    ))}
                                </select>
                                <button
                                    onClick={() => setShowFilters(!showFilters)}
                                    className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                                >
                                    <Filter size={16} />
                                    Filters
                                    {showFilters ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                                </button>
                            </div>
                        </div>

                        {showFilters && (
                            <div className="mt-4 pt-4 border-t border-gray-200">
                                <div className="flex flex-wrap gap-4">
                                    <div className="flex items-center gap-2">
                                        <label className="text-sm font-medium text-gray-700">Sort by:</label>
                                        <select
                                            value={sortBy}
                                            onChange={(e) => setSortBy(e.target.value)}
                                            className="px-3 py-1 border border-gray-300 rounded text-sm"
                                        >
                                            <option value="name">Name</option>
                                            <option value="state">State</option>
                                            <option value="drones">Drones</option>
                                            <option value="registered">Date Registered</option>
                                            {/* <option value="missions">Total Missions</option> */}
                                        </select>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <label className="text-sm font-medium text-gray-700">Order:</label>
                                        <select
                                            value={sortOrder}
                                            onChange={(e) => setSortOrder(e.target.value)}
                                            className="px-3 py-1 border border-gray-300 rounded text-sm"
                                        >
                                            <option value="asc">Ascending</option>
                                            <option value="desc">Descending</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Organizations Table */}
                    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <div className="flex items-center justify-between">
                                <h2 className="text-lg font-semibold text-gray-900">
                                    Organizations ({filteredAndSortedOrgs.length})
                                </h2>
                                <div className="text-sm text-gray-500">
                                    Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredAndSortedOrgs.length)} of {filteredAndSortedOrgs.length} results
                                </div>
                            </div>
                        </div>

                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onClick={() => handleSort('name')}>
                                            <div className="flex items-center gap-1">
                                                Organization Name
                                                {sortBy === 'name' && (sortOrder === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />)}
                                            </div>
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onClick={() => handleSort('state')}>
                                            <div className="flex items-center gap-1">
                                                State
                                                {sortBy === 'state' && (sortOrder === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />)}
                                            </div>
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onClick={() => handleSort('drones')}>
                                            <div className="flex items-center gap-1">
                                                Drones
                                                {sortBy === 'drones' && (sortOrder === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />)}
                                            </div>
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        {/* <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onClick={() => handleSort('missions')}>
                                            <div className="flex items-center gap-1">
                                                Missions
                                                {sortBy === 'missions' && (sortOrder === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />)}
                                            </div>
                                        </th> */}
                                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider text-left">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {paginatedOrgs.map((org) => (
                                        <tr key={org.id} className="hover:bg-gray-50 transition-colors">
                                            <td className="px-6 py-4 whitespace-nowrap text-left">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0 h-10 w-10">
                                                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                            <Building className="h-5 w-5 text-blue-600" />
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">{org.org}</div>
                                                        <div className="text-sm text-gray-500">{org.contact}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-left">
                                                <div className="flex items-center">
                                                    <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                                                    <span className="text-sm text-gray-900">{org.state}</span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-left">
                                                <span className="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                                    {org.category}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-left text-sm text-gray-900">
                                                <div className="flex items-center">
                                                    <Bot className="h-4 w-4 text-gray-400 mr-1" />
                                                    {org.drones}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-left">
                                                <span className={`inline-block px-2 py-1 text-xs font-semibold rounded-full ${
                                                    org.status.startsWith('Deleted')
                                                        ? 'bg-orange-100 text-orange-800'
                                                        : org.status === 'Approved'
                                                        ? 'bg-green-100 text-green-800'
                                                        : org.status === 'Rejected'
                                                        ? 'bg-red-100 text-red-800'
                                                        : 'bg-yellow-100 text-yellow-800'
                                                }`}>
                                                    {org.status}
                                                </span>
                                            </td>
                                            {/* <td className="px-6 py-4 whitespace-nowrap text-left text-sm text-gray-900">
                                                {org.totalMissions || 0}
                                            </td> */}
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div className="flex items-center justify-end gap-2">
                                                    {org.isDeleted && (org.canRestore || org.daysUntilPermanentDelete > 0) ? (
                                                        <>
                                                            <span className="text-xs text-red-600 font-medium bg-red-50 px-2 py-1 rounded">
                                                                ⏰ {org.hoursUntilPermanentDelete || Math.max(0, Math.floor(org.daysUntilPermanentDelete * 24))} hours
                                                            </span>
                                                            <button
                                                                onClick={() => handleRestoreOrg(org.id)}
                                                                className="text-green-600 hover:text-green-800 p-1 rounded"
                                                                title="Retrieve Organization"
                                                            >
                                                                <RotateCw size={16} />
                                                            </button>
                                                        </>
                                                    ) : org.isDeleted ? (
                                                        <span className="text-xs text-gray-500 font-medium">
                                                            Permanently deleted
                                                        </span>
                                                    ) : (
                                                        <>
                                                            <button
                                                                onClick={() => setSelectedOrg(org)}
                                                                className="text-blue-600 hover:text-blue-900 p-1 rounded"
                                                                title="View Details"
                                                            >
                                                                <Eye size={16} />
                                                            </button>
                                                            <button
                                                                onClick={() => handleEditOrg(org)}
                                                                className="text-gray-600 hover:text-gray-900 p-1 rounded"
                                                                title="Edit"
                                                            >
                                                                <Edit2 size={16} />
                                                            </button>
                                                            <button
                                                                onClick={() => handleDeleteOrg(org.id)}
                                                                className="text-red-600 hover:text-red-900 p-1 rounded"
                                                                title="Delete"
                                                            >
                                                                <Trash2 size={16} />
                                                            </button>
                                                        </>
                                                    )}
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                            <div className="px-6 py-4 border-t border-gray-200">
                                <div className="flex items-center justify-between">
                                    <div className="text-sm text-gray-700">
                                        Page {currentPage} of {totalPages}
                                    </div>
                                    <div className="flex gap-2">
                                        <button
                                            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                            disabled={currentPage === 1}
                                            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                                        >
                                            Previous
                                        </button>
                                        <button
                                            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                            disabled={currentPage === totalPages}
                                            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                                        >
                                            Next
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Bottom Section - Pending Approvals and Analytics */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
                        {/* Pending Approvals */}
                        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center justify-between mb-6">
                                <h2 className="text-lg font-semibold text-gray-900">Pending Approvals</h2>
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                                    {pendingOrgs.length} Pending
                                </span>
                            </div>

                            {pendingOrgs.length === 0 ? (
                                <div className="text-center py-8">
                                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <Check className="w-8 h-8 text-gray-400" />
                                    </div>
                                    <p className="text-gray-500">No pending approvals</p>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {pendingOrgs.map(org => (
                                        <div key={org.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                                            <Building className="w-5 h-5 text-orange-600" />
                                                        </div>
                                                        <div>
                                                            <h3 className="font-semibold text-gray-900">{org.name}</h3>
                                                            <p className="text-sm text-gray-500">{org.contact}</p>
                                                        </div>
                                                    </div>
                                                    <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                                                        <div className="flex items-center gap-1">
                                                            <MapPin className="w-4 h-4" />
                                                            {org.state}
                                                        </div>
                                                        <div className="flex items-center gap-1">
                                                            <Calendar className="w-4 h-4" />
                                                            {org.date}
                                                        </div>
                                                        <div className="flex items-center gap-1">
                                                            <Bot className="w-4 h-4" />
                                                            {org.drones} drones
                                                        </div>
                                                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                                            {org.category}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="flex gap-2">
                                                    <button
                                                        onClick={() => handleApproveOrg(org.id)}
                                                        className="flex items-center gap-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                                                    >
                                                        <Check size={16} />
                                                        Approve
                                                    </button>
                                                    <button
                                                        onClick={() => handleRejectOrg(org.id)}
                                                        className="flex items-center gap-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                                                    >
                                                        <X size={16} />
                                                        Reject
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>

                        {/* Top Organization Analytics */}
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center justify-between mb-6">
                                <h2 className="text-lg font-semibold text-gray-900">Top Performer</h2>
                                <TrendingUp className="w-5 h-5 text-green-500" />
                            </div>

                            {topOrg ? (
                                <div>
                                    <div className="text-center mb-6">
                                        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                            <Building className="w-8 h-8 text-blue-600" />
                                        </div>
                                        <h3 className="font-semibold text-gray-900">{topOrg.name}</h3>
                                        <p className="text-sm text-gray-500">{topOrg.totalMissions} total missions</p>
                                    </div>

                                    <div className="space-y-3 mb-6">
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-gray-600">Active Deployments</span>
                                            <span className="font-semibold text-gray-900">{topOrg.deployments}</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-gray-600">Success Rate</span>
                                            <span className="font-semibold text-green-600">98.5%</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-gray-600">Avg. Response Time</span>
                                            <span className="font-semibold text-gray-900">2.3 hrs</span>
                                        </div>
                                    </div>

                                    <div className="h-48">
                                        <ResponsiveContainer width="100%" height="100%">
                                            <BarChart data={topOrg.regions} layout="vertical" margin={{ left: 20, right: 20 }}>
                                                <XAxis type="number" hide />
                                                <YAxis dataKey="name" type="category" width={60} tick={{ fontSize: 12 }} />
                                                <Tooltip
                                                    contentStyle={{
                                                        backgroundColor: '#f8fafc',
                                                        border: '1px solid #e2e8f0',
                                                        borderRadius: '8px'
                                                    }}
                                                />
                                                <Bar dataKey="value" fill="#3b82f6" radius={[0, 4, 4, 0]} />
                                            </BarChart>
                                        </ResponsiveContainer>
                                    </div>
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <Activity className="w-8 h-8 text-gray-400" />
                                    </div>
                                    <p className="text-gray-500">No data available</p>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Organization Detail Popup Modal */}
                    {selectedOrg && (
                        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" onClick={() => setSelectedOrg(null)}>
                            <div
                                className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-100"
                                onClick={(e) => e.stopPropagation()}
                            >
                                {/* Modal Header */}
                                <div className="sticky top-0 bg-white p-6 border-b border-gray-200 rounded-t-xl">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                                <Building className="w-6 h-6 text-blue-600" />
                                            </div>
                                            <div>
                                                <h2 className="text-xl font-semibold text-gray-900">{selectedOrg.org}</h2>
                                                <p className="text-sm text-gray-500">Organization Details</p>
                                            </div>
                                        </div>
                                        <button
                                            onClick={() => setSelectedOrg(null)}
                                            className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200 group"
                                            title="Close"
                                        >
                                            <X size={20} className="text-gray-500 group-hover:text-gray-700" />
                                        </button>
                                    </div>
                                </div>

                                {/* Modal Content */}
                                <div className="p-6 text-black">
                                    {/* Status Badge */}
                                    <div className="mb-6">
                                        <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                                            selectedOrg.status === 'Approved'
                                                ? 'bg-green-100 text-green-800'
                                                : selectedOrg.status === 'Rejected'
                                                ? 'bg-red-100 text-red-800'
                                                : 'bg-yellow-100 text-yellow-800'
                                        }`}>
                                            {selectedOrg.status}
                                        </span>
                                    </div>

                                    {/* Main Content Grid */}
                                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                        {/* Basic Information */}
                                        <div className="space-y-6">
                                            <div>
                                                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                                                    <Building className="w-5 h-5 text-blue-600" />
                                                    Basic Information
                                                </h3>
                                                <div className="space-y-4">
                                                    <div className="bg-gray-50 p-4 rounded-lg">
                                                        <label className="text-sm font-medium text-gray-500 block mb-1">Organization Name</label>
                                                        <p className="text-gray-900 font-medium">{selectedOrg.org}</p>
                                                    </div>
                                                    <div className="bg-gray-50 p-4 rounded-lg">
                                                        <label className="text-sm font-medium text-gray-500 block mb-1">State</label>
                                                        <p className="text-gray-900 font-medium">{selectedOrg.state}</p>
                                                    </div>
                                                    <div className="bg-gray-50 p-4 rounded-lg">
                                                        <label className="text-sm font-medium text-gray-500 block mb-1">Category</label>
                                                        <p className="text-gray-900 font-medium">{selectedOrg.category}</p>
                                                    </div>
                                                    <div className="bg-gray-50 p-4 rounded-lg">
                                                        <label className="text-sm font-medium text-gray-500 block mb-1">Registration Date</label>
                                                        <p className="text-gray-900 font-medium">{selectedOrg.registered}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Contact Information */}
                                        <div className="space-y-6">
                                            <div>
                                                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                                                    <Phone className="w-5 h-5 text-green-600" />
                                                    Contact Information
                                                </h3>
                                                <div className="space-y-4">
                                                    <div className="bg-gray-50 p-4 rounded-lg">
                                                        <div className="flex items-center gap-3">
                                                            <Mail className="w-5 h-5 text-blue-500" />
                                                            <div>
                                                                <label className="text-sm font-medium text-gray-500 block">Email</label>
                                                                <p className="text-gray-900 font-medium">{selectedOrg.contact}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="bg-gray-50 p-4 rounded-lg">
                                                        <div className="flex items-center gap-3">
                                                            <Phone className="w-5 h-5 text-green-500" />
                                                            <div>
                                                                <label className="text-sm font-medium text-gray-500 block">Phone</label>
                                                                <p className="text-gray-900 font-medium">{selectedOrg.phone}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="bg-gray-50 p-4 rounded-lg">
                                                        <div className="flex items-center gap-3">
                                                            <Globe className="w-5 h-5 text-purple-500" />
                                                            <div>
                                                                <label className="text-sm font-medium text-gray-500 block">Website</label>
                                                                <p className="text-gray-900 font-medium">{selectedOrg.website}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="bg-gray-50 p-4 rounded-lg">
                                                        <div className="flex items-center gap-3">
                                                            <MapPin className="w-5 h-5 text-red-500" />
                                                            <div>
                                                                <label className="text-sm font-medium text-gray-500 block">Address</label>
                                                                <p className="text-gray-900 font-medium">{selectedOrg.address}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Performance Metrics */}
                                    <div className="mt-8 pt-6 border-t border-gray-200">
                                        <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                                            <Activity className="w-5 h-5 text-purple-600" />
                                            Performance Metrics
                                        </h3>
                                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                                            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200">
                                                <div className="flex items-center justify-between mb-2">
                                                    <Bot className="w-8 h-8 text-blue-600" />
                                                    <div className="text-right">
                                                        <div className="text-3xl font-bold text-blue-600">{selectedOrg.drones}</div>
                                                        <div className="text-sm font-medium text-blue-600">Total Drones</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl border border-green-200">
                                                <div className="flex items-center justify-between mb-2">
                                                    <Activity className="w-8 h-8 text-green-600" />
                                                    <div className="text-right">
                                                        <div className="text-3xl font-bold text-green-600">{selectedOrg.activeDeployments}</div>
                                                        <div className="text-sm font-medium text-green-600">Active Deployments</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200">
                                                <div className="flex items-center justify-between mb-2">
                                                    <TrendingUp className="w-8 h-8 text-purple-600" />
                                                    <div className="text-right">
                                                        <div className="text-3xl font-bold text-purple-600">{selectedOrg.totalMissions}</div>
                                                        <div className="text-sm font-medium text-purple-600">Total Missions</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Action Buttons */}
                                    <div className="mt-8 pt-6 border-t border-gray-200">
                                        <div className="flex flex-wrap gap-3 justify-end">
                                            <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                                <Edit2 size={16} />
                                                Edit Organization
                                            </button>
                                            <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                                <Download size={16} />
                                                Export Data
                                            </button>
                                            <button
                                                onClick={() => setSelectedOrg(null)}
                                                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                                            >
                                                <X size={16} />
                                                Close
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Edit Organization Modal */}
            {showEditModal && editingOrg && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                        {/* Header */}
                        <div className="flex items-center justify-between p-6 border-b border-gray-200">
                            <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                                <Building className="w-5 h-5" />
                                Edit Organization
                            </h2>
                            <button
                                onClick={() => {
                                    setShowEditModal(false);
                                    setEditingOrg(null);
                                }}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <X size={24} />
                            </button>
                        </div>

                        {/* Simple Form */}
                        <div className="p-6">
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Organization Name
                                    </label>
                                    <input
                                        type="text"
                                        defaultValue={editingOrg.name || ''}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        readOnly
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Email
                                    </label>
                                    <input
                                        type="email"
                                        defaultValue={editingOrg.contact?.primaryEmail || ''}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        readOnly
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Phone
                                    </label>
                                    <input
                                        type="tel"
                                        defaultValue={editingOrg.contact?.phone || ''}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        readOnly
                                    />
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex justify-end gap-4 mt-6 pt-4 border-t border-gray-200">
                                <button
                                    onClick={() => {
                                        setShowEditModal(false);
                                        setEditingOrg(null);
                                    }}
                                    className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                                >
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default OrganizationPage;
