const axios = require('axios');

async function testNotificationSystem() {
  try {
    console.log('🔍 Testing notification system...');
    
    // First, login to get a token
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful, token obtained');
    
    // Create a test individual
    const individualData = {
      personalInfo: {
        firstName: 'Test',
        lastName: 'NotificationUser',
        dateOfBirth: '1990-01-01',
        gender: 'male',
        nationality: 'Indian'
      },
      contact: {
        primaryEmail: `test-notification-${Date.now()}@example.com`,
        primaryPhone: '+91-9876543210',
        address: {
          street: '123 Test St',
          city: 'Mumbai',
          state: 'Maharashtra',
          pincode: '400001',
          country: 'India'
        }
      },
      documents: {
        panNumber: `TEST${Date.now().toString().slice(-5)}F`,
        aadharNumber: `${Date.now().toString().slice(-12)}`
      },
      organizationId: '688f3587fbc2d18330659244'
    };
    
    console.log('🔍 Creating test individual...');
    const createResponse = await axios.post('http://localhost:5000/api/individuals', individualData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Individual created successfully:', createResponse.data.data.individual.personalInfo.firstName, createResponse.data.data.individual.personalInfo.lastName);
    
    // Wait a moment for notification to be created
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check notifications
    console.log('🔍 Checking notifications...');
    const notificationsResponse = await axios.get('http://localhost:5000/api/notifications?page=1&limit=10&sortBy=createdAt&sortOrder=desc', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Notifications retrieved:', notificationsResponse.data.data.notifications.length, 'notifications found');
    
    if (notificationsResponse.data.data.notifications.length > 0) {
      const latestNotification = notificationsResponse.data.data.notifications[0];
      console.log('📧 Latest notification:');
      console.log('   Title:', latestNotification.title);
      console.log('   Message:', latestNotification.message);
      console.log('   Type:', latestNotification.type);
      console.log('   Created:', latestNotification.createdAt);
    } else {
      console.log('❌ No notifications found');
    }
    
  } catch (error) {
    console.error('❌ Error testing notification system:', error.response?.data || error.message);
  }
}

testNotificationSystem();
