import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Building,
  User,
  Mail,
  Phone,
  MapPin,
  CreditCard,
  FileText,
  Upload,
  CheckCircle,
  AlertCircle,
  Save,
  RefreshCw,
  X,
  Users,
  Briefcase,
  Bot
} from 'lucide-react';

import AdminSidebar from '../common/AdminSidebar';
import organizationService from '../../../services/organizationService';
import { useAuth } from '../../../context/AuthContext';




const OrganizationForm = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();

  const [formData, setFormData] = useState({
    registrationType: 'organization',
    name: '',
    displayName: '',
    description: '',
    type: '',
    contact: {
      primaryEmail: '',
      secondaryEmail: '',
      phone: '',
      alternatePhone: '',
      website: ''
    },
    address: {
      street: '',
      city: '',
      state: '',
      country: 'India',
      postalCode: '',
      coordinates: {
        latitude: '',
        longitude: ''
      }
    },
    registration: {
      registrationNumber: '',
      registrationDate: '',
      taxId: '',
      licenseNumber: '',
      licenseExpiryDate: ''
    },
    primaryContact: {
      name: '',
      designation: '',
      email: '',
      phone: ''
    },
    allocatedDrones: 0,
    // Legacy fields for backward compatibility
    organizationName: '',
    organizationType: '',
    officialName: '',
    contactNumber: '',
    panNumber: '',
    pocName: '',
    pocDesignation: '',
    pocEmail: '',
    pocKyc: null,
    pocIdProof: null,
    authorizationLetter: null
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState({
    pocKyc: null,
    pocIdProof: null,
    authorizationLetter: null
  });



  // Validation functions
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone) => {
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  const validatePAN = (pan) => {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(pan);
  };

  const validateForm = () => {
    const newErrors = {};

    // Basic organization info
    if (!formData.name.trim()) newErrors.name = 'Organization name is required';
    if (!formData.type) newErrors.type = 'Organization type is required';

    // Contact validation
    if (!formData.contact.primaryEmail.trim()) {
      newErrors.primaryEmail = 'Primary email is required';
    } else if (!validateEmail(formData.contact.primaryEmail)) {
      newErrors.primaryEmail = 'Please enter a valid email address';
    }

    if (!formData.contact.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!validatePhone(formData.contact.phone)) {
      newErrors.phone = 'Please enter a valid 10-digit mobile number';
    }

    // Address validation
    if (!formData.address.street.trim()) newErrors.street = 'Street address is required';
    if (!formData.address.city.trim()) newErrors.city = 'City is required';
    if (!formData.address.state.trim()) newErrors.state = 'State is required';
    if (!formData.address.country.trim()) newErrors.country = 'Country is required';
    if (!formData.address.postalCode.trim()) newErrors.postalCode = 'Postal code is required';

    // Registration validation
    if (!formData.registration.registrationNumber.trim()) newErrors.registrationNumber = 'Registration number is required';
    if (!formData.registration.registrationDate.trim()) newErrors.registrationDate = 'Registration date is required';

    // Primary contact validation
    if (!formData.primaryContact.name.trim()) newErrors.pocName = 'Primary contact name is required';
    if (!formData.primaryContact.designation.trim()) newErrors.pocDesignation = 'Primary contact designation is required';
    if (!formData.primaryContact.email.trim()) {
      newErrors.pocEmail = 'Primary contact email is required';
    } else if (!validateEmail(formData.primaryContact.email)) {
      newErrors.pocEmail = 'Please enter a valid email address';
    }
    if (!formData.primaryContact.phone.trim()) {
      newErrors.pocPhone = 'Primary contact phone is required';
    } else if (!validatePhone(formData.primaryContact.phone)) {
      newErrors.pocPhone = 'Please enter a valid phone number';
    }

    // Allocated drones validation
    const allocatedDrones = parseInt(formData.allocatedDrones);
    if (isNaN(allocatedDrones) || allocatedDrones < 0) {
      newErrors.allocatedDrones = 'Allocated drones must be a non-negative number';
    } else if (allocatedDrones > 1000) {
      newErrors.allocatedDrones = 'Allocated drones cannot exceed 1000';
    }

    // Legacy validation for backward compatibility
    if (!formData.organizationName.trim()) formData.organizationName = formData.name;
    if (!formData.organizationType) formData.organizationType = formData.type;
    if (!formData.contactNumber.trim()) formData.contactNumber = formData.contact.phone;
    if (!formData.pocName.trim()) formData.pocName = formData.primaryContact.name;
    if (!formData.pocDesignation.trim()) formData.pocDesignation = formData.primaryContact.designation;
    if (!formData.pocEmail.trim()) formData.pocEmail = formData.primaryContact.email;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === 'registrationType' && value === 'individual') {
      navigate('/individualform');
      return;
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Handle nested object updates
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      // Handle direct field updates and legacy fields
      const formattedValue = name === 'panNumber' ? value.toUpperCase() : value;

      setFormData(prev => {
        const newData = {
          ...prev,
          [name]: formattedValue
        };

        // Sync with nested structure for key fields
        if (name === 'organizationName') {
          newData.name = formattedValue;
        } else if (name === 'organizationType') {
          newData.type = formattedValue;
        } else if (name === 'contactNumber') {
          newData.contact = { ...newData.contact, phone: formattedValue };
        } else if (name === 'pocName') {
          newData.primaryContact = { ...newData.primaryContact, name: formattedValue };
        } else if (name === 'pocDesignation') {
          newData.primaryContact = { ...newData.primaryContact, designation: formattedValue };
        } else if (name === 'pocEmail') {
          newData.primaryContact = { ...newData.primaryContact, email: formattedValue };
        }

        return newData;
      });
    }
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    const file = files[0];

    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({
          ...prev,
          [name]: 'File size should be less than 5MB'
        }));
        return;
      }

      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          [name]: 'Only PDF, JPG, JPEG, and PNG files are allowed'
        }));
        return;
      }

      // Clear error and set file
      if (errors[name]) {
        setErrors(prev => ({
          ...prev,
          [name]: ''
        }));
      }

      setUploadedFiles(prev => ({
        ...prev,
        [name]: file
      }));

      setFormData(prev => ({
        ...prev,
        [name]: file
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API
      const organizationData = {
        name: formData.name || formData.organizationName,
        displayName: formData.displayName,
        description: formData.description,
        type: formData.type || formData.organizationType,
        contact: {
          primaryEmail: formData.contact.primaryEmail || formData.pocEmail,
          secondaryEmail: formData.contact.secondaryEmail,
          phone: formData.contact.phone || formData.contactNumber,
          alternatePhone: formData.contact.alternatePhone,
          website: formData.contact.website
        },
        address: {
          street: formData.address.street,
          city: formData.address.city,
          state: formData.address.state,
          country: formData.address.country,
          postalCode: formData.address.postalCode,
          ...(formData.address.coordinates.latitude && formData.address.coordinates.longitude ? {
            coordinates: {
              latitude: parseFloat(formData.address.coordinates.latitude),
              longitude: parseFloat(formData.address.coordinates.longitude)
            }
          } : {})
        },
        registration: {
          registrationNumber: formData.registration.registrationNumber,
          registrationDate: formData.registration.registrationDate,
          taxId: formData.registration.taxId || formData.panNumber,
          licenseNumber: formData.registration.licenseNumber,
          licenseExpiryDate: formData.registration.licenseExpiryDate
        },
        primaryContact: {
          name: formData.primaryContact.name || formData.pocName,
          designation: formData.primaryContact.designation || formData.pocDesignation,
          email: formData.primaryContact.email || formData.pocEmail,
          phone: formData.primaryContact.phone || formData.contactNumber
        },
        allocatedDrones: parseInt(formData.allocatedDrones) || 0
      };

      // Remove undefined and empty values
      const cleanData = JSON.parse(JSON.stringify(organizationData, (key, value) => {
        if (value === undefined || value === null || value === '') {
          return undefined; // This will remove the property entirely
        }
        return value;
      }));

      // Call the API
      const response = await organizationService.createOrganization(cleanData);

      if (response.success) {
        setShowSuccess(true);

        // Reset form after successful submission
        setTimeout(() => {
          setShowSuccess(false);
          // Navigate to organizations page
          navigate('/organizationpage');
        }, 2000); // Reduced to 2 seconds for better UX
      }

    } catch (error) {
      setErrors({ submit: error.message || 'Failed to submit form. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    setFormData({
      registrationType: 'organization',
      name: '',
      displayName: '',
      description: '',
      type: '',
      contact: {
        primaryEmail: '',
        secondaryEmail: '',
        phone: '',
        alternatePhone: '',
        website: ''
      },
      address: {
        street: '',
        city: '',
        state: '',
        country: 'India',
        postalCode: '',
        coordinates: {
          latitude: '',
          longitude: ''
        }
      },
      registration: {
        registrationNumber: '',
        registrationDate: '',
        taxId: '',
        licenseNumber: '',
        licenseExpiryDate: ''
      },
      primaryContact: {
        name: '',
        designation: '',
        email: '',
        phone: ''
      },
      // Legacy fields
      organizationName: '',
      organizationType: '',
      officialName: '',
      contactNumber: '',
      panNumber: '',
      pocName: '',
      pocDesignation: '',
      pocEmail: '',
      pocKyc: null,
      pocIdProof: null,
      authorizationLetter: null
    });
    setUploadedFiles({
      pocKyc: null,
      pocIdProof: null,
      authorizationLetter: null
    });
    setErrors({});
    setShowSuccess(false);
  };


  return (
    <div className="min-h-screen bg-gray-50 text-black">
      <AdminSidebar />
      <div className="pl-[250px] w-full">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4 text-black sticky top-0 z-10">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <Building className="text-blue-600" />
                Organization Registration
              </h2>
              <p className="text-gray-600 mt-1">
                Register your organization for drone operations
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                type="button"
                onClick={handleReset}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                Reset Form
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-4 md:p-6">
          {/* Success Message */}
          {showSuccess && (
            <div className="w-full mb-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <div>
                      <h3 className="text-green-800 font-medium">Registration Successful!</h3>
                      <p className="text-green-700 text-sm">Your organization registration has been submitted successfully.</p>
                    </div>
                  </div>
                  <button
                    onClick={() => navigate('/organizationpage')}
                    className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors"
                  >
                    View Organizations
                  </button>
                </div>
              </div>
            </div>
          )}

          <div className="w-full bg-white rounded-xl shadow-sm border border-gray-200">
            <form onSubmit={handleSubmit} className="h-full">
              {/* Registration Type Selector */}
              <div className="p-4 md:p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Registration Type</h3>
                <div className="flex flex-wrap items-center gap-6">
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="registrationType"
                      value="individual"
                      checked={formData.registrationType === 'individual'}
                      onChange={handleInputChange}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-900">Individual</span>
                  </label>
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="registrationType"
                      value="organization"
                      checked={formData.registrationType === 'organization'}
                      onChange={handleInputChange}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-900">Organization</span>
                  </label>
                </div>
              </div>

              {/* Organization Details */}
              <div className="p-4 md:p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <Building className="w-5 h-5 text-blue-600" />
                  Organization Details
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
                  {/* Organization Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Organization Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Enter organization name"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.name ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                    />
                    {errors.name && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.name}
                      </p>
                    )}
                  </div>

                  {/* Organization Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Organization Type *
                    </label>
                    <div className="relative">
                      <Briefcase className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <select
                        name="type"
                        value={formData.type}
                        onChange={handleInputChange}
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.type ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      >
                        <option value="">Select Organization Type</option>
                        <option value="government">Government</option>
                        <option value="private">Private</option>
                        <option value="ngo">NGO</option>
                        <option value="research">Research</option>
                        <option value="military">Military</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    {errors.type && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.type}
                      </p>
                    )}
                  </div>

                  {/* Registration Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Registration Number *
                    </label>
                    <div className="relative">
                      <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        name="registration.registrationNumber"
                        value={formData.registration.registrationNumber}
                        onChange={handleInputChange}
                        placeholder="Enter registration number"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.registrationNumber ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      />
                    </div>
                    {errors.registrationNumber && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.registrationNumber}
                      </p>
                    )}
                  </div>

                  {/* Registration Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Registration Date *
                    </label>
                    <input
                      type="date"
                      name="registration.registrationDate"
                      value={formData.registration.registrationDate}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.registrationDate ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                    />
                    {errors.registrationDate && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.registrationDate}
                      </p>
                    )}
                  </div>

                  {/* Official Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Official Name *
                    </label>
                    <input
                      type="text"
                      name="officialName"
                      value={formData.officialName}
                      onChange={handleInputChange}
                      placeholder="Enter official name"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.officialName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                    />
                    {errors.officialName && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.officialName}
                      </p>
                    )}
                  </div>

                  {/* Primary Email */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Primary Email *
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="email"
                        name="contact.primaryEmail"
                        value={formData.contact.primaryEmail}
                        onChange={handleInputChange}
                        placeholder="Enter primary email address"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.primaryEmail ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      />
                    </div>
                    {errors.primaryEmail && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.primaryEmail}
                      </p>
                    )}
                  </div>

                  {/* Contact Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contact Number *
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="tel"
                        name="contact.phone"
                        value={formData.contact.phone}
                        onChange={handleInputChange}
                        placeholder="Enter 10-digit mobile number"
                        maxLength="10"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.phone ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      />
                    </div>
                    {errors.phone && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.phone}
                      </p>
                    )}
                  </div>

                  {/* PAN Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      PAN Number *
                    </label>
                    <div className="relative">
                      <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        name="panNumber"
                        value={formData.panNumber}
                        onChange={handleInputChange}
                        placeholder="**********"
                        maxLength="10"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors uppercase ${errors.panNumber ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      />
                    </div>
                    {errors.panNumber && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.panNumber}
                      </p>
                    )}
                  </div>

                  {/* Street Address - Full Width */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Street Address *
                    </label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                      <textarea
                        name="address.street"
                        value={formData.address.street}
                        onChange={handleInputChange}
                        placeholder="Enter complete street address"
                        rows="3"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${errors.street ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      />
                    </div>
                    {errors.street && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.street}
                      </p>
                    )}
                  </div>

                  {/* City */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      City *
                    </label>
                    <input
                      type="text"
                      name="address.city"
                      value={formData.address.city}
                      onChange={handleInputChange}
                      placeholder="Enter city"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.city ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                    />
                    {errors.city && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.city}
                      </p>
                    )}
                  </div>

                  {/* State */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      State *
                    </label>
                    <input
                      type="text"
                      name="address.state"
                      value={formData.address.state}
                      onChange={handleInputChange}
                      placeholder="Enter state"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.state ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                    />
                    {errors.state && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.state}
                      </p>
                    )}
                  </div>

                  {/* Country */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Country *
                    </label>
                    <input
                      type="text"
                      name="address.country"
                      value={formData.address.country}
                      onChange={handleInputChange}
                      placeholder="Enter country"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.country ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                    />
                    {errors.country && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.country}
                      </p>
                    )}
                  </div>

                  {/* Postal Code */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Postal Code *
                    </label>
                    <input
                      type="text"
                      name="address.postalCode"
                      value={formData.address.postalCode}
                      onChange={handleInputChange}
                      placeholder="Enter postal code"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.postalCode ? 'border-red-300 bg-red-50' : 'border-gray-300'
                        }`}
                    />
                    {errors.postalCode && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.postalCode}
                      </p>
                    )}
                  </div>

                  {/* Allocated Drones */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Allocated Drones *
                    </label>
                    <div className="relative">
                      <Bot className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="number"
                        name="allocatedDrones"
                        value={formData.allocatedDrones}
                        onChange={handleInputChange}
                        placeholder="Enter number of drones to allocate"
                        min="0"
                        max="1000"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.allocatedDrones ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      />
                    </div>
                    {errors.allocatedDrones && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.allocatedDrones}
                      </p>
                    )}
                    <p className="mt-1 text-sm text-gray-500">
                      Number of drones to be allocated to this organization (0-1000)
                    </p>
                  </div>
                </div>
              </div>

              {/* POC Details */}
              <div className="p-4 md:p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <Users className="w-5 h-5 text-blue-600" />
                  Point of Contact (POC) Details
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
                  {/* POC Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      POC Name *
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        name="primaryContact.name"
                        value={formData.primaryContact.name}
                        onChange={handleInputChange}
                        placeholder="Enter POC full name"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.pocName ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      />
                    </div>
                    {errors.pocName && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.pocName}
                      </p>
                    )}
                  </div>

                  {/* POC Designation */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      POC Designation *
                    </label>
                    <div className="relative">
                      <Briefcase className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        name="primaryContact.designation"
                        value={formData.primaryContact.designation}
                        onChange={handleInputChange}
                        placeholder="Enter POC designation"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.pocDesignation ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      />
                    </div>
                    {errors.pocDesignation && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.pocDesignation}
                      </p>
                    )}
                  </div>

                  {/* POC Email */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      POC Email Address *
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="email"
                        name="primaryContact.email"
                        value={formData.primaryContact.email}
                        onChange={handleInputChange}
                        placeholder="Enter POC email address"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.pocEmail ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      />
                    </div>
                    {errors.pocEmail && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.pocEmail}
                      </p>
                    )}
                  </div>

                  {/* POC Phone */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      POC Phone Number *
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="tel"
                        name="primaryContact.phone"
                        value={formData.primaryContact.phone}
                        onChange={handleInputChange}
                        placeholder="Enter POC phone number"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${errors.pocPhone ? 'border-red-300 bg-red-50' : 'border-gray-300'
                          }`}
                      />
                    </div>
                    {errors.pocPhone && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.pocPhone}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Document Uploads */}
              <div className="p-4 md:p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <FileText className="w-5 h-5 text-blue-600" />
                  Required Documents
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
                  {/* POC KYC Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      POC KYC Document *
                    </label>
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${errors.pocKyc ? 'border-red-300 bg-red-50' :
                        uploadedFiles.pocKyc ? 'border-green-300 bg-green-50' :
                          'border-gray-300 hover:border-gray-400'
                      }`}>
                      {uploadedFiles.pocKyc ? (
                        <div className="flex flex-col items-center gap-2">
                          <CheckCircle className="w-8 h-8 text-green-600" />
                          <p className="text-sm font-medium text-green-800">{uploadedFiles.pocKyc.name}</p>
                          <p className="text-xs text-green-600">
                            {(uploadedFiles.pocKyc.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                          <button
                            type="button"
                            onClick={() => {
                              setUploadedFiles(prev => ({ ...prev, pocKyc: null }));
                              setFormData(prev => ({ ...prev, pocKyc: null }));
                            }}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ) : (
                        <>
                          <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                          <p className="text-xs text-gray-600 mb-2">Drop file or</p>
                          <label className="cursor-pointer">
                            <span className="text-blue-600 hover:text-blue-700 underline text-sm font-medium">browse</span>
                            <input
                              type="file"
                              name="pocKyc"
                              onChange={handleFileChange}
                              className="hidden"
                              accept=".pdf,.jpg,.jpeg,.png"
                            />
                          </label>
                          <p className="text-xs text-gray-500 mt-1">Max 5MB</p>
                        </>
                      )}
                    </div>
                    {errors.pocKyc && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.pocKyc}
                      </p>
                    )}
                  </div>

                  {/* POC ID Proof Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      POC ID Proof *
                    </label>
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${errors.pocIdProof ? 'border-red-300 bg-red-50' :
                        uploadedFiles.pocIdProof ? 'border-green-300 bg-green-50' :
                          'border-gray-300 hover:border-gray-400'
                      }`}>
                      {uploadedFiles.pocIdProof ? (
                        <div className="flex flex-col items-center gap-2">
                          <CheckCircle className="w-8 h-8 text-green-600" />
                          <p className="text-sm font-medium text-green-800">{uploadedFiles.pocIdProof.name}</p>
                          <p className="text-xs text-green-600">
                            {(uploadedFiles.pocIdProof.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                          <button
                            type="button"
                            onClick={() => {
                              setUploadedFiles(prev => ({ ...prev, pocIdProof: null }));
                              setFormData(prev => ({ ...prev, pocIdProof: null }));
                            }}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ) : (
                        <>
                          <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                          <p className="text-xs text-gray-600 mb-2">Drop file or</p>
                          <label className="cursor-pointer">
                            <span className="text-blue-600 hover:text-blue-700 underline text-sm font-medium">browse</span>
                            <input
                              type="file"
                              name="pocIdProof"
                              onChange={handleFileChange}
                              className="hidden"
                              accept=".pdf,.jpg,.jpeg,.png"
                            />
                          </label>
                          <p className="text-xs text-gray-500 mt-1">Max 5MB</p>
                        </>
                      )}
                    </div>
                    {errors.pocIdProof && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.pocIdProof}
                      </p>
                    )}
                  </div>

                  {/* Authorization Letter Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Authorization Letter *
                    </label>
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${errors.authorizationLetter ? 'border-red-300 bg-red-50' :
                        uploadedFiles.authorizationLetter ? 'border-green-300 bg-green-50' :
                          'border-gray-300 hover:border-gray-400'
                      }`}>
                      {uploadedFiles.authorizationLetter ? (
                        <div className="flex flex-col items-center gap-2">
                          <CheckCircle className="w-8 h-8 text-green-600" />
                          <p className="text-sm font-medium text-green-800">{uploadedFiles.authorizationLetter.name}</p>
                          <p className="text-xs text-green-600">
                            {(uploadedFiles.authorizationLetter.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                          <button
                            type="button"
                            onClick={() => {
                              setUploadedFiles(prev => ({ ...prev, authorizationLetter: null }));
                              setFormData(prev => ({ ...prev, authorizationLetter: null }));
                            }}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ) : (
                        <>
                          <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                          <p className="text-xs text-gray-600 mb-2">Drop file or</p>
                          <label className="cursor-pointer">
                            <span className="text-blue-600 hover:text-blue-700 underline text-sm font-medium">browse</span>
                            <input
                              type="file"
                              name="authorizationLetter"
                              onChange={handleFileChange}
                              className="hidden"
                              accept=".pdf,.jpg,.jpeg,.png"
                            />
                          </label>
                          <p className="text-xs text-gray-500 mt-1">Max 5MB</p>
                        </>
                      )}
                    </div>
                    {errors.authorizationLetter && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.authorizationLetter}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Submit Section */}
              <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4 md:p-6">
                {errors.submit && (
                  <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                    <AlertCircle className="w-5 h-5 text-red-600" />
                    <p className="text-red-800">{errors.submit}</p>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                  <button
                    type="button"
                    onClick={handleReset}
                    className="w-full sm:w-auto flex items-center justify-center gap-2 px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Reset Form
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex items-center gap-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                  >
                    {isSubmitting ? (
                      <>
                        <RefreshCw className="w-4 h-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        Submit Registration
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrganizationForm;










