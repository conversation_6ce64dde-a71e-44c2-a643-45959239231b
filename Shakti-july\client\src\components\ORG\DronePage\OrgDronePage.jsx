import React, { useState, useEffect } from 'react';
import {
  Bell,
  Search,
  RefreshCw,
  Filter,
  Eye,
  MapPin,
  Settings,
  Plane,
  Battery,
  XCircle
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import toast, { Toaster } from 'react-hot-toast';
import OrgSidebar from '../common/OrgSidebar';
import DroneFilters from './DroneFilters';
import DroneStats from './DroneStats';
import DroneActions from './DroneActions';
import OrganizationPortalService from '../../../services/organizationPortalService';
import './dronePage.css';

const OrgDronePage = () => {
  const navigate = useNavigate();

  // Enhanced state management
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRegion, setSelectedRegion] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [selectedDeployment, setSelectedDeployment] = useState("");
  const [sortOrder, setSortOrder] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedDrones, setSelectedDrones] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const [drones, setDrones] = useState([]);
  const [error, setError] = useState(null);
  const [selectedDroneDetails, setSelectedDroneDetails] = useState(null);
  const [showDroneDetailsModal, setShowDroneDetailsModal] = useState(false);
  const rowsPerPage = 8;

  // Function to fetch drones from API
  const fetchDrones = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await OrganizationPortalService.getDrones({
        page: currentPage,
        limit: rowsPerPage,
        search: searchQuery,
        status: selectedStatus === 'All' ? '' : selectedStatus
      });

      if (response.success) {
        // Transform API data to match UI expectations
        const transformedDrones = response.data.drones.map(drone => ({
          id: drone._id,
          name: drone.name,
          deployDate: drone.createdAt ? new Date(drone.createdAt).toLocaleDateString() : '',
          pilot: drone.assignedPilot || '',
          location: drone.currentLocation?.address || '',
          status: drone.status || 'inactive',
          model: drone.model,
          batteryLevel: drone.batteryLevel || 0,
          flightHours: drone.flightStats?.totalFlightTime || 0,
          lastMaintenance: drone.lastMaintenance ? new Date(drone.lastMaintenance).toLocaleDateString() : '',
          nextMaintenance: drone.nextMaintenance ? new Date(drone.nextMaintenance).toLocaleDateString() : '',
          coordinates: {
            lat: drone.currentLocation?.latitude || 0,
            lng: drone.currentLocation?.longitude || 0
          },
          altitude: drone.currentLocation?.altitude || 0,
          speed: drone.currentSpeed || 0,
          temperature: drone.environmentalData?.temperature || 0,
          signalStrength: drone.signalStrength || 0,
          missions: drone.flightStats?.totalFlights || 0,
          efficiency: drone.efficiency || 0,
          image: '/api/placeholder/300/200',
          serialNumber: drone.serialNumber,
          registrationNumber: drone.registrationNumber,
          manufacturer: drone.manufacturer,
          droneType: drone.droneType
        }));

        setDrones(transformedDrones);
      } else {
        setError(response.message || 'Failed to fetch drones');
        setDrones([]);
      }
    } catch (error) {
      console.error('❌ Error fetching drones:', error);
      setError('Failed to load drone data. Please try again.');
      setDrones([]);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const statusClasses = {
    'active': 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm text-center',
    'inactive': 'bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-sm text-center',
    'maintenance': 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm text-center',
    'retired': 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm text-center',
    'lost': 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm text-center',
    // Legacy support for old status values
    'Active': 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm text-center',
    'Inactive': 'bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-sm text-center',
    'Maintenance': 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm text-center',
    'Crashed': 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm text-center',
    'Flying': 'bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm text-center',
    'Approval Pending': 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm text-center'
  };

  // Initialize component and fetch drones
  useEffect(() => {
    fetchDrones();
  }, [currentPage, selectedStatus, searchQuery]);

  // Refresh function
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDrones();
  };

  // Handle viewing drone details
  const handleViewDroneDetails = async (droneId) => {
    try {
      // Find the drone in the current list first
      const drone = drones.find(d => d.id === droneId);
      if (drone) {
        setSelectedDroneDetails(drone);
        setShowDroneDetailsModal(true);
      } else {
        // If not found, fetch from API
        const response = await OrganizationPortalService.getDroneById(droneId);
        if (response.success) {
          setSelectedDroneDetails(response.data);
          setShowDroneDetailsModal(true);
        } else {
          toast.error('Failed to load drone details');
        }
      }
    } catch (error) {
      console.error('Error loading drone details:', error);
      toast.error('Failed to load drone details');
    }
  };

  // Bulk action handlers
  const handleBulkStatusUpdate = async (droneIds, status) => {
    try {
      const results = await Promise.allSettled(
        droneIds.map(droneId =>
          OrganizationPortalService.updateDroneStatus(droneId, status)
        )
      );

      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.length - successful;

      if (successful > 0) {
        toast.success(`Successfully updated ${successful} drone(s) to ${status}${failed > 0 ? `. ${failed} failed.` : ''}`);
        await fetchDrones(); // Refresh the list
        setSelectedDrones([]); // Clear selection
      } else {
        toast.error('Failed to update drone status');
      }
    } catch (error) {
      console.error('Bulk status update failed:', error);
      toast.error('Failed to update drone status');
    }
  };

  const handleBulkDelete = async (droneIds) => {
    try {
      const results = await Promise.allSettled(
        droneIds.map(droneId =>
          OrganizationPortalService.deleteDrone(droneId)
        )
      );

      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.length - successful;

      if (successful > 0) {
        toast.success(`Successfully deleted ${successful} drone(s)${failed > 0 ? `. ${failed} failed.` : ''}`);
        await fetchDrones(); // Refresh the list
        setSelectedDrones([]); // Clear selection
      } else {
        toast.error('Failed to delete drones');
      }
    } catch (error) {
      console.error('Bulk delete failed:', error);
      toast.error('Failed to delete drones');
    }
  };

  const handleBulkAssignPilot = async (droneIds) => {
    const pilot = prompt('Enter pilot name to assign to selected drones:');
    if (!pilot) return;

    try {
      const results = await Promise.allSettled(
        droneIds.map(droneId =>
          OrganizationPortalService.updateDrone(droneId, { assignedPilot: pilot })
        )
      );

      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.length - successful;

      if (successful > 0) {
        toast.success(`Successfully assigned pilot to ${successful} drone(s)${failed > 0 ? `. ${failed} failed.` : ''}`);
        await fetchDrones(); // Refresh the list
        setSelectedDrones([]); // Clear selection
      } else {
        toast.error('Failed to assign pilot');
      }
    } catch (error) {
      console.error('Bulk pilot assignment failed:', error);
      toast.error('Failed to assign pilot');
    }
  };

  const handleBulkAssignLocation = async (droneIds) => {
    const location = prompt('Enter location to assign to selected drones:');
    if (!location) return;

    try {
      const results = await Promise.allSettled(
        droneIds.map(droneId =>
          OrganizationPortalService.updateDrone(droneId, {
            currentLocation: { address: location }
          })
        )
      );

      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.length - successful;

      if (successful > 0) {
        toast.success(`Successfully assigned location to ${successful} drone(s)${failed > 0 ? `. ${failed} failed.` : ''}`);
        await fetchDrones(); // Refresh the list
        setSelectedDrones([]); // Clear selection
      } else {
        toast.error('Failed to assign location');
      }
    } catch (error) {
      console.error('Bulk location assignment failed:', error);
      toast.error('Failed to assign location');
    }
  };

  const handleBulkScheduleMaintenance = async (droneIds) => {
    const maintenanceDate = prompt('Enter maintenance date (YYYY-MM-DD):');
    if (!maintenanceDate) return;

    try {
      const results = await Promise.allSettled(
        droneIds.map(droneId =>
          OrganizationPortalService.updateDrone(droneId, {
            status: 'maintenance',
            nextMaintenance: maintenanceDate
          })
        )
      );

      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.length - successful;

      if (successful > 0) {
        toast.success(`Successfully scheduled maintenance for ${successful} drone(s)${failed > 0 ? `. ${failed} failed.` : ''}`);
        await fetchDrones(); // Refresh the list
        setSelectedDrones([]); // Clear selection
      } else {
        toast.error('Failed to schedule maintenance');
      }
    } catch (error) {
      console.error('Bulk maintenance scheduling failed:', error);
      toast.error('Failed to schedule maintenance');
    }
  };

  // Enhanced filtering and search logic
  const filteredDrones = drones.filter(drone => {
    const matchesSearch = searchQuery === '' ||
      drone.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      drone.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      drone.pilot.toLowerCase().includes(searchQuery.toLowerCase()) ||
      drone.location.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesRegion = selectedRegion === '' ||
      drone.location.toLowerCase() === selectedRegion.toLowerCase();

    const matchesStatus = selectedStatus === '' ||
      drone.status.toLowerCase() === selectedStatus.toLowerCase();

    const matchesDeployment = selectedDeployment === '' ||
      (selectedDeployment === "Deployed" ? drone.deployDate : !drone.deployDate);

    return matchesSearch && matchesRegion && matchesStatus && matchesDeployment;
  }).sort((a, b) => {
    if (sortOrder === "latest") return new Date(b.deployDate || 0) - new Date(a.deployDate || 0);
    if (sortOrder === "oldest") return new Date(a.deployDate || 0) - new Date(b.deployDate || 0);
    if (sortOrder === "name") return a.name.localeCompare(b.name);
    if (sortOrder === "efficiency") return b.efficiency - a.efficiency;
    if (sortOrder === "battery") return b.batteryLevel - a.batteryLevel;
    return 0;
  });



  // Handle drone selection
  const handleDroneSelect = (droneId) => {
    setSelectedDrones(prev =>
      prev.includes(droneId)
        ? prev.filter(id => id !== droneId)
        : [...prev, droneId]
    );
  };

  // Calculate statistics
  const stats = {
    total: drones.length,
    active: drones.filter(d => d.status.toLowerCase() === 'active').length,
    inactive: drones.filter(d => d.status.toLowerCase() === 'inactive').length,
    maintenance: drones.filter(d => d.status.toLowerCase() === 'maintenance').length,
    crashed: drones.filter(d => d.status.toLowerCase() === 'retired' || d.status.toLowerCase() === 'lost' || d.status.toLowerCase() === 'crashed').length,
    pending: drones.filter(d => d.status === 'Approval Pending').length,
    avgEfficiency: drones.length > 0 ? drones.reduce((acc, d) => acc + (d.efficiency || 0), 0) / drones.length : 0,
    avgBattery: drones.filter(d => d.batteryLevel > 0).length > 0 ? drones.filter(d => d.batteryLevel > 0).reduce((acc, d) => acc + d.batteryLevel, 0) / drones.filter(d => d.batteryLevel > 0).length : 0,
    totalFlightHours: drones.reduce((acc, d) => acc + (d.flightHours || 0), 0),
    totalMissions: drones.reduce((acc, d) => acc + (d.missions || 0), 0)
  };

  const totalPages = Math.ceil(filteredDrones.length / rowsPerPage);
  const paginatedDrones = filteredDrones.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage);

  if (isLoading) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading drone fleet...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="drone-page-container w-full min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 overflow-x-hidden">
      <OrgSidebar />

      {/* Main Content */}
      <main className="drone-page-main ml-0 lg:ml-[18em] min-h-screen flex flex-col transition-all duration-300 max-w-full overflow-x-hidden">
        {/* Professional Header */}
        <div className="drone-page-header bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
          <div className="px-4 lg:px-6 py-4">
            <div className="flex flex-col lg:flex-row lg:items-center gap-4 lg:gap-0 lg:justify-between">
              {/* Search Section */}
              <div className="flex-1 max-w-full lg:max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                  <input
                    type="text"
                    placeholder="Search drones, pilots, locations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2 lg:gap-3 lg:ml-6 flex-shrink-0">
                <button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className="p-2.5 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 disabled:opacity-50"
                  title="Refresh"
                >
                  <RefreshCw className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} />
                </button>

                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`p-2.5 rounded-lg transition-all duration-200 ${
                    showFilters
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                  }`}
                  title="Filters"
                >
                  <Filter className="w-5 h-5" />
                </button>

                <div className="relative">
                  <Bell className="w-5 h-5 text-gray-600" />
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                    3
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Dashboard */}
        <div className="p-4 lg:p-6 space-y-4 lg:space-y-6 max-w-full overflow-x-hidden">
          <div className="w-full overflow-x-auto">
            <DroneStats stats={stats} />
          </div>

          {/* Quick Actions */}
          <div className="w-full overflow-x-auto">
            <DroneActions
              selectedDrones={selectedDrones}
              onAddDrone={() => navigate("/orgadddrone")}
              onExport={() => {/* Export logic */}}
              onBulkStatusUpdate={handleBulkStatusUpdate}
              onBulkDelete={handleBulkDelete}
              onBulkAssignPilot={handleBulkAssignPilot}
              onBulkAssignLocation={handleBulkAssignLocation}
              onBulkScheduleMaintenance={handleBulkScheduleMaintenance}
              setSelectedDrones={setSelectedDrones}
            />
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="w-full overflow-x-auto">
              <DroneFilters
                selectedRegion={selectedRegion}
                setSelectedRegion={setSelectedRegion}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                selectedDeployment={selectedDeployment}
                setSelectedDeployment={setSelectedDeployment}
                sortOrder={sortOrder}
                setSortOrder={setSortOrder}
              />
            </div>
          )}

          {/* Drone Display */}
          <div className="space-y-4">
            {/* Results Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h2 className="text-xl font-semibold text-gray-800">
                  Drone Fleet
                </h2>
                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                  {filteredDrones.length} drones
                </span>
              </div>

              {selectedDrones.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">
                    {selectedDrones.length} selected
                  </span>
                  <button
                    onClick={() => setSelectedDrones([])}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Clear selection
                  </button>
                </div>
              )}
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-3 text-gray-600">Loading drones...</span>
                </div>
              </div>
            )}

            {/* Error State */}
            {error && !isLoading && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div className="text-center">
                  <div className="text-red-600 mb-2">⚠️ Error</div>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <button
                    onClick={handleRefresh}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            )}

            {/* Empty State */}
            {!isLoading && !error && drones.length === 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div className="text-center">
                  <Plane className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No drones found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchQuery || selectedStatus !== 'All'
                      ? 'No drones match your current filters.'
                      : 'You haven\'t added any drones yet.'}
                  </p>
                  <button
                    onClick={() => navigate("/orgadddrone")}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Add Your First Drone
                  </button>
                </div>
              </div>
            )}

            {/* Drone Table */}
            {!isLoading && !error && drones.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden w-full">
                <div className="overflow-x-auto w-full text-left">
                  <table className="w-full min-w-full table-auto">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input
                          type="checkbox"
                          checked={selectedDrones.length === paginatedDrones.length}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedDrones(paginatedDrones.map(d => d.id));
                            } else {
                              setSelectedDrones([]);
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Drone</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pilot</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Battery</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Efficiency</th>
                      <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {paginatedDrones.map((drone) => (
                      <tr key={drone.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={selectedDrones.includes(drone.id)}
                            onChange={() => handleDroneSelect(drone.id)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                                <Plane className="h-5 w-5 text-white" />
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{drone.name}</div>
                              <div className="text-sm text-gray-500">{drone.id}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClasses[drone.status]}`}>
                            {drone.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left">
                          <div className="text-left">
                            <MapPin className="h-4 w-4 text-gray-400 inline mr-1" />
                            {drone.location || '-'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left">
                          {drone.pilot || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-left">
                          <div className="text-left">
                            <Battery className={`h-4 w-4 inline mr-2 ${
                              drone.batteryLevel > 50 ? 'text-green-500' :
                              drone.batteryLevel > 20 ? 'text-yellow-500' : 'text-red-500'
                            }`} />
                            <span className="text-sm text-gray-900">{drone.batteryLevel}%</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {drone.efficiency.toFixed(1)}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-left">
                          <button
                            onClick={() => handleViewDroneDetails(drone.id)}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            className="text-gray-600 hover:text-gray-900"
                            title="Settings"
                          >
                            <Settings className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            )}

            {/* Enhanced Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing {((currentPage - 1) * rowsPerPage) + 1} to {Math.min(currentPage * rowsPerPage, filteredDrones.length)} of {filteredDrones.length} results
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  {[...Array(totalPages)].map((_, i) => {
                    const page = i + 1;
                    if (page === 1 || page === totalPages || (page >= currentPage - 1 && page <= currentPage + 1)) {
                      return (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`px-3 py-2 text-sm font-medium rounded-md ${
                            currentPage === page
                              ? 'bg-blue-600 text-white'
                              : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    } else if (page === currentPage - 2 || page === currentPage + 2) {
                      return <span key={page} className="px-2 text-gray-500">...</span>;
                    }
                    return null;
                  })}

                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Drone Details Modal */}
      {showDroneDetailsModal && selectedDroneDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center">
                <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mr-4">
                  <Plane className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{selectedDroneDetails.name}</h2>
                  <p className="text-gray-600">{selectedDroneDetails.model}</p>
                </div>
              </div>
              <button
                onClick={() => setShowDroneDetailsModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <XCircle className="h-6 w-6" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Basic Information</h3>
                  <div className="space-y-2">
                    <div><span className="font-medium">Name:</span> {selectedDroneDetails.name || 'N/A'}</div>
                    <div><span className="font-medium">Model:</span> {selectedDroneDetails.model || 'N/A'}</div>
                    <div><span className="font-medium">Manufacturer:</span> {selectedDroneDetails.manufacturer || 'N/A'}</div>
                    <div><span className="font-medium">Type:</span> {selectedDroneDetails.droneType || 'N/A'}</div>
                    <div><span className="font-medium">Serial Number:</span> {selectedDroneDetails.serialNumber || 'N/A'}</div>
                    <div><span className="font-medium">Registration:</span> {selectedDroneDetails.registrationNumber || 'N/A'}</div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Status & Performance</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="font-medium">Status:</span>
                      <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClasses[selectedDroneDetails.status]}`}>
                        {selectedDroneDetails.status}
                      </span>
                    </div>
                    <div><span className="font-medium">Battery Level:</span> {selectedDroneDetails.batteryLevel || 0}%</div>
                    <div><span className="font-medium">Flight Hours:</span> {selectedDroneDetails.flightHours || 0}</div>
                    <div><span className="font-medium">Efficiency:</span> {selectedDroneDetails.efficiency || 0}%</div>
                    <div><span className="font-medium">Total Missions:</span> {selectedDroneDetails.missions || 0}</div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Location & Deployment</h3>
                  <div className="space-y-2">
                    <div><span className="font-medium">Current Location:</span> {selectedDroneDetails.location || 'Not specified'}</div>
                    <div><span className="font-medium">Assigned Pilot:</span> {selectedDroneDetails.pilot || 'Not assigned'}</div>
                    <div><span className="font-medium">Deploy Date:</span> {selectedDroneDetails.deployDate || 'Not deployed'}</div>
                    <div><span className="font-medium">Altitude:</span> {selectedDroneDetails.altitude || 0} m</div>
                    <div><span className="font-medium">Speed:</span> {selectedDroneDetails.speed || 0} km/h</div>
                  </div>
                </div>
              </div>

              {/* Specifications */}
              {selectedDroneDetails.specifications && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Technical Specifications</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div><span className="font-medium">Battery Capacity:</span> {selectedDroneDetails.specifications.batteryCapacity || 'N/A'} mAh</div>
                    <div><span className="font-medium">Camera Resolution:</span> {selectedDroneDetails.specifications.cameraResolution || 'N/A'}</div>
                    <div><span className="font-medium">GPS:</span> {selectedDroneDetails.specifications.hasGPS ? 'Yes' : 'No'}</div>
                    <div><span className="font-medium">Gimbal:</span> {selectedDroneDetails.specifications.hasGimbal ? 'Yes' : 'No'}</div>
                    <div><span className="font-medium">Obstacle Avoidance:</span> {selectedDroneDetails.specifications.hasObstacleAvoidance ? 'Yes' : 'No'}</div>
                  </div>
                </div>
              )}

              {/* Purchase Information */}
              {selectedDroneDetails.purchase && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Purchase Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div><span className="font-medium">Purchase Date:</span> {selectedDroneDetails.purchase.purchaseDate || 'N/A'}</div>
                    <div><span className="font-medium">Purchase Price:</span> ₹{selectedDroneDetails.purchase.purchasePrice || 'N/A'}</div>
                    <div><span className="font-medium">Vendor:</span> {selectedDroneDetails.purchase.vendor || 'N/A'}</div>
                  </div>
                </div>
              )}

              {/* Maintenance Information */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Maintenance</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div><span className="font-medium">Last Maintenance:</span> {selectedDroneDetails.lastMaintenance || 'No maintenance recorded'}</div>
                  <div><span className="font-medium">Next Maintenance:</span> {selectedDroneDetails.nextMaintenance || 'Not scheduled'}</div>
                </div>
              </div>

              {/* Environmental Data */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">Environmental Data</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div><span className="font-medium">Temperature:</span> {selectedDroneDetails.temperature || 0}°C</div>
                  <div><span className="font-medium">Signal Strength:</span> {selectedDroneDetails.signalStrength || 0}%</div>
                  <div><span className="font-medium">Coordinates:</span> {selectedDroneDetails.coordinates?.lat || 0}, {selectedDroneDetails.coordinates?.lng || 0}</div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex items-center justify-end p-6 border-t border-gray-200 space-x-3">
              <button
                onClick={() => setShowDroneDetailsModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
              <button
                onClick={() => {
                  setShowDroneDetailsModal(false);
                  navigate(`/orgdrone/${selectedDroneDetails.id}/edit`);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Edit Drone
              </button>
            </div>
          </div>
        </div>
      )}

      {/* React Hot Toast */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            theme: {
              primary: 'green',
              secondary: 'black',
            },
          },
          error: {
            duration: 4000,
            theme: {
              primary: 'red',
              secondary: 'black',
            },
          },
        }}
      />
    </div>
  );
};

export default OrgDronePage;
