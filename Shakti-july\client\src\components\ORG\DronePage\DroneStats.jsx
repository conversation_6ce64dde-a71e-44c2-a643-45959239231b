import React from 'react';
import {
  Plane,
  Activity,
  Battery,
  Zap,
  Clock,
  Target,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Wrench,
  XCircle
} from 'lucide-react';

const DroneStats = ({ stats }) => {
  const statCards = [
    {
      title: 'Total Drones',
      value: stats.total,
      icon: Plane,
      color: 'blue',
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Active Drones',
      value: stats.active,
      icon: CheckCircle,
      color: 'green',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600',
      borderColor: 'border-green-200',
      percentage: ((stats.active / stats.total) * 100).toFixed(1)
    },
    {
      title: 'Inactive Drones',
      value: stats.inactive,
      icon: XCircle,
      color: 'gray',
      bgColor: 'bg-gray-50',
      iconColor: 'text-gray-600',
      borderColor: 'border-gray-200',
      percentage: ((stats.inactive / stats.total) * 100).toFixed(1)
    },
    {
      title: 'In Maintenance',
      value: stats.maintenance,
      icon: Wrench,
      color: 'yellow',
      bgColor: 'bg-yellow-50',
      iconColor: 'text-yellow-600',
      borderColor: 'border-yellow-200',
      percentage: ((stats.maintenance / stats.total) * 100).toFixed(1)
    },
    {
      title: 'Crashed',
      value: stats.crashed,
      icon: AlertTriangle,
      color: 'red',
      bgColor: 'bg-red-50',
      iconColor: 'text-red-600',
      borderColor: 'border-red-200',
      percentage: ((stats.crashed / stats.total) * 100).toFixed(1)
    },
    {
      title: 'Pending Approval',
      value: stats.pending,
      icon: Clock,
      color: 'indigo',
      bgColor: 'bg-indigo-50',
      iconColor: 'text-indigo-600',
      borderColor: 'border-indigo-200',
      percentage: ((stats.pending / stats.total) * 100).toFixed(1)
    }
  ];

  const performanceCards = [
    {
      title: 'Average Efficiency',
      value: `${stats.avgEfficiency.toFixed(1)}%`,
      icon: Zap,
      color: 'purple',
      bgColor: 'bg-purple-50',
      iconColor: 'text-purple-600',
      borderColor: 'border-purple-200',
      trend: stats.avgEfficiency >= 85 ? 'up' : 'down',
      trendValue: '2.3%'
    },
    {
      title: 'Average Battery',
      value: `${stats.avgBattery.toFixed(1)}%`,
      icon: Battery,
      color: 'emerald',
      bgColor: 'bg-emerald-50',
      iconColor: 'text-emerald-600',
      borderColor: 'border-emerald-200',
      trend: stats.avgBattery >= 70 ? 'up' : 'down',
      trendValue: '5.1%'
    },
    {
      title: 'Total Flight Hours',
      value: stats.totalFlightHours.toFixed(1),
      icon: Clock,
      color: 'orange',
      bgColor: 'bg-orange-50',
      iconColor: 'text-orange-600',
      borderColor: 'border-orange-200',
      trend: 'up',
      trendValue: '12.5%'
    },
    {
      title: 'Total Missions',
      value: stats.totalMissions,
      icon: Target,
      color: 'teal',
      bgColor: 'bg-teal-50',
      iconColor: 'text-teal-600',
      borderColor: 'border-teal-200',
      trend: 'up',
      trendValue: '8.7%'
    }
  ];

  const StatCard = ({ title, value, icon: Icon, bgColor, iconColor, borderColor, percentage, trend, trendValue }) => (
    <div className={`${bgColor} ${borderColor} border rounded-xl p-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`p-3 ${bgColor} rounded-lg border ${borderColor}`}>
            <Icon className={`w-6 h-6 ${iconColor}`} />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {percentage && (
              <p className="text-xs text-gray-500 mt-1">{percentage}% of total</p>
            )}
          </div>
        </div>
        
        {trend && trendValue && (
          <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
            trend === 'up' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {trend === 'up' ? (
              <TrendingUp className="w-3 h-3" />
            ) : (
              <TrendingDown className="w-3 h-3" />
            )}
            {trendValue}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Fleet Overview */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Activity className="w-5 h-5 text-gray-600" />
          <h2 className="text-xl font-semibold text-gray-800">Fleet Overview</h2>
        </div>
        <div className="drone-stats-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 lg:gap-4">
          {statCards.map((card, index) => (
            <StatCard key={index} {...card} />
          ))}
        </div>
      </div>

      {/* Performance Metrics */}
      <div>
        <div className="flex items-center gap-2 mb-4">
          <TrendingUp className="w-5 h-5 text-gray-600" />
          <h2 className="text-xl font-semibold text-gray-800">Performance Metrics</h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4">
          {performanceCards.map((card, index) => (
            <StatCard key={index} {...card} />
          ))}
        </div>
      </div>

      {/* Fleet Health Summary */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center gap-2 mb-4">
          <CheckCircle className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-800">Fleet Health Summary</h3>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
          {/* Operational Status */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-700">Operational Status</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Operational</span>
                <span className="text-sm font-medium text-green-600">
                  {stats.active} ({((stats.active / stats.total) * 100).toFixed(1)}%)
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(stats.active / stats.total) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Maintenance Status */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-700">Maintenance Required</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Needs Attention</span>
                <span className="text-sm font-medium text-yellow-600">
                  {stats.maintenance + stats.crashed} ({(((stats.maintenance + stats.crashed) / stats.total) * 100).toFixed(1)}%)
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((stats.maintenance + stats.crashed) / stats.total) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Deployment Status */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-700">Deployment Status</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Ready for Deployment</span>
                <span className="text-sm font-medium text-blue-600">
                  {stats.inactive + stats.pending} ({(((stats.inactive + stats.pending) / stats.total) * 100).toFixed(1)}%)
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((stats.inactive + stats.pending) / stats.total) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DroneStats;
