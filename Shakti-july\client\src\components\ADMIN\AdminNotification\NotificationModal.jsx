import React from 'react';
import {
  X,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Wifi,
  Battery,
  Wrench,
  Cloud,
  MapPin,
  Download,
  Eye,
  Archive,
  Trash2,
  ExternalLink,
  Copy,
  Share2,
  Calendar,
  User,
  Building,
  Navigation
} from 'lucide-react';

const NotificationModal = ({
  notification,
  isOpen,
  onClose,
  onMarkAsRead,
  onArchive,
  onDelete,
  onViewDrone
}) => {
  if (!isOpen || !notification) return null;

  // Icon mapping
  const iconMap = {
    AlertTriangle: AlertTriangle,
    CheckCircle2: CheckCircle2,
    Clock: Clock,
    Wifi: Wifi,
    Battery: Battery,
    Wrench: Wrench,
    Cloud: Cloud,
    MapPin: MapPin,
    Download: Download
  };

  // Color mapping
  const colorMap = {
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      text: 'text-red-600',
      dot: 'bg-red-500',
      button: 'bg-red-600 hover:bg-red-700'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      text: 'text-orange-600',
      dot: 'bg-orange-500',
      button: 'bg-orange-600 hover:bg-orange-700'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-600',
      dot: 'bg-yellow-500',
      button: 'bg-yellow-600 hover:bg-yellow-700'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      text: 'text-green-600',
      dot: 'bg-green-500',
      button: 'bg-green-600 hover:bg-green-700'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      text: 'text-blue-600',
      dot: 'bg-blue-500',
      button: 'bg-blue-600 hover:bg-blue-700'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      text: 'text-purple-600',
      dot: 'bg-purple-500',
      button: 'bg-purple-600 hover:bg-purple-700'
    }
  };

  const IconComponent = iconMap[notification.icon] || AlertTriangle;
  const colors = colorMap[notification.color] || colorMap.blue;

  // Get priority badge
  const getPriorityBadge = (priority) => {
    const badges = {
      critical: { label: 'Critical', color: 'bg-red-100 text-red-800 border-red-200' },
      high: { label: 'High', color: 'bg-orange-100 text-orange-800 border-orange-200' },
      medium: { label: 'Medium', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      low: { label: 'Low', color: 'bg-green-100 text-green-800 border-green-200' }
    };
    
    const badge = badges[priority] || badges.low;
    return (
      <span className={`px-3 py-1 text-sm font-medium rounded-full border ${badge.color}`}>
        {badge.label}
      </span>
    );
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(timestamp);
  };

  // Copy to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className={`${colors.bg} ${colors.border} border-b px-6 py-4`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className={`w-12 h-12 ${colors.bg} ${colors.border} border-2 rounded-lg flex items-center justify-center`}>
                <IconComponent className={`w-6 h-6 ${colors.text}`} />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">{notification.title}</h2>
                <div className="flex items-center gap-3 mt-1">
                  {getPriorityBadge(notification.priority)}
                  <span className="text-sm text-gray-600 capitalize">{notification.category}</span>
                  {!notification.isRead && (
                    <span className="flex items-center gap-1 text-sm text-blue-600">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Unread
                    </span>
                  )}
                </div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-50 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Message */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Details</h3>
            <p className="text-gray-700 leading-relaxed">{notification.message}</p>
          </div>

          {/* Drone Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Navigation className="w-4 h-4" />
                Drone Information
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Name:</span>
                  <span className="text-sm font-medium text-gray-900">{notification.droneName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">ID:</span>
                  <span className="text-sm font-medium text-gray-900 font-mono">{notification.droneId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Location:</span>
                  <span className="text-sm font-medium text-gray-900">{notification.location}</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Building className="w-4 h-4" />
                Organization
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Name:</span>
                  <span className="text-sm font-medium text-gray-900">{notification.organization}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Type:</span>
                  <span className="text-sm font-medium text-gray-900">{notification.type}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Metadata */}
          {notification.metadata && Object.keys(notification.metadata).length > 0 && (
            <div className="mb-6">
              <h4 className="text-sm font-semibold text-gray-900 mb-3">Additional Information</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(notification.metadata).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-sm text-gray-600 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}:
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : value}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Timestamp */}
          <div className="mb-6">
            <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Timestamp
            </h4>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-900">{formatTimestamp(notification.timestamp)}</span>
                <button
                  onClick={() => copyToClipboard(notification.timestamp.toISOString())}
                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                  title="Copy timestamp"
                >
                  <Copy className="w-4 h-4 text-gray-500" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="border-t border-gray-200 px-6 py-4 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                onClick={() => onViewDrone(notification.droneId)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <ExternalLink className="w-4 h-4" />
                View Drone
              </button>
              <button
                onClick={() => copyToClipboard(notification.id)}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Copy className="w-4 h-4" />
                Copy ID
              </button>
            </div>

            <div className="flex items-center gap-3">
              {!notification.isRead && (
                <button
                  onClick={() => {
                    onMarkAsRead(notification.id);
                    onClose();
                  }}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Eye className="w-4 h-4" />
                  Mark as Read
                </button>
              )}
              <button
                onClick={() => {
                  onArchive(notification.id);
                  onClose();
                }}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Archive className="w-4 h-4" />
                Archive
              </button>
              <button
                onClick={() => {
                  onDelete(notification.id);
                  onClose();
                }}
                className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationModal;
