const mongoose = require('mongoose');

const droneSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: [true, 'Drone name is required'],
    trim: true,
    maxlength: [100, 'Drone name cannot exceed 100 characters']
  },
  model: {
    type: String,
    required: [true, 'Drone model is required'],
    trim: true,
    maxlength: [100, 'Drone model cannot exceed 100 characters']
  },
  manufacturer: {
    type: String,
    required: [true, 'Manufacturer is required'],
    trim: true,
    maxlength: [100, 'Manufacturer name cannot exceed 100 characters']
  },
  serialNumber: {
    type: String,
    required: [true, 'Serial number is required'],
    unique: true,
    trim: true,
    maxlength: [50, 'Serial number cannot exceed 50 characters']
  },
  registrationNumber: {
    type: String,
    required: [true, 'Registration number is required'],
    unique: true,
    trim: true,
    maxlength: [50, 'Registration number cannot exceed 50 characters']
  },

  // Organization Reference
  organizationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization',
    required: [true, 'Organization reference is required']
  },

  // Technical Specifications
  specifications: {
    type: {
      type: String,
      required: [true, 'Drone type is required'],
      enum: ['quadcopter', 'hexacopter', 'octocopter', 'fixed-wing', 'hybrid', 'other'],
      default: 'quadcopter'
    },
    weight: {
      type: Number,
      required: [true, 'Weight is required'],
      min: [0.1, 'Weight must be at least 0.1 kg'],
      max: [25, 'Weight cannot exceed 25 kg']
    },
    maxPayload: {
      type: Number,
      required: [true, 'Maximum payload is required'],
      min: [0, 'Maximum payload cannot be negative'],
      max: [10, 'Maximum payload cannot exceed 10 kg']
    },
    maxFlightTime: {
      type: Number,
      required: [true, 'Maximum flight time is required'],
      min: [1, 'Maximum flight time must be at least 1 minute'],
      max: [300, 'Maximum flight time cannot exceed 300 minutes']
    },
    maxRange: {
      type: Number,
      required: [true, 'Maximum range is required'],
      min: [0.1, 'Maximum range must be at least 0.1 km'],
      max: [100, 'Maximum range cannot exceed 100 km']
    },
    maxAltitude: {
      type: Number,
      required: [true, 'Maximum altitude is required'],
      min: [1, 'Maximum altitude must be at least 1 meter'],
      max: [500, 'Maximum altitude cannot exceed 500 meters']
    },
    maxSpeed: {
      type: Number,
      required: [true, 'Maximum speed is required'],
      min: [1, 'Maximum speed must be at least 1 km/h'],
      max: [200, 'Maximum speed cannot exceed 200 km/h']
    },
    batteryCapacity: {
      type: Number,
      required: [true, 'Battery capacity is required'],
      min: [100, 'Battery capacity must be at least 100 mAh'],
      max: [50000, 'Battery capacity cannot exceed 50000 mAh']
    },
    cameraResolution: {
      type: String,
      trim: true,
      maxlength: [50, 'Camera resolution cannot exceed 50 characters']
    },
    hasGimbal: {
      type: Boolean,
      default: false
    },
    hasGPS: {
      type: Boolean,
      default: true
    },
    hasObstacleAvoidance: {
      type: Boolean,
      default: false
    }
  },

  // Status and Operational Information
  status: {
    type: String,
    required: [true, 'Drone status is required'],
    enum: ['active', 'inactive', 'maintenance', 'retired', 'lost'],
    default: 'active'
  },
  
  condition: {
    type: String,
    required: [true, 'Drone condition is required'],
    enum: ['excellent', 'good', 'fair', 'poor', 'damaged'],
    default: 'excellent'
  },

  // Current Location and Tracking
  currentLocation: {
    latitude: {
      type: Number,
      min: [-90, 'Latitude must be between -90 and 90'],
      max: [90, 'Latitude must be between -90 and 90']
    },
    longitude: {
      type: Number,
      min: [-180, 'Longitude must be between -180 and 180'],
      max: [180, 'Longitude must be between -180 and 180']
    },
    altitude: {
      type: Number,
      min: [0, 'Altitude cannot be negative']
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },

  // Flight Statistics
  flightStats: {
    totalFlights: {
      type: Number,
      default: 0,
      min: [0, 'Total flights cannot be negative']
    },
    totalFlightTime: {
      type: Number,
      default: 0,
      min: [0, 'Total flight time cannot be negative']
    },
    totalDistance: {
      type: Number,
      default: 0,
      min: [0, 'Total distance cannot be negative']
    },
    lastFlightDate: {
      type: Date,
      default: null
    },
    averageFlightTime: {
      type: Number,
      default: 0,
      min: [0, 'Average flight time cannot be negative']
    }
  },

  // Maintenance Information
  maintenance: {
    lastMaintenanceDate: {
      type: Date,
      default: null
    },
    nextMaintenanceDate: {
      type: Date,
      default: null
    },
    maintenanceIntervalHours: {
      type: Number,
      default: 50,
      min: [1, 'Maintenance interval must be at least 1 hour']
    },
    totalMaintenanceHours: {
      type: Number,
      default: 0,
      min: [0, 'Total maintenance hours cannot be negative']
    }
  },

  // Insurance and Legal
  insurance: {
    provider: {
      type: String,
      trim: true,
      maxlength: [100, 'Insurance provider name cannot exceed 100 characters']
    },
    policyNumber: {
      type: String,
      trim: true,
      maxlength: [50, 'Policy number cannot exceed 50 characters']
    },
    expiryDate: {
      type: Date
    },
    coverageAmount: {
      type: Number,
      min: [0, 'Coverage amount cannot be negative']
    }
  },

  // Purchase Information
  purchase: {
    purchaseDate: {
      type: Date,
      required: [true, 'Purchase date is required']
    },
    purchasePrice: {
      type: Number,
      required: [true, 'Purchase price is required'],
      min: [0, 'Purchase price cannot be negative']
    },
    vendor: {
      type: String,
      required: [true, 'Vendor is required'],
      trim: true,
      maxlength: [100, 'Vendor name cannot exceed 100 characters']
    },
    warrantyExpiryDate: {
      type: Date
    }
  },

  // Assigned Personnel
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },

  // Audit Trail
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Created by user is required']
  },
  
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },

  // Notes and Comments
  notes: [{
    content: {
      type: String,
      required: true,
      trim: true,
      maxlength: [1000, 'Note content cannot exceed 1000 characters']
    },
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    addedAt: {
      type: Date,
      default: Date.now
    },
    type: {
      type: String,
      enum: ['general', 'maintenance', 'incident', 'inspection'],
      default: 'general'
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
droneSchema.index({ serialNumber: 1 });
droneSchema.index({ registrationNumber: 1 });
droneSchema.index({ organizationId: 1 });
droneSchema.index({ status: 1 });
droneSchema.index({ assignedTo: 1 });
droneSchema.index({ 'currentLocation': '2dsphere' });

// Virtual for checking if maintenance is due
droneSchema.virtual('isMaintenanceDue').get(function() {
  if (!this.maintenance.nextMaintenanceDate) return false;
  return this.maintenance.nextMaintenanceDate <= new Date();
});

// Virtual for checking if warranty is valid
droneSchema.virtual('isWarrantyValid').get(function() {
  if (!this.purchase.warrantyExpiryDate) return false;
  return this.purchase.warrantyExpiryDate > new Date();
});

// Virtual for checking if insurance is valid
droneSchema.virtual('isInsuranceValid').get(function() {
  if (!this.insurance.expiryDate) return false;
  return this.insurance.expiryDate > new Date();
});

// Pre-save middleware to calculate next maintenance date
droneSchema.pre('save', function(next) {
  if (this.isModified('flightStats.totalFlightTime') || this.isModified('maintenance.lastMaintenanceDate')) {
    if (this.maintenance.lastMaintenanceDate && this.maintenance.maintenanceIntervalHours) {
      const nextMaintenanceHours = this.maintenance.maintenanceIntervalHours;
      const hoursToAdd = nextMaintenanceHours * 60 * 60 * 1000; // Convert to milliseconds
      this.maintenance.nextMaintenanceDate = new Date(this.maintenance.lastMaintenanceDate.getTime() + hoursToAdd);
    }
  }

  // Calculate average flight time
  if (this.flightStats.totalFlights > 0) {
    this.flightStats.averageFlightTime = this.flightStats.totalFlightTime / this.flightStats.totalFlights;
  }

  next();
});

// Static method to find drones by organization
droneSchema.statics.findByOrganization = function(organizationId, options = {}) {
  const filter = { organizationId };

  if (options.status) {
    filter.status = options.status;
  }

  if (options.condition) {
    filter.condition = options.condition;
  }

  if (options.assignedTo) {
    filter.assignedTo = options.assignedTo;
  }

  return this.find(filter);
};

// Static method to get drone statistics by organization
droneSchema.statics.getOrganizationStats = async function(organizationId) {
  const stats = await this.aggregate([
    { $match: { organizationId: mongoose.Types.ObjectId(organizationId) } },
    {
      $group: {
        _id: null,
        totalDrones: { $sum: 1 },
        activeDrones: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        inactiveDrones: {
          $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
        },
        maintenanceDrones: {
          $sum: { $cond: [{ $eq: ['$status', 'maintenance'] }, 1, 0] }
        },
        totalFlightTime: { $sum: '$flightStats.totalFlightTime' },
        totalFlights: { $sum: '$flightStats.totalFlights' },
        totalDistance: { $sum: '$flightStats.totalDistance' },
        averageFlightTime: { $avg: '$flightStats.averageFlightTime' }
      }
    }
  ]);

  return stats[0] || {
    totalDrones: 0,
    activeDrones: 0,
    inactiveDrones: 0,
    maintenanceDrones: 0,
    totalFlightTime: 0,
    totalFlights: 0,
    totalDistance: 0,
    averageFlightTime: 0
  };
};

// Instance method to add note
droneSchema.methods.addNote = function(content, addedBy, type = 'general') {
  this.notes.push({
    content,
    addedBy,
    type,
    addedAt: new Date()
  });
  return this.save();
};

// Instance method to update flight statistics
droneSchema.methods.updateFlightStats = async function(flightData) {
  this.flightStats.totalFlights += 1;
  this.flightStats.totalFlightTime += flightData.flightTime || 0;
  this.flightStats.totalDistance += flightData.distance || 0;
  this.flightStats.lastFlightDate = new Date();

  if (flightData.location) {
    this.currentLocation = {
      ...flightData.location,
      lastUpdated: new Date()
    };
  }

  return this.save();
};

// Instance method to assign to user
droneSchema.methods.assignToUser = async function(userId) {
  this.assignedTo = userId;
  return this.save();
};

// Instance method to unassign from user
droneSchema.methods.unassign = async function() {
  this.assignedTo = null;
  return this.save();
};

module.exports = mongoose.model('Drone', droneSchema);
