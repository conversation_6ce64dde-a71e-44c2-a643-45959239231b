import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  Wrench,
  Activity,
  Battery,
  Thermometer,
  Gauge,
  TrendingUp,
  TrendingDown,
  Calendar,
  FileText,
  Users,
  Settings
} from 'lucide-react';
import QCLayout from '../common/QCLayout';
// import MaintenanceOverview from './MaintenanceOverview';
// import DroneHealthMetrics from './DroneHealthMetrics';
// import AlertsPanel from './AlertsPanel';

const QCDashboard = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState('today');

  // Mock data for dashboard metrics
  const dashboardStats = {
    totalDrones: 24,
    activeDrones: 18,
    maintenanceDue: 3,
    criticalAlerts: 2,
    completedToday: 5,
    avgHealthScore: 87.5,
    uptime: 94.2,
    efficiency: 91.8
  };

  const quickActions = [
    {
      id: 1,
      title: 'Schedule Maintenance',
      description: 'Plan preventive maintenance',
      icon: Calendar,
      color: '#e0e7ff',
      hoverColor: '#c7d2fe',
      path: '/qc-maintenance/scheduler'
    },
    {
      id: 2,
      title: 'Create Work Order',
      description: 'New maintenance task',
      icon: FileText,
      color: '#e0e7ff',
      hoverColor: '#c7d2fe',
      path: '/qc-maintenance/work-orders'
    },
    {
      id: 3,
      title: 'Health Monitor',
      description: 'Real-time diagnostics',
      icon: Activity,
      color: '#f3e8ff',
      hoverColor: '#e9d5ff',
      path: '/qc-diagnostics/health'
    },
    {
      id: 4,
      title: 'Quality Check',
      description: 'Inspection checklist',
      icon: CheckCircle,
      color: '#f0fdfa',
      hoverColor: '#ccfbf1',
      path: '/qc-inspections/quality'
    }
  ];

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const headerActions = (
    <div className="flex items-center gap-3">
      <select
        value={selectedTimeRange}
        onChange={(e) => setSelectedTimeRange(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="today">Today</option>
        <option value="week">This Week</option>
        <option value="month">This Month</option>
        <option value="quarter">This Quarter</option>
      </select>
    </div>
  );

  return (
    <QCLayout
      title="QC Dashboard"
      subtitle="Quality Control & Maintenance Overview"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Drones */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Drones</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardStats.totalDrones}</p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <TrendingUp className="w-3 h-3" />
                  +2 this month
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Activity className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          {/* Active Drones */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Drones</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardStats.activeDrones}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {((dashboardStats.activeDrones / dashboardStats.totalDrones) * 100).toFixed(1)}% operational
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          {/* Maintenance Due */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Maintenance Due</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardStats.maintenanceDue}</p>
                <p className="text-sm text-purple-500 flex items-center gap-1 mt-1">
                  <Clock className="w-3 h-3" />
                  Requires attention
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#faf5ff'}}>
                <Wrench className="w-6 h-6 text-purple-500" />
              </div>
            </div>
          </div>

          {/* Critical Alerts */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Critical Alerts</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardStats.criticalAlerts}</p>
                <p className="text-sm text-orange-500 flex items-center gap-1 mt-1">
                  <AlertTriangle className="w-3 h-3" />
                  Immediate action
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fffbeb'}}>
                <AlertTriangle className="w-6 h-6 text-orange-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Fleet Health Score</h3>
            <div className="flex items-center justify-center">
              <div className="relative w-32 h-32">
                <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    stroke="#e5e7eb"
                    strokeWidth="8"
                    fill="none"
                  />
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    stroke="#a5b4fc"
                    strokeWidth="8"
                    fill="none"
                    strokeDasharray={`${(dashboardStats.avgHealthScore / 100) * 314} 314`}
                    strokeLinecap="round"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-2xl font-bold text-gray-900">{dashboardStats.avgHealthScore}%</span>
                </div>
              </div>
            </div>
            <p className="text-center text-sm text-gray-600 mt-2">Excellent condition</p>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">System Uptime</h3>
            <div className="flex items-center justify-center">
              <div className="relative w-32 h-32">
                <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    stroke="#e5e7eb"
                    strokeWidth="8"
                    fill="none"
                  />
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    stroke="#c7d2fe"
                    strokeWidth="8"
                    fill="none"
                    strokeDasharray={`${(dashboardStats.uptime / 100) * 314} 314`}
                    strokeLinecap="round"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-2xl font-bold text-gray-900">{dashboardStats.uptime}%</span>
                </div>
              </div>
            </div>
            <p className="text-center text-sm text-gray-600 mt-2">Last 30 days</p>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Efficiency Rating</h3>
            <div className="flex items-center justify-center">
              <div className="relative w-32 h-32">
                <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    stroke="#e5e7eb"
                    strokeWidth="8"
                    fill="none"
                  />
                  <circle
                    cx="60"
                    cy="60"
                    r="50"
                    stroke="#e9d5ff"
                    strokeWidth="8"
                    fill="none"
                    strokeDasharray={`${(dashboardStats.efficiency / 100) * 314} 314`}
                    strokeLinecap="round"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-2xl font-bold text-gray-900">{dashboardStats.efficiency}%</span>
                </div>
              </div>
            </div>
            <p className="text-center text-sm text-gray-600 mt-2">Above target</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => {
              const Icon = action.icon;
              return (
                <button
                  key={action.id}
                  className="p-4 rounded-lg transition-all duration-150 ease-out group text-gray-700 hover:shadow-lg hover:-translate-y-0.5 transform relative overflow-hidden"
                  style={{
                    backgroundColor: action.color,
                  }}
                >
                  {/* Hover overlay */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-150 ease-out"
                    style={{backgroundColor: action.hoverColor}}
                  />

                  <div className="flex items-center gap-3 relative z-10">
                    <Icon className="w-6 h-6 text-blue-600 transition-transform duration-150 group-hover:scale-105" />
                    <div className="text-left">
                      <div className="font-medium">{action.title}</div>
                      <div className="text-sm text-gray-600">{action.description}</div>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Main Dashboard Components */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          <div className="xl:col-span-2">
            {/* Maintenance Overview */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 h-[600px] flex flex-col">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Maintenance Overview</h3>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                    {selectedTimeRange === 'today' ? 'Today' : selectedTimeRange === 'week' ? 'This Week' : 'This Month'}
                  </span>
                </div>
              </div>

              {/* Maintenance Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4 flex-shrink-0">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-3 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-blue-700">Scheduled</p>
                      <p className="text-xl font-bold text-blue-900">8</p>
                    </div>
                    <Calendar className="w-6 h-6 text-blue-600" />
                  </div>
                  <p className="text-xs text-blue-600 mt-1">Next 7 days</p>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-green-100 p-3 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-green-700">Completed</p>
                      <p className="text-xl font-bold text-green-900">12</p>
                    </div>
                    <CheckCircle className="w-6 h-6 text-green-600" />
                  </div>
                  <p className="text-xs text-green-600 mt-1">This week</p>
                </div>

                <div className="bg-gradient-to-r from-orange-50 to-orange-100 p-3 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-orange-700">Overdue</p>
                      <p className="text-xl font-bold text-orange-900">3</p>
                    </div>
                    <AlertTriangle className="w-6 h-6 text-orange-600" />
                  </div>
                  <p className="text-xs text-orange-600 mt-1">Requires attention</p>
                </div>
              </div>

              {/* Recent Maintenance Activities */}
              <div className="flex-1 flex flex-col min-h-0">
                <h4 className="text-sm font-semibold text-gray-900 mb-2 flex-shrink-0">Recent Activities</h4>
                <div className="space-y-2 flex-1 overflow-y-auto pr-1">
                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium text-gray-900 truncate">Battery Replacement - ARJUNA-001</p>
                      <p className="text-xs text-gray-500">Completed 2 hours ago</p>
                    </div>
                    <span className="px-1.5 py-0.5 bg-green-100 text-green-800 rounded text-xs flex-shrink-0">Completed</span>
                  </div>

                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium text-gray-900 truncate">Propeller Inspection - ARJUNA-003</p>
                      <p className="text-xs text-gray-500">In progress</p>
                    </div>
                    <span className="px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded text-xs flex-shrink-0">In Progress</span>
                  </div>

                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium text-gray-900 truncate">Sensor Calibration - ARJUNA-007</p>
                      <p className="text-xs text-gray-500">Scheduled tomorrow</p>
                    </div>
                    <span className="px-1.5 py-0.5 bg-yellow-100 text-yellow-800 rounded text-xs flex-shrink-0">Scheduled</span>
                  </div>

                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium text-gray-900 truncate">Motor Replacement - ARJUNA-012</p>
                      <p className="text-xs text-gray-500">Overdue by 2 days</p>
                    </div>
                    <span className="px-1.5 py-0.5 bg-red-100 text-red-800 rounded text-xs flex-shrink-0">Overdue</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div>
            {/* Critical Alerts Panel */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 h-[600px] flex flex-col">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Critical Alerts</h3>
                <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                  {dashboardStats.criticalAlerts} Active
                </span>
              </div>

              <div className="space-y-4 flex-1 overflow-y-auto">
                {/* High Priority Alert */}
                <div className="border-l-4 border-red-500 bg-red-50 p-4 rounded-r-lg">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-red-900">Battery Critical - ARJUNA-005</h4>
                      <p className="text-xs text-red-700 mt-1">Battery level at 8% - Immediate landing required</p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="px-2 py-1 bg-red-200 text-red-800 rounded text-xs">High Priority</span>
                        <span className="text-xs text-red-600">2 min ago</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Medium Priority Alert */}
                <div className="border-l-4 border-orange-500 bg-orange-50 p-4 rounded-r-lg">
                  <div className="flex items-start gap-3">
                    <Thermometer className="w-5 h-5 text-orange-600 mt-0.5" />
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-orange-900">Temperature Warning - ARJUNA-009</h4>
                      <p className="text-xs text-orange-700 mt-1">Motor temperature at 85°C - Above normal range</p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="px-2 py-1 bg-orange-200 text-orange-800 rounded text-xs">Medium Priority</span>
                        <span className="text-xs text-orange-600">5 min ago</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* System Alert */}
                <div className="border-l-4 border-yellow-500 bg-yellow-50 p-4 rounded-r-lg">
                  <div className="flex items-start gap-3">
                    <Settings className="w-5 h-5 text-yellow-600 mt-0.5" />
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-yellow-900">GPS Signal Weak - ARJUNA-014</h4>
                      <p className="text-xs text-yellow-700 mt-1">GPS accuracy reduced to 5m - Monitor closely</p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="px-2 py-1 bg-yellow-200 text-yellow-800 rounded text-xs">System Alert</span>
                        <span className="text-xs text-yellow-600">12 min ago</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Maintenance Alert */}
                <div className="border-l-4 border-purple-500 bg-purple-50 p-4 rounded-r-lg">
                  <div className="flex items-start gap-3">
                    <Wrench className="w-5 h-5 text-purple-600 mt-0.5" />
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-purple-900">Maintenance Due - ARJUNA-002</h4>
                      <p className="text-xs text-purple-700 mt-1">Scheduled maintenance overdue by 3 days</p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="px-2 py-1 bg-purple-200 text-purple-800 rounded text-xs">Maintenance</span>
                        <span className="text-xs text-purple-600">1 hour ago</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Alert */}
                <div className="border-l-4 border-blue-500 bg-blue-50 p-4 rounded-r-lg">
                  <div className="flex items-start gap-3">
                    <Settings className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-blue-900">Communication Loss - ARJUNA-011</h4>
                      <p className="text-xs text-blue-700 mt-1">Lost connection for 3 minutes - Auto-recovery initiated</p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="px-2 py-1 bg-blue-200 text-blue-800 rounded text-xs">System Alert</span>
                        <span className="text-xs text-blue-600">8 min ago</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Alert */}
                <div className="border-l-4 border-gray-500 bg-gray-50 p-4 rounded-r-lg">
                  <div className="flex items-start gap-3">
                    <Activity className="w-5 h-5 text-gray-600 mt-0.5" />
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-gray-900">Low Fuel Warning - ARJUNA-018</h4>
                      <p className="text-xs text-gray-700 mt-1">Fuel level at 15% - Return to base recommended</p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="px-2 py-1 bg-gray-200 text-gray-800 rounded text-xs">Low Priority</span>
                        <span className="text-xs text-gray-600">15 min ago</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Alert Actions */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                    View All Alerts
                  </button>
                  <button className="text-sm text-gray-600 hover:text-gray-800">
                    Mark All Read
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Drone Health Metrics */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Drone Health Metrics</h3>
            <div className="flex items-center gap-2">
              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                Live Data
              </span>
            </div>
          </div>

          {/* Health Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {/* Battery Health */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-semibold text-green-900">Battery Health</h4>
                <Battery className="w-5 h-5 text-green-600" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-green-700">Average Level</span>
                  <span className="text-sm font-bold text-green-900">78%</span>
                </div>
                <div className="w-full bg-green-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{width: '78%'}}></div>
                </div>
                <div className="flex justify-between text-xs text-green-600">
                  <span>18 Healthy</span>
                  <span>3 Low</span>
                  <span>1 Critical</span>
                </div>
              </div>
            </div>

            {/* Temperature Status */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-semibold text-blue-900">Temperature</h4>
                <Thermometer className="w-5 h-5 text-blue-600" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-blue-700">Average Temp</span>
                  <span className="text-sm font-bold text-blue-900">42°C</span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{width: '65%'}}></div>
                </div>
                <div className="flex justify-between text-xs text-blue-600">
                  <span>Normal Range</span>
                  <span>35-50°C</span>
                </div>
              </div>
            </div>

            {/* Performance Score */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-semibold text-purple-900">Performance</h4>
                <Gauge className="w-5 h-5 text-purple-600" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-purple-700">Overall Score</span>
                  <span className="text-sm font-bold text-purple-900">91.8%</span>
                </div>
                <div className="w-full bg-purple-200 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full" style={{width: '91.8%'}}></div>
                </div>
                <div className="flex justify-between text-xs text-purple-600">
                  <span>Excellent</span>
                  <span>Above Target</span>
                </div>
              </div>
            </div>

            {/* System Status */}
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-lg border border-orange-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-semibold text-orange-900">System Status</h4>
                <Activity className="w-5 h-5 text-orange-600" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-orange-700">Operational</span>
                  <span className="text-sm font-bold text-orange-900">20/24</span>
                </div>
                <div className="w-full bg-orange-200 rounded-full h-2">
                  <div className="bg-orange-600 h-2 rounded-full" style={{width: '83%'}}></div>
                </div>
                <div className="flex justify-between text-xs text-orange-600">
                  <span>4 Offline</span>
                  <span>83% Uptime</span>
                </div>
              </div>
            </div>
          </div>

          {/* Detailed Health Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Drone ID</th>
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Health Score</th>
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Battery</th>
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Temperature</th>
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Flight Hours</th>
                  <th className="text-left py-3 px-4 text-sm font-semibold text-gray-900">Status</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100 text-left">
                <tr className="hover:bg-gray-50">
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">ARJUNA-001</td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <div className="w-12 bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{width: '95%'}}></div>
                      </div>
                      <span className="text-sm text-gray-900">95%</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-900">87%</td>
                  <td className="py-3 px-4 text-sm text-gray-900">38°C</td>
                  <td className="py-3 px-4 text-sm text-gray-900">245.5h</td>
                  <td className="py-3 px-4">
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Excellent</span>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">ARJUNA-002</td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <div className="w-12 bg-gray-200 rounded-full h-2">
                        <div className="bg-yellow-600 h-2 rounded-full" style={{width: '72%'}}></div>
                      </div>
                      <span className="text-sm text-gray-900">72%</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-900">23%</td>
                  <td className="py-3 px-4 text-sm text-gray-900">52°C</td>
                  <td className="py-3 px-4 text-sm text-gray-900">189.2h</td>
                  <td className="py-3 px-4">
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Needs Attention</span>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">ARJUNA-003</td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <div className="w-12 bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{width: '88%'}}></div>
                      </div>
                      <span className="text-sm text-gray-900">88%</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-900">92%</td>
                  <td className="py-3 px-4 text-sm text-gray-900">41°C</td>
                  <td className="py-3 px-4 text-sm text-gray-900">312.4h</td>
                  <td className="py-3 px-4">
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Good</span>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">ARJUNA-004</td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <div className="w-12 bg-gray-200 rounded-full h-2">
                        <div className="bg-red-600 h-2 rounded-full" style={{width: '45%'}}></div>
                      </div>
                      <span className="text-sm text-gray-900">45%</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-900">8%</td>
                  <td className="py-3 px-4 text-sm text-gray-900">85°C</td>
                  <td className="py-3 px-4 text-sm text-gray-900">156.8h</td>
                  <td className="py-3 px-4">
                    <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Critical</span>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">ARJUNA-005</td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <div className="w-12 bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{width: '91%'}}></div>
                      </div>
                      <span className="text-sm text-gray-900">91%</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-900">78%</td>
                  <td className="py-3 px-4 text-sm text-gray-900">43°C</td>
                  <td className="py-3 px-4 text-sm text-gray-900">198.7h</td>
                  <td className="py-3 px-4">
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Good</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default QCDashboard;
