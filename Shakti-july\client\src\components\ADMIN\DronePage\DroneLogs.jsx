import React, { useEffect, useState, useMemo } from 'react';
import {
  ArrowLeft,
  Activity,
  Battery,
  Thermometer,
  Gauge,
  Navigation,
  Wifi,
  AlertTriangle,
  CheckCircle,
  Clock,
  MapPin,
  Plane,
  Settings,
  Download,
  RefreshCw,
  Maximize2,
  BarChart3,
  TrendingUp,
  Signal,
  Zap,
  Wind,
  Eye,
  Calendar,
  User,
  Building2
} from 'lucide-react';
import {
  FaPlane,
  FaRocket,
  FaChartLine,
  FaBatteryFull,
  FaThermometerHalf,
  FaCompass,
  FaWifi,
  FaMapMarkerAlt,
  FaUser,
  FaBuilding,
  FaCalendarAlt,
  FaDownload,
  FaExpand,
  FaSync
} from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { MapContainer, <PERSON>ile<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import AdminSidebar from '../common/AdminSidebar';

// Custom CSS for professional scrollbar and animations
const customStyles = `
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideIn {
    from {
      transform: translateX(-10px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  /* Custom Scrollbar Styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 10px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #3b82f6, #8b5cf6);
    border-radius: 10px;
    transition: all 0.3s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #2563eb, #7c3aed);
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  }

  /* Professional hover effects */
  .log-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .log-item:hover {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  /* Smooth scroll behavior */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Status indicator animations */
  .status-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }

  /* Timeline connector animation */
  .timeline-connector {
    animation: slideIn 0.8s ease-out forwards;
  }
`;

// Inject custom styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = customStyles;
  document.head.appendChild(styleSheet);
}

const DroneLogs = () => {
  const navigate = useNavigate();

  // Enhanced state for scroll management
  const [scrollPosition, setScrollPosition] = useState(0);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);

  // Enhanced drone information
  const [droneInfo] = useState({
    id: 'PRYMAA951753',
    name: 'ARJUNA MK-II',
    model: 'Agricultural Sprayer Pro',
    organization: 'SkyDrone Technologies',
    pilot: 'Suraj Shinde',
    location: 'Maharashtra, India',
    deploymentDate: '2025-04-25',
    registrationDate: '2025-04-24',
    serialNumber: 'SDT-2024-001',
    flightController: 'FC12345678',
    remoteController: 'RC9988234'
  });

  // Enhanced real-time sensor data
  const [sensorData, setSensorData] = useState({
    temperature: 25,
    altitude: 300,
    pressure: 1013,
    battery: 78,
    batteryVoltage: 22.4,
    roll: 12.5,
    yaw: -3.2,
    pitch: 87.1,
    speed: 15.2,
    heading: 245,
    satellites: 12,
    signalStrength: 85,
    flightTime: 1245,
    distance: 2.8,
    maxAltitude: 350,
    homeDistance: 150
  });

  // Flight history and logs
  const [flightLogs] = useState([
    {
      id: 1,
      timestamp: '2025-04-25 14:30:15',
      event: 'Flight Started',
      status: 'success',
      details: 'Takeoff successful from base station'
    },
    {
      id: 2,
      timestamp: '2025-04-25 14:32:45',
      event: 'Altitude Reached',
      status: 'info',
      details: 'Target altitude of 300m achieved'
    },
    {
      id: 3,
      timestamp: '2025-04-25 14:35:12',
      event: 'Spraying Started',
      status: 'success',
      details: 'Agricultural spraying operation commenced'
    },
    {
      id: 4,
      timestamp: '2025-04-25 14:38:30',
      event: 'Battery Warning',
      status: 'warning',
      details: 'Battery level dropped to 80%'
    },
    {
      id: 5,
      timestamp: '2025-04-25 14:42:18',
      event: 'Route Adjustment',
      status: 'info',
      details: 'Flight path adjusted due to wind conditions'
    }
  ]);

  // Performance metrics
  const [performanceMetrics] = useState({
    totalFlightTime: '245h 30m',
    totalDistance: '1,250 km',
    averageSpeed: '18.5 km/h',
    maxSpeed: '45 km/h',
    totalMissions: 156,
    successRate: 98.7,
    lastMaintenance: '2025-04-20',
    nextMaintenance: '2025-05-20'
  });

  // Enhanced real-time updates with more realistic data
  useEffect(() => {
    const interval = setInterval(() => {
      setSensorData(prev => ({
        ...prev,
        temperature: 24 + Math.random() * 4,
        altitude: 290 + Math.random() * 30,
        pressure: 1010 + Math.random() * 6,
        battery: Math.max(70, prev.battery - Math.random() * 0.1),
        batteryVoltage: 20 + (prev.battery / 100) * 4.8,
        roll: (Math.random() - 0.5) * 30,
        yaw: (Math.random() - 0.5) * 20,
        pitch: 85 + (Math.random() - 0.5) * 10,
        speed: 12 + Math.random() * 8,
        heading: (prev.heading + (Math.random() - 0.5) * 5) % 360,
        signalStrength: 80 + Math.random() * 15,
        flightTime: prev.flightTime + 1,
        distance: prev.distance + (prev.speed / 3600),
        homeDistance: 100 + Math.random() * 100
      }));
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  // Utility functions
  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      case 'info': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getBatteryColor = (level) => {
    if (level > 70) return 'text-green-600';
    if (level > 30) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSignalStrength = (strength) => {
    if (strength > 80) return { color: 'text-green-600', label: 'Excellent' };
    if (strength > 60) return { color: 'text-yellow-600', label: 'Good' };
    if (strength > 40) return { color: 'text-orange-600', label: 'Fair' };
    return { color: 'text-red-600', label: 'Poor' };
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Enhanced drone location with real-time tracking
  const droneIcon = new L.Icon({
    iconUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiMzQjgyRjYiLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIxIDEyQzIxIDEyIDIwIDEyIDIwIDEyQzIwIDEyIDIwIDEyIDIwIDEyWiIgZmlsbD0id2hpdGUiLz4KPHN2ZyB4PSI0IiB5PSI0IiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+Cjwvc3ZnPgo=',
    iconSize: [32, 32],
    iconAnchor: [16, 16],
    popupAnchor: [0, -16],
  });

  const droneLocations = [
    {
      id: droneInfo.id,
      lat: 19.0760 + (Math.random() - 0.5) * 0.01,
      lng: 72.8777 + (Math.random() - 0.5) * 0.01,
      name: droneInfo.name,
      altitude: sensorData.altitude,
      speed: sensorData.speed,
      heading: sensorData.heading
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />
      <div className="ml-[250px]">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4 text-black">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/dronepage')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <FaPlane className="text-blue-600" />
                  {droneInfo.name} - Live Monitoring
                </h2>
                <p className="text-gray-600 mt-1">
                  Real-time telemetry and flight data for {droneInfo.id}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 px-3 py-2 bg-green-100 text-green-800 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Live</span>
              </div>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <Download className="w-4 h-4" />
                Export Data
              </button>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <Maximize2 className="w-4 h-4" />
                Full Screen
              </button>
            </div>
          </div>
        </div>

        {/* Drone Status Overview */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6 text-black">
            {/* Drone Info Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                  <FaPlane className="w-8 h-8 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{droneInfo.name}</h3>
                  <p className="text-sm text-gray-500">{droneInfo.id}</p>
                  <p className="text-xs text-gray-400">{droneInfo.model}</p>
                </div>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Building2 className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">{droneInfo.organization}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">{droneInfo.pilot}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">{droneInfo.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-600">Deployed: {new Date(droneInfo.deploymentDate).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            {/* Flight Status */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Flight Status</h3>
                <div className="flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                  <CheckCircle className="w-4 h-4" />
                  Active
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Flight Time</span>
                  <span className="text-sm font-medium text-gray-900">{formatTime(sensorData.flightTime)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Distance</span>
                  <span className="text-sm font-medium text-gray-900">{sensorData.distance.toFixed(1)} km</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Max Altitude</span>
                  <span className="text-sm font-medium text-gray-900">{sensorData.maxAltitude}m</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Home Distance</span>
                  <span className="text-sm font-medium text-gray-900">{sensorData.homeDistance.toFixed(0)}m</span>
                </div>
              </div>
            </div>

            {/* Battery & Power */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Power Status</h3>
                <Battery className={`w-5 h-5 ${getBatteryColor(sensorData.battery)}`} />
              </div>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">Battery Level</span>
                    <span className={`text-sm font-medium ${getBatteryColor(sensorData.battery)}`}>
                      {sensorData.battery.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        sensorData.battery > 70 ? 'bg-green-500' :
                        sensorData.battery > 30 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${sensorData.battery}%` }}
                    ></div>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Voltage</span>
                  <span className="text-sm font-medium text-gray-900">{sensorData.batteryVoltage.toFixed(1)}V</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Estimated Time</span>
                  <span className="text-sm font-medium text-gray-900">
                    {Math.floor((sensorData.battery / 100) * 45)}min
                  </span>
                </div>
              </div>
            </div>

            {/* Connectivity */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Connectivity</h3>
                <Signal className={`w-5 h-5 ${getSignalStrength(sensorData.signalStrength).color}`} />
              </div>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">Signal Strength</span>
                    <span className={`text-sm font-medium ${getSignalStrength(sensorData.signalStrength).color}`}>
                      {getSignalStrength(sensorData.signalStrength).label}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        sensorData.signalStrength > 80 ? 'bg-green-500' :
                        sensorData.signalStrength > 60 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${sensorData.signalStrength}%` }}
                    ></div>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">GPS Satellites</span>
                  <span className="text-sm font-medium text-gray-900">{sensorData.satellites}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Controller</span>
                  <span className="text-sm font-medium text-green-600">Connected</span>
                </div>
              </div>
            </div>
          </div>

          {/* Map and Real-time Sensors */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-6 text-black">
            {/* Live Map */}
            <div className="xl:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Live Flight Path</h3>
                <div className="flex items-center gap-2">
                  <button className="flex items-center gap-2 px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <Maximize2 className="w-4 h-4" />
                    Full Screen
                  </button>
                  <button className="flex items-center gap-2 px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <RefreshCw className="w-4 h-4" />
                    Refresh
                  </button>
                </div>
              </div>
              <div className="relative">
                <MapContainer
                  center={[19.0760, 72.8777]}
                  zoom={12}
                  scrollWheelZoom={true}
                  style={{ width: '100%', height: '400px', borderRadius: '0.75rem' }}
                >
                  <TileLayer
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                    attribution='&copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a>'
                  />
                  {droneLocations.map(drone => (
                    <Marker
                      key={drone.id}
                      position={[drone.lat, drone.lng]}
                      icon={droneIcon}
                    >
                      <Popup>
                        <div className="p-2">
                          <h4 className="font-semibold">{drone.name}</h4>
                          <p className="text-sm text-gray-600">Altitude: {drone.altitude}m</p>
                          <p className="text-sm text-gray-600">Speed: {drone.speed.toFixed(1)} km/h</p>
                          <p className="text-sm text-gray-600">Heading: {drone.heading.toFixed(0)}°</p>
                        </div>
                      </Popup>
                    </Marker>
                  ))}
                </MapContainer>
                <div className="absolute top-4 left-4 bg-white rounded-lg shadow-md p-3 text-sm">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="font-medium">Current Position</span>
                  </div>
                  <div className="space-y-1 text-xs text-gray-600">
                    <p>Lat: {droneLocations[0]?.lat.toFixed(6)}</p>
                    <p>Lng: {droneLocations[0]?.lng.toFixed(6)}</p>
                    <p>Alt: {sensorData.altitude.toFixed(0)}m</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Real-time Sensor Data */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Live Telemetry</h3>
                <div className="flex items-center gap-2 px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Real-time</span>
                </div>
              </div>

              <div className="space-y-4">
                {/* Environmental Sensors */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Environmental</h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Thermometer className="w-4 h-4 text-orange-500" />
                        <span className="text-xs text-gray-600">Temperature</span>
                      </div>
                      <p className="text-lg font-semibold text-gray-900">{sensorData.temperature.toFixed(1)}°C</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Gauge className="w-4 h-4 text-blue-500" />
                        <span className="text-xs text-gray-600">Pressure</span>
                      </div>
                      <p className="text-lg font-semibold text-gray-900">{sensorData.pressure.toFixed(0)} hPa</p>
                    </div>
                  </div>
                </div>

                {/* Flight Data */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Flight Data</h4>
                  <div className="space-y-3">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Activity className="w-4 h-4 text-green-500" />
                        <span className="text-xs text-gray-600">Altitude</span>
                      </div>
                      <p className="text-lg font-semibold text-gray-900">{sensorData.altitude.toFixed(0)}m</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Navigation className="w-4 h-4 text-purple-500" />
                        <span className="text-xs text-gray-600">Speed</span>
                      </div>
                      <p className="text-lg font-semibold text-gray-900">{sensorData.speed.toFixed(1)} km/h</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Navigation className="w-4 h-4 text-indigo-500" />
                        <span className="text-xs text-gray-600">Heading</span>
                      </div>
                      <p className="text-lg font-semibold text-gray-900">{sensorData.heading.toFixed(0)}°</p>
                    </div>
                  </div>
                </div>

                {/* Orientation */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Orientation</h4>
                  <div className="grid grid-cols-3 gap-2">
                    <div className="bg-gray-50 rounded-lg p-2 text-center">
                      <p className="text-xs text-gray-600 mb-1">Roll</p>
                      <p className="text-sm font-semibold text-gray-900">{sensorData.roll.toFixed(1)}°</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-2 text-center">
                      <p className="text-xs text-gray-600 mb-1">Pitch</p>
                      <p className="text-sm font-semibold text-gray-900">{sensorData.pitch.toFixed(1)}°</p>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-2 text-center">
                      <p className="text-xs text-gray-600 mb-1">Yaw</p>
                      <p className="text-sm font-semibold text-gray-900">{sensorData.yaw.toFixed(1)}°</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Flight Logs and Performance */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-6 text-black">
            {/* Flight Logs */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Flight Logs</h3>
                <button className="flex items-center gap-2 px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  <Download className="w-4 h-4" />
                  Export
                </button>
              </div>
              <div className="relative group">
                {/* Professional Scrollable Container with Enhanced Features */}
                <div
                  className="space-y-3 max-h-80 overflow-y-auto pr-3 scrollbar-thin smooth-scroll"
                  onScroll={(e) => {
                    const { scrollTop, scrollHeight, clientHeight } = e.target;
                    const scrollPercent = (scrollTop / (scrollHeight - clientHeight)) * 100;
                    setScrollPosition(scrollPercent);
                    setIsScrolledToBottom(scrollTop + clientHeight >= scrollHeight - 5);
                    setShowScrollToBottom(scrollTop + clientHeight < scrollHeight - 50);
                  }}
                  id="flight-logs-container"
                >
                  {flightLogs.map((log, index) => (
                    <div
                      key={log.id}
                      className="log-item flex items-start gap-4 p-4 bg-gradient-to-r from-white via-gray-50 to-white rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-lg group/item"
                      style={{
                        animationDelay: `${index * 80}ms`,
                        animation: 'fadeInUp 0.5s ease-out forwards',
                        opacity: 0
                      }}
                    >
                      {/* Enhanced Timeline with Professional Design */}
                      <div className="flex flex-col items-center relative">
                        {/* Status Indicator with Glow Effect */}
                        <div className={`relative w-4 h-4 rounded-full shadow-lg transition-all duration-300 group-hover/item:scale-110 ${
                          log.status === 'success' ? 'bg-gradient-to-br from-green-400 to-green-600 shadow-green-200' :
                          log.status === 'warning' ? 'bg-gradient-to-br from-yellow-400 to-yellow-600 shadow-yellow-200' :
                          log.status === 'error' ? 'bg-gradient-to-br from-red-400 to-red-600 shadow-red-200' :
                          'bg-gradient-to-br from-blue-400 to-blue-600 shadow-blue-200'
                        }`}>
                          {/* Inner glow */}
                          <div className={`absolute inset-0.5 rounded-full ${
                            log.status === 'success' ? 'bg-green-300' :
                            log.status === 'warning' ? 'bg-yellow-300' :
                            log.status === 'error' ? 'bg-red-300' :
                            'bg-blue-300'
                          } opacity-60 animate-pulse`}></div>

                          {/* Status Icon */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            {log.status === 'success' && <CheckCircle className="w-2 h-2 text-white" />}
                            {log.status === 'warning' && <AlertTriangle className="w-2 h-2 text-white" />}
                            {log.status === 'error' && <AlertTriangle className="w-2 h-2 text-white" />}
                            {log.status === 'info' && <Activity className="w-2 h-2 text-white" />}
                          </div>
                        </div>

                        {/* Timeline Connector */}
                        {index < flightLogs.length - 1 && (
                          <div className="timeline-connector w-0.5 h-12 bg-gradient-to-b from-gray-300 via-gray-200 to-transparent mt-2 relative">
                            <div className="absolute inset-0 bg-gradient-to-b from-blue-200 to-transparent opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"></div>
                          </div>
                        )}
                      </div>

                      {/* Enhanced Content with Professional Typography */}
                      <div className="flex-1 min-w-0 space-y-2">
                        {/* Header with Enhanced Styling */}
                        <div className="flex items-start justify-between gap-3">
                          <h4 className="text-sm font-bold text-gray-900 leading-tight group-hover/item:text-blue-900 transition-colors duration-200">
                            {log.event}
                          </h4>
                          <div className="flex items-center gap-2 flex-shrink-0">
                            {/* Enhanced Status Badge */}
                            <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-semibold shadow-sm transition-all duration-200 ${
                              log.status === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
                              log.status === 'warning' ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' :
                              log.status === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
                              'bg-blue-100 text-blue-800 border border-blue-200'
                            }`}>
                              <div className={`w-1.5 h-1.5 rounded-full ${
                                log.status === 'success' ? 'bg-green-500' :
                                log.status === 'warning' ? 'bg-yellow-500' :
                                log.status === 'error' ? 'bg-red-500' :
                                'bg-blue-500'
                              }`}></div>
                              {log.status.toUpperCase()}
                            </span>

                            {/* Enhanced Timestamp */}
                            <div className="flex items-center gap-1 px-2 py-1 bg-gray-100 rounded-lg">
                              <Clock className="w-3 h-3 text-gray-500" />
                              <span className="text-xs text-gray-600 font-mono">
                                {new Date(log.timestamp).toLocaleTimeString('en-US', {
                                  hour12: false,
                                  hour: '2-digit',
                                  minute: '2-digit',
                                  second: '2-digit'
                                })}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Enhanced Description */}
                        <p className="text-sm text-gray-700 leading-relaxed pl-1 border-l-2 border-gray-200 group-hover/item:border-blue-300 transition-colors duration-200">
                          {log.details}
                        </p>

                        {/* Progress Bar for Active Events */}
                        {(log.status === 'success' || log.status === 'warning') && (
                          <div className="mt-3 w-full bg-gray-200 rounded-full h-1.5 overflow-hidden">
                            <div className={`h-full rounded-full transition-all duration-2000 ease-out ${
                              log.status === 'success' ? 'bg-gradient-to-r from-green-400 to-green-600' : 'bg-gradient-to-r from-yellow-400 to-yellow-600'
                            }`}
                            style={{
                              width: '100%',
                              animation: 'slideIn 2s ease-out'
                            }}></div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}

                  {/* Loading indicator for new logs */}
                  <div className="flex items-center justify-center py-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
                      <span>Monitoring for new events...</span>
                    </div>
                  </div>
                </div>

                {/* Enhanced Scroll Indicator */}
                <div className="absolute top-0 right-0 w-2 h-full bg-gray-100 rounded-full overflow-hidden opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div
                    className="w-full bg-gradient-to-b from-blue-500 via-purple-500 to-blue-600 rounded-full transition-all duration-300 shadow-sm"
                    style={{
                      height: `${Math.max(10, (flightLogs.length / 10) * 100)}%`,
                      transform: `translateY(${scrollPosition}%)`
                    }}
                  ></div>
                </div>

                {/* Scroll to Bottom Button */}
                {showScrollToBottom && (
                  <button
                    onClick={() => {
                      const container = document.getElementById('flight-logs-container');
                      container?.scrollTo({ top: container.scrollHeight, behavior: 'smooth' });
                    }}
                    className="absolute bottom-4 right-4 p-2 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200 hover:scale-110 z-10"
                    title="Scroll to bottom"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                    </svg>
                  </button>
                )}

                {/* Professional Fade Gradients */}
                <div className="absolute top-0 left-0 right-2 h-6 bg-gradient-to-b from-white via-white/80 to-transparent pointer-events-none z-10"></div>
                <div className="absolute bottom-0 left-0 right-2 h-6 bg-gradient-to-t from-white via-white/80 to-transparent pointer-events-none z-10"></div>

                {/* Hover Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 via-blue-50/20 to-blue-50/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-xl"></div>
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Performance Metrics</h3>
                <div className="flex items-center gap-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                  <BarChart3 className="w-3 h-3" />
                  <span>Analytics</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Total Flight Time</span>
                  </div>
                  <p className="text-xl font-bold text-blue-900">{performanceMetrics.totalFlightTime}</p>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Navigation className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-green-900">Total Distance</span>
                  </div>
                  <p className="text-xl font-bold text-green-900">{performanceMetrics.totalDistance}</p>
                </div>
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="w-4 h-4 text-purple-600" />
                    <span className="text-sm font-medium text-purple-900">Success Rate</span>
                  </div>
                  <p className="text-xl font-bold text-purple-900">{performanceMetrics.successRate}%</p>
                </div>
                <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Settings className="w-4 h-4 text-orange-600" />
                    <span className="text-sm font-medium text-orange-900">Missions</span>
                  </div>
                  <p className="text-xl font-bold text-orange-900">{performanceMetrics.totalMissions}</p>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">Last Maintenance:</span>
                  <span className="font-medium text-gray-900">{new Date(performanceMetrics.lastMaintenance).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between items-center text-sm mt-2">
                  <span className="text-gray-600">Next Maintenance:</span>
                  <span className="font-medium text-orange-600">{new Date(performanceMetrics.nextMaintenance).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Technical Specifications */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 text-black">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Technical Specifications</h3>
              <div className="flex items-center gap-2 px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                <Settings className="w-4 h-4" />
                <span>Hardware Details</span>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Basic Specifications</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max Takeoff Weight:</span>
                    <span className="font-medium">25 kg</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Payload Capacity:</span>
                    <span className="font-medium">10 kg</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Flight Time:</span>
                    <span className="font-medium">45 min</span>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Control Systems</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Flight Controller:</span>
                    <span className="font-medium">{droneInfo.flightController}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Remote Controller:</span>
                    <span className="font-medium">{droneInfo.remoteController}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">GPS Accuracy:</span>
                    <span className="font-medium">±1m</span>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Propulsion</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Motors:</span>
                    <span className="font-medium">4x Brushless</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Propellers:</span>
                    <span className="font-medium">Carbon Fiber</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max Speed:</span>
                    <span className="font-medium">65 km/h</span>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Spray System</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">System Type:</span>
                    <span className="font-medium">Hydraulic</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Nozzles:</span>
                    <span className="font-medium">4 Units</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Flow Rate:</span>
                    <span className="font-medium">2.4 L/min</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};



export default DroneLogs;
