import { useState, useMemo } from 'react';
import {
  Eye,
  Edit2,
  Trash2,
  Play,
  Pause,
  RotateCw,
  MapPin,
  Thermometer,
  Battery,
  Gauge,
  ChevronUp,
  ChevronDown,
  MoreVertical,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';

// React Icons
import {
  FaPlane,
  FaBatteryFull,
  FaBatteryThreeQuarters,
  FaBatteryHalf,
  FaBatteryQuarter,
  FaBatteryEmpty,
  FaTemperatureHigh,
  FaMapMarkerAlt,
  FaRocket,
  FaCog,
  FaExclamationTriangle,
  FaCheckCircle,
  FaTimesCircle,
  FaPlay,
  FaPause,
  FaStop,
  FaEye,
  FaEdit,
  FaTrash,
  FaWifi,
  FaSignal,
  FaCloud,
  FaCloudRain,
  FaTimes
} from 'react-icons/fa';

const DroneTable = ({
  searchTerm = '',
  statusFilter = 'All',
  sortBy = 'name',
  sortOrder = 'asc',
  onSort
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedDrone, setSelectedDrone] = useState(null);
  const [deployedDrones, setDeployedDrones] = useState([]);
  const [availableDrones, setAvailableDrones] = useState([
    {
      shaktiId: 'S001',
      droneId: 'D001',
      name: 'Arjuna',
      addedOn: '2025-07-01',
      status: 'Active',
      model: 'DJI Phantom 4',
      category: 'Agriculture',
      lastMaintenance: '2025-06-15',
      flightHours: 245,
      maxFlightTime: 28,
      sensor: {
        lat: 19.1234,
        long: 72.1234,
        alt: 200,
        temp: 45,
        battery: 85,
        signal: 95,
        gps: 'Strong',
        weather: 'Clear'
      },
    },
    {
      shaktiId: 'S002',
      droneId: 'D002',
      name: 'Vikrant',
      addedOn: '2025-06-15',
      status: 'Maintenance',
      model: 'DJI Mavic Pro',
      category: 'Surveillance',
      lastMaintenance: '2025-07-10',
      flightHours: 189,
      maxFlightTime: 25,
      sensor: {
        lat: 21.0000,
        long: 78.0000,
        alt: 180,
        temp: 40,
        battery: 65,
        signal: 78,
        gps: 'Moderate',
        weather: 'Cloudy'
      },
    },
    {
      shaktiId: 'S003',
      droneId: 'D003',
      name: 'Bhima',
      addedOn: '2025-05-20',
      status: 'Deployed',
      model: 'DJI Air 2S',
      category: 'Mapping',
      lastMaintenance: '2025-06-01',
      flightHours: 312,
      maxFlightTime: 31,
      sensor: {
        lat: 18.5204,
        long: 73.8567,
        alt: 250,
        temp: 38,
        battery: 92,
        signal: 88,
        gps: 'Strong',
        weather: 'Clear'
      },
    },
    {
      shaktiId: 'S004',
      droneId: 'D004',
      name: 'Nakula',
      addedOn: '2025-04-10',
      status: 'Active',
      model: 'DJI Mini 3',
      category: 'Inspection',
      lastMaintenance: '2025-05-15',
      flightHours: 156,
      maxFlightTime: 34,
      sensor: {
        lat: 22.5726,
        long: 88.3639,
        alt: 195,
        temp: 42,
        battery: 78,
        signal: 82,
        gps: 'Strong',
        weather: 'Partly Cloudy'
      },
    },
    {
      shaktiId: 'S005',
      droneId: 'D005',
      name: 'Sahadeva',
      addedOn: '2025-03-25',
      status: 'Inactive',
      model: 'DJI Phantom 4 Pro',
      category: 'Agriculture',
      lastMaintenance: '2025-07-05',
      flightHours: 89,
      maxFlightTime: 30,
      sensor: {
        lat: 12.9716,
        long: 77.5946,
        alt: 165,
        temp: 35,
        battery: 45,
        signal: 65,
        gps: 'Weak',
        weather: 'Rainy'
      },
    },
    {
      shaktiId: 'S006',
      droneId: 'D006',
      name: 'Karna',
      addedOn: '2025-02-14',
      status: 'Active',
      model: 'DJI Matrice 300',
      category: 'Heavy Lift',
      lastMaintenance: '2025-06-20',
      flightHours: 278,
      maxFlightTime: 55,
      sensor: {
        lat: 26.9124,
        long: 75.7873,
        alt: 220,
        temp: 48,
        battery: 88,
        signal: 91,
        gps: 'Strong',
        weather: 'Hot'
      },
    }
  ]);

  // Filtering and sorting logic
  const filteredAndSortedDrones = useMemo(() => {
    let filtered = availableDrones.filter(drone => {
      const matchesSearch = drone.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          drone.droneId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          drone.shaktiId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          drone.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          drone.category.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'All' || drone.status === statusFilter;

      return matchesSearch && matchesStatus;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'status':
          aValue = a.status.toLowerCase();
          bValue = b.status.toLowerCase();
          break;
        case 'battery':
          aValue = a.sensor.battery;
          bValue = b.sensor.battery;
          break;
        case 'flightHours':
          aValue = a.flightHours;
          bValue = b.flightHours;
          break;
        case 'addedOn':
          aValue = new Date(a.addedOn);
          bValue = new Date(b.addedOn);
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [availableDrones, searchTerm, statusFilter, sortBy, sortOrder]);

  const rowsPerPage = 10;
  const indexOfLast = currentPage * rowsPerPage;
  const indexOfFirst = indexOfLast - rowsPerPage;
  const currentDrones = filteredAndSortedDrones.slice(indexOfFirst, indexOfLast);
  const totalPages = Math.ceil(filteredAndSortedDrones.length / rowsPerPage);

  const handleDeploy = (droneId) => {
    const drone = availableDrones.find(d => d.droneId === droneId);
    if (drone) {
      const deployedDrone = { ...drone, status: 'Deployed' };
      setDeployedDrones([...deployedDrones, deployedDrone]);
      setAvailableDrones(availableDrones.filter(d => d.droneId !== droneId));
    }
  };

  const getBatteryIcon = (battery) => {
    if (battery >= 80) return <FaBatteryFull className="text-green-600" />;
    if (battery >= 60) return <FaBatteryThreeQuarters className="text-yellow-600" />;
    if (battery >= 40) return <FaBatteryHalf className="text-orange-600" />;
    if (battery >= 20) return <FaBatteryQuarter className="text-red-600" />;
    return <FaBatteryEmpty className="text-red-700" />;
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Active': return <FaCheckCircle className="text-green-600" />;
      case 'Deployed': return <FaRocket className="text-blue-600" />;
      case 'Maintenance': return <FaCog className="text-orange-600" />;
      case 'Inactive': return <FaTimesCircle className="text-gray-500" />;
      default: return <FaExclamationTriangle className="text-yellow-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Deployed': return 'bg-blue-100 text-blue-800';
      case 'Maintenance': return 'bg-orange-100 text-orange-800';
      case 'Inactive': return 'bg-gray-100 text-gray-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <FaPlane className="text-blue-600" />
            Drone Fleet ({filteredAndSortedDrones.length})
          </h2>
          <div className="text-sm text-gray-500">
            Showing {indexOfFirst + 1} to {Math.min(indexOfLast, filteredAndSortedDrones.length)} of {filteredAndSortedDrones.length} results
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => onSort && onSort('name')}
              >
                <div className="flex items-center gap-1">
                  Drone Details
                  {sortBy === 'name' && (sortOrder === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />)}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Model & Category
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => onSort && onSort('status')}
              >
                <div className="flex items-center gap-1">
                  Status
                  {sortBy === 'status' && (sortOrder === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />)}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Location & Sensors
              </th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentDrones.map((drone) => (
              <tr key={drone.droneId} className="hover:bg-gray-50 transition-colors">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-12 w-12">
                      <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                        <FaPlane className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{drone.name}</div>
                      <div className="text-sm text-gray-500">ID: {drone.droneId}</div>
                      <div className="text-xs text-gray-400">Shakti: {drone.shaktiId}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 font-medium">{drone.model}</div>
                  <div className="text-sm text-gray-500">{drone.category}</div>
                  <div className="text-xs text-gray-400">Added: {drone.addedOn}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(drone.status)}
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(drone.status)}`}>
                      {drone.status}
                    </span>
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    Last maintenance: {drone.lastMaintenance}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="space-y-1">
                    <div className="flex items-center gap-1 text-sm">
                      <MapPin className="w-3 h-3 text-gray-400" />
                      <span className="text-gray-900">{drone.sensor.lat.toFixed(4)}, {drone.sensor.long.toFixed(4)}</span>
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      <Gauge className="w-3 h-3 text-gray-400" />
                      <span className="text-gray-600">{drone.sensor.alt}m altitude</span>
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      <Thermometer className="w-3 h-3 text-gray-400" />
                      <span className="text-gray-600">{drone.sensor.temp}°C</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs">
                      <FaSignal className="text-gray-400" />
                      <span className="text-gray-500">Signal: {drone.sensor.signal}%</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs">
                      {getBatteryIcon(drone.sensor.battery)}
                      <span className="text-gray-500">Battery: {drone.sensor.battery}%</span>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center justify-center gap-2">
                    <button
                      onClick={() => setSelectedDrone(drone)}
                      className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md border border-blue-300 transition-colors"
                      title="View Details"
                    >
                      <Eye size={18} className="w-4 h-4" />
                    </button>
                    <button
                      className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-md border border-gray-300 transition-colors"
                      title="Edit"
                    >
                      <Edit2 size={18} className="w-4 h-4" />
                    </button>
                    {drone.status === 'Active' && (
                      <button
                        onClick={() => handleDeploy(drone.droneId)}
                        className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-md border border-green-300 transition-colors"
                        title="Deploy"
                      >
                        <FaRocket size={16} className="w-4 h-4" />
                      </button>
                    )}
                    <button
                      className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md border border-red-300 transition-colors"
                      title="Delete"
                    >
                      <FaTrash size={16} className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Drone Detail Modal */}
      {selectedDrone && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" onClick={() => setSelectedDrone(null)}>
          <div
            className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-100"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="sticky top-0 bg-white p-6 border-b border-gray-200 rounded-t-xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <FaPlane className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">{selectedDrone.name}</h2>
                    <p className="text-sm text-gray-500">Drone Details & Analytics</p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedDrone(null)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200 group"
                  title="Close"
                >
                  <FaTimes size={20} className="text-gray-500 group-hover:text-gray-700" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 text-black">
              {/* Status Badge */}
              <div className="mb-6">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(selectedDrone.status)}`}>
                  {selectedDrone.status}
                </span>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Basic Information */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <FaPlane className="w-5 h-5 text-blue-600" />
                      Basic Information
                    </h3>
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Drone Name</label>
                        <p className="text-gray-900 font-medium">{selectedDrone.name}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Model</label>
                        <p className="text-gray-900 font-medium">{selectedDrone.model}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Category</label>
                        <p className="text-gray-900 font-medium">{selectedDrone.category}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <label className="text-sm font-medium text-gray-500 block mb-1">Added On</label>
                        <p className="text-gray-900 font-medium">{selectedDrone.addedOn}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Sensor Data */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Gauge className="w-5 h-5 text-green-600" />
                      Sensor Data
                    </h3>
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center gap-3">
                          <MapPin className="w-5 h-5 text-blue-500" />
                          <div>
                            <label className="text-sm font-medium text-gray-500 block">Location</label>
                            <p className="text-gray-900 font-medium">{selectedDrone.sensor.lat.toFixed(6)}, {selectedDrone.sensor.long.toFixed(6)}</p>
                          </div>
                        </div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Gauge className="w-5 h-5 text-purple-500" />
                          <div>
                            <label className="text-sm font-medium text-gray-500 block">Altitude</label>
                            <p className="text-gray-900 font-medium">{selectedDrone.sensor.alt} meters</p>
                          </div>
                        </div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Thermometer className="w-5 h-5 text-red-500" />
                          <div>
                            <label className="text-sm font-medium text-gray-500 block">Temperature</label>
                            <p className="text-gray-900 font-medium">{selectedDrone.sensor.temp}°C</p>
                          </div>
                        </div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center gap-3">
                          {getBatteryIcon(selectedDrone.sensor.battery)}
                          <div>
                            <label className="text-sm font-medium text-gray-500 block">Battery Level</label>
                            <p className="text-gray-900 font-medium">{selectedDrone.sensor.battery}%</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                  <Clock className="w-5 h-5 text-purple-600" />
                  Performance Metrics
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200">
                    <div className="flex items-center justify-between mb-2">
                      <Clock className="w-8 h-8 text-blue-600" />
                      <div className="text-right">
                        <div className="text-3xl font-bold text-blue-600">{selectedDrone.flightHours}</div>
                        <div className="text-sm font-medium text-blue-600">Flight Hours</div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl border border-green-200">
                    <div className="flex items-center justify-between mb-2">
                      <Battery className="w-8 h-8 text-green-600" />
                      <div className="text-right">
                        <div className="text-3xl font-bold text-green-600">{selectedDrone.maxFlightTime}</div>
                        <div className="text-sm font-medium text-green-600">Max Flight Time (min)</div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200">
                    <div className="flex items-center justify-between mb-2">
                      <FaSignal className="w-8 h-8 text-purple-600" />
                      <div className="text-right">
                        <div className="text-3xl font-bold text-purple-600">{selectedDrone.sensor.signal}</div>
                        <div className="text-sm font-medium text-purple-600">Signal Strength (%)</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="flex flex-wrap gap-3 justify-end">
                  <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <Edit2 size={16} />
                    Edit Drone
                  </button>
                  {selectedDrone.status === 'Active' && (
                    <button
                      onClick={() => {
                        handleDeploy(selectedDrone.droneId);
                        setSelectedDrone(null);
                      }}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <FaRocket size={16} />
                      Deploy Drone
                    </button>
                  )}
                  <button
                    onClick={() => setSelectedDrone(null)}
                    className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FaTimes size={16} />
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DroneTable;
