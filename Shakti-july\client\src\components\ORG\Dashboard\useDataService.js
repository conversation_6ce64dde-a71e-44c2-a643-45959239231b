import { useState, useEffect, useCallback } from 'react';
import dataService from './dataService';

// Custom hook for using the data service
export const useDataService = (dataType, autoUpdate = true) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let unsubscribe = null;

    const initializeData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Initialize the service if not already done
        await dataService.initialize();

        // Get initial data
        const initialData = dataService.getData(dataType);
        setData(initialData);

        // Subscribe to updates if autoUpdate is enabled
        if (autoUpdate) {
          unsubscribe = dataService.subscribe(`${dataType}Updated`, (updatedData) => {
            setData(updatedData);
          });

          // Also subscribe to general data loaded event
          const unsubscribeDataLoaded = dataService.subscribe('dataLoaded', (allData) => {
            setData(allData[dataType]);
          });

          // Return combined unsubscribe function
          const originalUnsubscribe = unsubscribe;
          unsubscribe = () => {
            originalUnsubscribe();
            unsubscribeDataLoaded();
          };
        }

        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    initializeData();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [dataType, autoUpdate]);

  const updateData = useCallback((newData) => {
    dataService.updateData(dataType, newData);
  }, [dataType]);

  const refreshData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      await dataService.initialize();
      const refreshedData = dataService.getData(dataType);
      setData(refreshedData);
      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  }, [dataType]);

  return {
    data,
    loading,
    error,
    updateData,
    refreshData
  };
};

// Hook for drone-specific operations
export const useDroneData = () => {
  const { data: drones, loading, error, refreshData } = useDataService('drones');

  const getDroneById = useCallback((droneId) => {
    return drones?.find(drone => drone.id === droneId) || null;
  }, [drones]);

  const getDronesByStatus = useCallback((status) => {
    return drones?.filter(drone => drone.status === status) || [];
  }, [drones]);

  const getActiveDrones = useCallback(() => {
    return drones?.filter(drone => drone.status === 'Active' || drone.status === 'Flying') || [];
  }, [drones]);

  const updateDroneStatus = useCallback(async (droneId, status) => {
    try {
      const success = await dataService.updateDroneStatus(droneId, status);
      if (success) {
        await refreshData();
      }
      return success;
    } catch (err) {
      console.error('Failed to update drone status:', err);
      return false;
    }
  }, [refreshData]);

  const getDroneStats = useCallback(() => {
    if (!drones) return null;

    return {
      total: drones.length,
      active: drones.filter(d => d.status === 'Active').length,
      flying: drones.filter(d => d.status === 'Flying').length,
      maintenance: drones.filter(d => d.status === 'Maintenance').length,
      crashed: drones.filter(d => d.status === 'Crashed').length,
      inactive: drones.filter(d => d.status === 'Inactive').length
    };
  }, [drones]);

  return {
    drones,
    loading,
    error,
    getDroneById,
    getDronesByStatus,
    getActiveDrones,
    updateDroneStatus,
    getDroneStats,
    refreshData
  };
};

// Hook for activity feed
export const useActivityFeed = (limit = 10) => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeActivities = async () => {
      try {
        await dataService.initialize();
        const initialActivities = dataService.getData('activities') || [];
        setActivities(initialActivities.slice(0, limit));
        setLoading(false);
      } catch (err) {
        console.error('Failed to load activities:', err);
        setLoading(false);
      }
    };

    initializeActivities();

    // Subscribe to new activities
    const unsubscribe = dataService.subscribe('activityAdded', (newActivity) => {
      setActivities(prev => [newActivity, ...prev.slice(0, limit - 1)]);
    });

    return unsubscribe;
  }, [limit]);

  const addActivity = useCallback((activity) => {
    const newActivity = {
      ...activity,
      id: Date.now(),
      timestamp: new Date()
    };
    
    setActivities(prev => [newActivity, ...prev.slice(0, limit - 1)]);
    
    // Also update the service
    const currentActivities = dataService.getData('activities') || [];
    dataService.updateData('activities', [newActivity, ...currentActivities]);
  }, [limit]);

  return {
    activities,
    loading,
    addActivity
  };
};

// Hook for performance metrics
export const usePerformanceMetrics = () => {
  const { data: performance, loading, error } = useDataService('performance');

  const getOverallScore = useCallback(() => {
    if (!performance) return 0;
    const { efficiency, uptime, accuracy, coverage } = performance;
    return Math.round((efficiency + uptime + accuracy + coverage) / 4);
  }, [performance]);

  const getMetricStatus = useCallback((metric, value) => {
    const thresholds = {
      efficiency: { good: 85, warning: 70 },
      uptime: { good: 90, warning: 80 },
      accuracy: { good: 95, warning: 85 },
      speed: { good: 80, warning: 60 },
      coverage: { good: 85, warning: 70 }
    };

    const threshold = thresholds[metric];
    if (!threshold) return 'unknown';

    if (value >= threshold.good) return 'good';
    if (value >= threshold.warning) return 'warning';
    return 'critical';
  }, []);

  return {
    performance,
    loading,
    error,
    getOverallScore,
    getMetricStatus
  };
};

// Hook for analytics data
export const useAnalytics = () => {
  const { data: analytics, loading, error } = useDataService('analytics');

  const getMonthlyTrend = useCallback((metric) => {
    if (!analytics?.monthlyData) return null;
    
    const data = analytics.monthlyData;
    const current = data[data.length - 1]?.[metric] || 0;
    const previous = data[data.length - 2]?.[metric] || 0;
    
    if (previous === 0) return 0;
    return ((current - previous) / previous * 100).toFixed(1);
  }, [analytics]);

  const getTotalMetric = useCallback((metric) => {
    if (!analytics?.monthlyData) return 0;
    return analytics.monthlyData.reduce((sum, item) => sum + (item[metric] || 0), 0);
  }, [analytics]);

  const getRegionalLeader = useCallback(() => {
    if (!analytics?.regionalData) return null;
    return analytics.regionalData.reduce((leader, region) => 
      region.value > leader.value ? region : leader
    );
  }, [analytics]);

  return {
    analytics,
    loading,
    error,
    getMonthlyTrend,
    getTotalMetric,
    getRegionalLeader
  };
};

// Hook for notifications
export const useNotifications = () => {
  const { data: notifications, loading, updateData } = useDataService('notifications');

  const unreadCount = useCallback(() => {
    return notifications?.filter(n => !n.read).length || 0;
  }, [notifications]);

  const markAsRead = useCallback((notificationId) => {
    if (!notifications) return;
    
    const updated = notifications.map(n => 
      n.id === notificationId ? { ...n, read: true } : n
    );
    updateData(updated);
  }, [notifications, updateData]);

  const markAllAsRead = useCallback(() => {
    if (!notifications) return;
    
    const updated = notifications.map(n => ({ ...n, read: true }));
    updateData(updated);
  }, [notifications, updateData]);

  const addNotification = useCallback((notification) => {
    if (!notifications) return;
    
    const newNotification = {
      ...notification,
      id: Date.now(),
      read: false
    };
    
    updateData([newNotification, ...notifications]);
  }, [notifications, updateData]);

  return {
    notifications,
    loading,
    unreadCount: unreadCount(),
    markAsRead,
    markAllAsRead,
    addNotification
  };
};

export default useDataService;
