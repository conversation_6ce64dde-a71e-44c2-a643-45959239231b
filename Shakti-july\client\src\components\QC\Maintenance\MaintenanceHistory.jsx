import React, { useState } from 'react';
import QCLayout from '../common/QCLayout';
import {
  History,
  Search,
  Filter,
  Calendar,
  Clock,
  User,
  Wrench,
  CheckCircle,
  AlertTriangle,
  DollarSign,
  FileText,
  Download,
  Eye,
  TrendingUp,
  TrendingDown,
  BarChart3,
  MapPin
} from 'lucide-react';

const MaintenanceHistory = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterDrone, setFilterDrone] = useState('all');
  const [dateRange, setDateRange] = useState('30days');

  // Sample maintenance history data
  const maintenanceHistory = [
    {
      id: 'MH-2024-001',
      workOrderId: 'WO-2024-001',
      title: 'Emergency Battery Replacement',
      droneId: 'DRN-001',
      droneName: 'Surveyor Alpha',
      type: 'Corrective',
      status: 'Completed',
      technician: '<PERSON>',
      startDate: '2024-01-15',
      completionDate: '2024-01-15',
      duration: '3.5 hours',
      cost: 820,
      location: 'Hangar A',
      description: 'Replaced faulty battery pack and performed capacity test. All systems operational.',
      partsUsed: ['Battery Pack LiPo 6S', 'Battery Connector', 'Thermal Pad'],
      issuesFound: ['Battery swelling', 'Reduced capacity', 'Connector corrosion'],
      resolution: 'Complete battery system replacement with upgraded components',
      nextMaintenanceDate: '2024-04-15'
    },
    {
      id: 'MH-2024-002',
      workOrderId: 'WO-2024-003',
      title: 'Camera System Calibration',
      droneId: 'DRN-005',
      droneName: 'Mapper Gamma',
      type: 'Corrective',
      status: 'Completed',
      technician: 'Sarah Johnson',
      startDate: '2024-01-12',
      completionDate: '2024-01-14',
      duration: '3.5 hours',
      cost: 180,
      location: 'Lab C',
      description: 'Recalibrated camera system after minor impact. Alignment and focus restored.',
      partsUsed: ['Calibration Target', 'Lens Cleaning Kit'],
      issuesFound: ['Camera misalignment', 'Focus drift', 'Lens contamination'],
      resolution: 'Full camera recalibration and lens cleaning performed',
      nextMaintenanceDate: '2024-07-12'
    },
    {
      id: 'MH-2024-003',
      workOrderId: 'WO-2023-045',
      title: 'Routine Propeller Inspection',
      droneId: 'DRN-003',
      droneName: 'Scout Beta',
      type: 'Preventive',
      status: 'Completed',
      technician: 'Mike Wilson',
      startDate: '2024-01-10',
      completionDate: '2024-01-10',
      duration: '1 hour',
      cost: 45,
      location: 'Workshop B',
      description: 'Routine inspection of all propellers. Minor wear detected on prop #3.',
      partsUsed: ['Balance Weights'],
      issuesFound: ['Minor prop wear', 'Slight imbalance'],
      resolution: 'Propeller rebalanced, wear within acceptable limits',
      nextMaintenanceDate: '2024-04-10'
    },
    {
      id: 'MH-2024-004',
      workOrderId: 'WO-2023-044',
      title: 'Firmware Update Package',
      droneId: 'DRN-002',
      droneName: 'Inspector Delta',
      type: 'Preventive',
      status: 'Completed',
      technician: 'Lisa Chen',
      startDate: '2024-01-08',
      completionDate: '2024-01-08',
      duration: '2 hours',
      cost: 0,
      location: 'Remote',
      description: 'Updated flight control firmware and sensor calibration software.',
      partsUsed: [],
      issuesFound: ['Outdated firmware', 'Sensor drift'],
      resolution: 'Firmware updated to latest version, sensors recalibrated',
      nextMaintenanceDate: '2024-07-08'
    },
    {
      id: 'MH-2024-005',
      workOrderId: 'WO-2023-043',
      title: 'Motor Bearing Replacement',
      droneId: 'DRN-004',
      droneName: 'Patrol Echo',
      type: 'Corrective',
      status: 'Completed',
      technician: 'John Smith',
      startDate: '2024-01-05',
      completionDate: '2024-01-06',
      duration: '6 hours',
      cost: 320,
      location: 'Workshop A',
      description: 'Replaced worn motor bearings after vibration detected during flight.',
      partsUsed: ['Motor Bearing Set', 'Motor Oil', 'Gasket Kit'],
      issuesFound: ['Bearing wear', 'Motor vibration', 'Oil contamination'],
      resolution: 'Complete motor overhaul with new bearings and lubrication',
      nextMaintenanceDate: '2024-10-05'
    }
  ];

  const getTypeColor = (type) => {
    switch (type) {
      case 'Preventive': return 'bg-green-100 text-green-700';
      case 'Corrective': return 'bg-orange-100 text-orange-700';
      case 'Emergency': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const filteredHistory = maintenanceHistory.filter(record => {
    const matchesSearch = record.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.droneId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.technician.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = filterType === 'all' || record.type.toLowerCase() === filterType.toLowerCase();
    const matchesDrone = filterDrone === 'all' || record.droneId === filterDrone;
    return matchesSearch && matchesType && matchesDrone;
  });

  const totalCost = filteredHistory.reduce((sum, record) => sum + record.cost, 0);
  const avgCost = filteredHistory.length > 0 ? totalCost / filteredHistory.length : 0;
  const totalHours = filteredHistory.reduce((sum, record) => sum + parseFloat(record.duration), 0);

  const headerActions = (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search history..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:border-transparent"
          onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
          onBlur={(e) => e.target.style.boxShadow = 'none'}
        />
      </div>
      
      <select
        value={filterType}
        onChange={(e) => setFilterType(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="all">All Types</option>
        <option value="preventive">Preventive</option>
        <option value="corrective">Corrective</option>
        <option value="emergency">Emergency</option>
      </select>

      <select
        value={dateRange}
        onChange={(e) => setDateRange(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2"
        onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px #a5b4fc'}
        onBlur={(e) => e.target.style.boxShadow = 'none'}
      >
        <option value="7days">Last 7 days</option>
        <option value="30days">Last 30 days</option>
        <option value="90days">Last 90 days</option>
        <option value="1year">Last year</option>
      </select>

      <button className="px-4 py-2 text-blue-700 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors flex items-center gap-2">
        <Download className="w-4 h-4" />
        Export Report
      </button>
    </div>
  );

  return (
    <QCLayout
      title="Maintenance History"
      subtitle="Track and analyze completed maintenance activities"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Summary */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{filteredHistory.length}</div>
              <div className="text-sm text-gray-600">Total Records</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">${totalCost.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Total Cost</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{totalHours.toFixed(1)}h</div>
              <div className="text-sm text-gray-600">Total Hours</div>
            </div>
          </div>
        </div>

        {/* Detailed Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed Tasks</p>
                <p className="text-2xl font-bold text-gray-900">{filteredHistory.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <History className="w-3 h-3" />
                  This period
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <History className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average Cost</p>
                <p className="text-2xl font-bold text-gray-900">${Math.round(avgCost)}</p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <TrendingDown className="w-3 h-3" />
                  Per task
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <DollarSign className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Preventive</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredHistory.filter(r => r.type === 'Preventive').length}
                </p>
                <p className="text-sm text-purple-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  Scheduled
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#faf5ff'}}>
                <CheckCircle className="w-6 h-6 text-purple-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Corrective</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredHistory.filter(r => r.type === 'Corrective').length}
                </p>
                <p className="text-sm text-orange-600 flex items-center gap-1 mt-1">
                  <Wrench className="w-3 h-3" />
                  Repairs
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fffbeb'}}>
                <Wrench className="w-6 h-6 text-orange-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Maintenance History Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Maintenance Records</h3>
                <p className="text-sm text-gray-600 mt-1">Systematic view of all completed maintenance activities</p>
              </div>
              <div className="flex items-center gap-2">
                <button className="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md transition-colors flex items-center gap-1">
                  <BarChart3 className="w-4 h-4" />
                  Analytics
                </button>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Maintenance Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Drone & Technician
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Timeline & Cost
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Issues & Resolution
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredHistory.map((record) => (
                  <tr key={record.id} className="hover:bg-gray-50 transition-colors">
                    {/* Maintenance Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                          <History className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="text-sm font-medium text-gray-900">{record.title}</h4>
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getTypeColor(record.type)}`}>
                              {record.type}
                            </span>
                          </div>
                          <p className="text-xs text-gray-600 mb-2">{record.description}</p>
                          <div className="flex items-center gap-3 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <FileText className="w-3 h-3" />
                              {record.id}
                            </span>
                            <span className="flex items-center gap-1">
                              <MapPin className="w-3 h-3" />
                              {record.location}
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Drone & Technician */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{record.droneId}</p>
                          <p className="text-xs text-gray-600">{record.droneName}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                            <User className="w-3 h-3 text-blue-500" />
                          </div>
                          <p className="text-sm text-gray-900">{record.technician}</p>
                        </div>
                      </div>
                    </td>

                    {/* Timeline & Cost */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-1 text-sm">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          <span className="font-medium text-gray-900">{record.completionDate}</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">{record.duration}</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm">
                          <DollarSign className="w-4 h-4 text-gray-400" />
                          <span className="font-medium text-gray-900">${record.cost}</span>
                        </div>
                      </div>
                    </td>

                    {/* Issues & Resolution */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        {record.issuesFound.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Issues Found:</p>
                            <div className="space-y-1">
                              {record.issuesFound.slice(0, 2).map((issue, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <AlertTriangle className="w-3 h-3 text-red-500 flex-shrink-0" />
                                  <span className="text-xs text-red-700">{issue}</span>
                                </div>
                              ))}
                              {record.issuesFound.length > 2 && (
                                <p className="text-xs text-gray-500">+{record.issuesFound.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}

                        {record.partsUsed.length > 0 && (
                          <div>
                            <p className="text-xs text-gray-500 mb-1">Parts Used:</p>
                            <div className="space-y-1">
                              {record.partsUsed.slice(0, 2).map((part, index) => (
                                <div key={index} className="flex items-center gap-1">
                                  <Wrench className="w-3 h-3 text-blue-500 flex-shrink-0" />
                                  <span className="text-xs text-blue-700">{part}</span>
                                </div>
                              ))}
                              {record.partsUsed.length > 2 && (
                                <p className="text-xs text-gray-500">+{record.partsUsed.length - 2} more</p>
                              )}
                            </div>
                          </div>
                        )}

                        <div className="mt-2 pt-2 border-t border-gray-100">
                          <p className="text-xs text-gray-500 mb-1">Resolution:</p>
                          <p className="text-xs text-green-700">{record.resolution}</p>
                        </div>
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="View Details"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          className="p-2 text-gray-400 hover:text-green-600 transition-colors rounded-lg hover:bg-green-50"
                          title="Download Report"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing <span className="font-medium">1</span> to <span className="font-medium">{filteredHistory.length}</span> of{' '}
                <span className="font-medium">{filteredHistory.length}</span> records
              </div>
              <div className="flex items-center gap-2">
                <button className="px-3 py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50" disabled>
                  Previous
                </button>
                <button className="px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-md">
                  1
                </button>
                <button className="px-3 py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50" disabled>
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </QCLayout>
  );
};

export default MaintenanceHistory;
