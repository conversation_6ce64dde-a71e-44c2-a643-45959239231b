const mongoose = require('mongoose');
const Notification = require('./models/Notification');
const Individual = require('./models/Individual');
require('dotenv').config();

async function createTestIndividualNotification() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get an existing individual or create a mock one
    let individual = await Individual.findOne();
    
    if (!individual) {
      console.log('📝 No existing individual found, creating a mock notification...');
      // Create a mock notification for testing
      const testNotification = new Notification({
        title: 'New Individual Registered: Test User',
        message: 'A new individual "Test User" has been registered in the system with 0 drones allocated.',
        type: 'individual_created',
        category: 'user_management',
        priority: 'medium',
        triggeredBy: {
          userId: null,
          username: 'Admin',
          role: 'admin'
        },
        metadata: {
          action: 'created'
        }
      });

      await testNotification.save();
      console.log('✅ Mock individual notification created successfully');
    } else {
      console.log('📝 Found existing individual, creating notification...');
      console.log('Individual data:', JSON.stringify(individual, null, 2));

      // Handle different data structures
      const fullName = individual.fullName ||
                      (individual.personalInfo ? `${individual.personalInfo.firstName} ${individual.personalInfo.lastName}` : '') ||
                      `${individual.firstName || 'Unknown'} ${individual.lastName || 'User'}`;

      // Create a real notification for the existing individual
      const testNotification = new Notification({
        title: `Individual Updated: ${fullName}`,
        message: `Individual "${fullName}" profile has been updated. Changes may include contact information, status, or drone allocation.`,
        type: 'individual_updated',
        category: 'user_management',
        priority: 'low',
        triggeredBy: {
          userId: null,
          username: 'System',
          role: 'admin'
        },
        relatedEntity: {
          entityType: 'individual',
          entityId: individual._id,
          entityName: fullName
        },
        metadata: {
          action: 'updated',
          newValues: {
            name: fullName,
            email: individual.contact?.primaryEmail || individual.email,
            phone: individual.contact?.primaryPhone || individual.phone,
            dronesAllocated: individual.dronesAllocated || 0,
            status: individual.status || 'active'
          }
        }
      });

      await testNotification.save();
      console.log('✅ Individual notification created successfully');
    }

    // Check total notifications count
    const totalNotifications = await Notification.countDocuments();
    console.log('📊 Total notifications in database:', totalNotifications);

    // List recent notifications
    const recentNotifications = await Notification.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title type createdAt');
    
    console.log('📋 Recent notifications:');
    recentNotifications.forEach((notif, index) => {
      console.log(`   ${index + 1}. ${notif.title} (${notif.type}) - ${notif.createdAt}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

createTestIndividualNotification();
