import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import { useNavigate } from "react-router-dom";
import {
  MapPin,
  Maximize2,
  Filter,
  Activity,
  AlertTriangle,
  Settings,
  Pause,
  RefreshCw,
  Building2,
  User,
  Users,
  Eye
} from 'lucide-react';
import dashboardDataService from './DashboardDataService';

// Fix for Leaflet default markers - this is essential for markers to display
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom icons for different drone statuses
const createDroneIcon = (status) => {
  const colors = {
    'Active': '#10b981',
    'Inactive': '#6b7280',
    'Crashed': '#ef4444',
    'Maintenance': '#f59e0b'
  };

  return new L.DivIcon({
    html: `
      <div style="
        background-color: ${colors[status] || '#6b7280'};
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
      ">
        <div style="
          width: 8px;
          height: 8px;
          background-color: white;
          border-radius: 50%;
          ${status === 'Active' ? 'animation: pulse 2s infinite;' : ''}
        "></div>
      </div>
      <style>
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
      </style>
    `,
    className: 'custom-drone-icon',
    iconSize: [24, 24],
    iconAnchor: [12, 12],
    popupAnchor: [0, -12],
  });
};

const DroneMap = () => {
  const [drones, setDrones] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedOwnerType, setSelectedOwnerType] = useState('All');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedDrone, setSelectedDrone] = useState(null);
  const navigate = useNavigate();

  const center = [20.5937, 78.9629]; // Default to India center

  // Load drone data from service and set up real-time updates
  useEffect(() => {
    const loadDroneData = async () => {
      try {
        setIsLoading(true);
        const droneData = await dashboardDataService.fetchDroneData();
        setDrones(droneData);
      } catch (error) {
        // Silently handle error
      } finally {
        setIsLoading(false);
      }
    };

    // Load initial data
    loadDroneData();

    // Subscribe to real-time drone updates
    const unsubscribe = dashboardDataService.subscribe('drones', (updatedDrones) => {
      setDrones(updatedDrones);
    });

    // Start real-time updates
    dashboardDataService.startRealTimeUpdates();

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, []);

  // Enhanced filtering for both status and owner type
  const filteredDrones = drones.filter((drone) => {
    const statusMatch = selectedStatus === 'All' || drone.status === selectedStatus;
    const ownerTypeMatch = selectedOwnerType === 'All' || drone.ownerType === selectedOwnerType;
    return statusMatch && ownerTypeMatch;
  });



  const getStatusIcon = (status) => {
    switch (status) {
      case 'Active': return <Activity className="w-4 h-4 text-green-600" />;
      case 'Inactive': return <Pause className="w-4 h-4 text-gray-600" />;
      case 'Crashed': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'Maintenance': return <Settings className="w-4 h-4 text-amber-600" />;
      default: return <MapPin className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'text-green-600 bg-green-50';
      case 'Inactive': return 'text-gray-600 bg-gray-50';
      case 'Crashed': return 'text-red-600 bg-red-50';
      case 'Maintenance': return 'text-amber-600 bg-amber-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      const droneData = await dashboardDataService.fetchDroneData();
      setDrones(droneData);
    } catch (error) {
      // Silently handle error
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden h-[600px] flex flex-col">
      <div className="p-6 border-b border-gray-100 flex-shrink-0">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <MapPin className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-gray-800">
                Live Drone Tracking
              </h2>
              <p className="text-sm text-gray-500">
                Real-time drone locations and status
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Owner Type Filter */}
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-gray-400" />
              <select
                className="border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-300"
                value={selectedOwnerType}
                onChange={(e) => setSelectedOwnerType(e.target.value)}
              >
                <option value="All">All Types ({drones.length})</option>
                <option value="organization">Organizations ({drones.filter(d => d.ownerType === 'organization').length})</option>
                <option value="individual">Individuals ({drones.filter(d => d.ownerType === 'individual').length})</option>
              </select>
            </div>

            {/* Status Filter */}
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                className="border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-300"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <option value="All">All Status ({filteredDrones.length})</option>
                <option value="Active">Active ({drones.filter(d => d.status === 'Active').length})</option>
                <option value="Inactive">Inactive ({drones.filter(d => d.status === 'Inactive').length})</option>
                <option value="Maintenance">Maintenance ({drones.filter(d => d.status === 'Maintenance').length})</option>
                <option value="Crashed">Crashed ({drones.filter(d => d.status === 'Crashed').length})</option>
              </select>
            </div>

            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              {isRefreshing ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              Refresh
            </button>

            <button
              onClick={() => navigate("/map")}
              className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              <Maximize2 className="w-4 h-4" />
              Full Map
            </button>
          </div>
        </div>
      </div>

      <div className="p-6 flex-1 flex flex-col">
        <div className="rounded-xl overflow-hidden shadow-inner relative flex-1">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
              <div className="text-center">
                <RefreshCw className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-2" />
                <p className="text-gray-600">Loading drone data...</p>
              </div>
            </div>
          )}
          <MapContainer
            key="drone-map"
            center={center}
            zoom={5}
            scrollWheelZoom={true}
            style={{
              width: '100%',
              height: '100%',
              zIndex: 1,
              minHeight: '400px',
              display: 'block'
            }}
          >
            <TileLayer
              url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
              attribution='Tiles &copy; <a href="https://www.esri.com/">Esri</a> — Source: Esri, Maxar, Earthstar Geographics'
            />

            {!isLoading && filteredDrones.map((drone) => (
              <Marker
                key={drone.id}
                position={[drone.lat, drone.lng]}
                icon={createDroneIcon(drone.status)}
              >
                <Popup className="custom-popup">
                  <div className="p-3 min-w-[280px]">
                    {/* Header with Status */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(drone.status)}
                        <h3 className="font-bold text-gray-800">{drone.name}</h3>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(drone.status)}`}>
                        {drone.status}
                      </span>
                    </div>

                    {/* Owner Information */}
                    <div className="mb-3 p-2 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-1">
                        {drone.ownerType === 'organization' ? (
                          <Building2 className="w-4 h-4 text-blue-600" />
                        ) : (
                          <User className="w-4 h-4 text-green-600" />
                        )}
                        <span className="text-xs font-medium text-gray-600 uppercase">
                          {drone.ownerType === 'organization' ? 'Organization' : 'Individual'}
                        </span>
                      </div>
                      <p className="font-medium text-gray-800">{drone.ownerName}</p>
                    </div>

                    {/* Drone Details */}
                    <div className="space-y-2 text-sm">
                      {drone.model && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Model:</span>
                          <span className="font-medium">{drone.model}</span>
                        </div>
                      )}
                      {drone.registrationNumber && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Registration:</span>
                          <span className="font-medium">{drone.registrationNumber}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-gray-600">Battery:</span>
                        <span className={`font-medium ${drone.battery < 20 ? 'text-red-600' : drone.battery < 50 ? 'text-amber-600' : 'text-green-600'}`}>
                          {drone.battery}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Altitude:</span>
                        <span className="font-medium">{drone.altitude}m</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Location:</span>
                        <span className="font-mono text-xs">{drone.lat.toFixed(4)}, {drone.lng.toFixed(4)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Last Update:</span>
                        <span className="text-blue-600 text-xs">
                          {drone.lastUpdate ?
                            (typeof drone.lastUpdate === 'string' ?
                              drone.lastUpdate :
                              new Date(drone.lastUpdate).toLocaleString()
                            ) :
                            'Unknown'
                          }
                        </span>
                      </div>
                    </div>

                    {/* Action Button */}
                    <button
                      onClick={() => setSelectedDrone(drone)}
                      className="mt-3 w-full bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                    >
                      <Eye className="w-4 h-4" />
                      View Details
                    </button>
                  </div>
                </Popup>
              </Marker>
            ))}
          </MapContainer>
        </div>
      </div>
    </div>
  );
};

export default DroneMap;
