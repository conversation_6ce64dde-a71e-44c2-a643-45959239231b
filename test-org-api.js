const axios = require('axios');

// Test Organization Portal API
const BASE_URL = 'http://localhost:5000/api';

async function testOrgAPI() {
  console.log('🧪 Testing Organization Portal API...\n');

  try {
    // Test 1: Test endpoint (no auth required)
    console.log('1️⃣ Testing API Connection...');
    try {
      const testResponse = await axios.get(`${BASE_URL}/org-portal/test`);
      console.log('✅ API Connection successful:', testResponse.data.message);
    } catch (error) {
      console.log('❌ API Connection failed:', error.response?.data?.message || error.message);
      return;
    }

    // Test 2: Test authentication (this will fail without token, which is expected)
    console.log('\n2️⃣ Testing Authentication (should fail without token)...');
    try {
      const authResponse = await axios.get(`${BASE_URL}/org-portal/dashboard`);
      console.log('❌ Unexpected success - authentication should have failed');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Authentication properly rejected unauthorized request');
      } else {
        console.log('❌ Unexpected error:', error.response?.data?.message || error.message);
      }
    }

    // Test 3: Test with mock token (for development)
    console.log('\n3️⃣ Testing with Mock Token...');
    console.log('⚠️  Note: This requires a valid JWT token for a user with organization access');
    console.log('⚠️  You can get a token by logging in through the frontend or creating one manually');

    // Instructions for getting a token
    console.log('\n📝 To get a valid token:');
    console.log('   1. Start the frontend application');
    console.log('   2. Login as an organization user');
    console.log('   3. Check browser localStorage for the token');
    console.log('   4. Update this script with the token');
    console.log('   5. Or use the browser network tab to see the Authorization header');

    console.log('\n🎉 Basic API Test Complete!');
    console.log('✅ API is responding correctly');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Function to test with a real token (update TOKEN variable)
async function testWithToken(token) {
  console.log('\n🔐 Testing with Authentication Token...\n');

  const api = axios.create({
    baseURL: BASE_URL,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  try {
    // Test dashboard
    console.log('1️⃣ Testing Dashboard...');
    const dashboardResponse = await api.get('/org-portal/dashboard');
    console.log('✅ Dashboard data retrieved:', dashboardResponse.data.success);

    // Test drones list
    console.log('\n2️⃣ Testing Drones List...');
    const dronesResponse = await api.get('/org-portal/drones');
    console.log('✅ Drones list retrieved:', dronesResponse.data.success);
    console.log('   - Total drones:', dronesResponse.data.data?.total || 0);

    // Test add drone
    console.log('\n3️⃣ Testing Add Drone...');
    const testDroneData = {
      name: 'API Test Drone',
      model: 'DJI Mini 2',
      manufacturer: 'DJI',
      serialNumber: `API-TEST-${Date.now()}`,
      registrationNumber: `REG-API-${Date.now()}`,
      specifications: {
        type: 'quadcopter',
        weight: 0.249,
        maxFlightTime: 31,
        batteryCapacity: 2250,
        hasGPS: true
      },
      purchase: {
        purchaseDate: new Date().toISOString(),
        purchasePrice: 449,
        vendor: 'DJI Store'
      }
    };

    const addDroneResponse = await api.post('/org-portal/drones', testDroneData);
    console.log('✅ Drone added successfully:', addDroneResponse.data.success);
    console.log('   - Drone ID:', addDroneResponse.data.data?.drone?._id);

    console.log('\n🎉 Authenticated API Test Complete!');
    console.log('✅ All authenticated endpoints working correctly');

  } catch (error) {
    console.error('❌ Authenticated test failed:', error.response?.data?.message || error.message);
  }
}

// Run the test
if (require.main === module) {
  console.log('🚀 Starting Organization Portal API Test...');
  console.log('🌐 Make sure the server is running on http://localhost:5000\n');
  
  testOrgAPI().catch(console.error);

  // Uncomment and add a real token to test authenticated endpoints
  // const REAL_TOKEN = 'your-jwt-token-here';
  // if (REAL_TOKEN && REAL_TOKEN !== 'your-jwt-token-here') {
  //   testWithToken(REAL_TOKEN).catch(console.error);
  // }
}

module.exports = { testOrgAPI, testWithToken };
