import api from './api';

class OrganizationPortalService {
  /**
   * Test API connection
   */
  static async testConnection() {
    try {
      const response = await api.get('/org-portal/test');
      return response.data;
    } catch (error) {
      console.error('❌ Test connection error:', error);
      throw error;
    }
  }
  /**
   * Initialize organization data
   */
  static async initializeOrganization() {
    try {
      const response = await api.post('/org-portal/initialize');
      return response.data;
    } catch (error) {
      console.error('❌ Initialize organization error:', error);
      throw error;
    }
  }

  /**
   * Get dashboard data
   */
  static async getDashboardData() {
    try {
      const response = await api.get('/org-portal/dashboard');
      return response.data;
    } catch (error) {
      console.error('❌ Get dashboard data error:', error);
      throw error;
    }
  }

  /**
   * Get organization drones
   */
  static async getDrones(params = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.page) queryParams.append('page', params.page);
      if (params.limit) queryParams.append('limit', params.limit);
      if (params.status) queryParams.append('status', params.status);
      if (params.search) queryParams.append('search', params.search);

      const response = await api.get(`/org-portal/drones?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('❌ Get drones error:', error);
      throw error;
    }
  }

  /**
   * Get specific drone by ID
   */
  static async getDroneById(droneId) {
    try {
      const response = await api.get(`/org-portal/drones/${droneId}`);
      return response.data;
    } catch (error) {
      console.error('❌ Get drone by ID error:', error);
      throw error;
    }
  }

  /**
   * Add new drone
   */
  static async addDrone(droneData) {
    try {
      const response = await api.post('/org-portal/drones', droneData);
      return response.data;
    } catch (error) {
      console.error('❌ Add drone error:', error);
      throw error;
    }
  }

  /**
   * Update drone
   */
  static async updateDrone(droneId, updateData) {
    try {
      const response = await api.put(`/org-portal/drones/${droneId}`, updateData);
      return response.data;
    } catch (error) {
      console.error('❌ Update drone error:', error);
      throw error;
    }
  }

  /**
   * Delete drone
   */
  static async deleteDrone(droneId) {
    try {
      const response = await api.delete(`/org-portal/drones/${droneId}`);
      return response.data;
    } catch (error) {
      console.error('❌ Delete drone error:', error);
      throw error;
    }
  }

  /**
   * Update drone status
   */
  static async updateDroneStatus(droneId, status) {
    try {
      const response = await api.patch(`/org-portal/drones/${droneId}/status`, { status });
      return response.data;
    } catch (error) {
      console.error('❌ Update drone status error:', error);
      throw error;
    }
  }

  /**
   * Add note to drone
   */
  static async addDroneNote(droneId, noteData) {
    try {
      const response = await api.post(`/org-portal/drones/${droneId}/notes`, noteData);
      return response.data;
    } catch (error) {
      console.error('❌ Add drone note error:', error);
      throw error;
    }
  }

  /**
   * Get organization statistics
   */
  static async getStatistics() {
    try {
      const response = await api.get('/org-portal/statistics');
      return response.data;
    } catch (error) {
      console.error('❌ Get statistics error:', error);
      throw error;
    }
  }

  /**
   * Get organization analytics
   */
  static async getAnalytics() {
    try {
      const response = await api.get('/org-portal/analytics');
      return response.data;
    } catch (error) {
      console.error('❌ Get analytics error:', error);
      throw error;
    }
  }

  /**
   * Get map data
   */
  static async getMapData() {
    try {
      const response = await api.get('/org-portal/map-data');
      return response.data;
    } catch (error) {
      console.error('❌ Get map data error:', error);
      throw error;
    }
  }

  /**
   * Validate drone data before submission
   */
  static validateDroneData(droneData) {
    const errors = [];

    // Required fields
    if (!droneData.name || droneData.name.trim().length < 2) {
      errors.push('Drone name is required and must be at least 2 characters');
    }

    if (!droneData.model || droneData.model.trim().length < 2) {
      errors.push('Model is required and must be at least 2 characters');
    }

    if (!droneData.manufacturer || droneData.manufacturer.trim().length < 2) {
      errors.push('Manufacturer is required and must be at least 2 characters');
    }

    if (!droneData.serialNumber || droneData.serialNumber.trim().length < 5) {
      errors.push('Serial number is required and must be at least 5 characters');
    }

    if (!droneData.registrationNumber || droneData.registrationNumber.trim().length < 5) {
      errors.push('Registration number is required and must be at least 5 characters');
    }

    // Purchase information
    if (!droneData.purchase) {
      errors.push('Purchase information is required');
    } else {
      if (!droneData.purchase.purchaseDate) {
        errors.push('Purchase date is required');
      }
      if (!droneData.purchase.purchasePrice || droneData.purchase.purchasePrice < 0) {
        errors.push('Purchase price is required and must be positive');
      }
      if (!droneData.purchase.vendor || droneData.purchase.vendor.trim().length < 2) {
        errors.push('Vendor is required and must be at least 2 characters');
      }
    }

    // Specifications validation
    if (droneData.specifications) {
      if (droneData.specifications.weight && (droneData.specifications.weight < 0.1 || droneData.specifications.weight > 25)) {
        errors.push('Weight must be between 0.1 and 25 kg');
      }
      if (droneData.specifications.maxPayload && (droneData.specifications.maxPayload < 0 || droneData.specifications.maxPayload > 10)) {
        errors.push('Max payload must be between 0 and 10 kg');
      }
      if (droneData.specifications.maxFlightTime && (droneData.specifications.maxFlightTime < 1 || droneData.specifications.maxFlightTime > 300)) {
        errors.push('Max flight time must be between 1 and 300 minutes');
      }
      if (droneData.specifications.maxRange && (droneData.specifications.maxRange < 0.1 || droneData.specifications.maxRange > 100)) {
        errors.push('Max range must be between 0.1 and 100 km');
      }
      if (droneData.specifications.maxAltitude && (droneData.specifications.maxAltitude < 1 || droneData.specifications.maxAltitude > 500)) {
        errors.push('Max altitude must be between 1 and 500 meters');
      }
      if (droneData.specifications.maxSpeed && (droneData.specifications.maxSpeed < 1 || droneData.specifications.maxSpeed > 200)) {
        errors.push('Max speed must be between 1 and 200 km/h');
      }
      if (droneData.specifications.batteryCapacity && (droneData.specifications.batteryCapacity < 100 || droneData.specifications.batteryCapacity > 50000)) {
        errors.push('Battery capacity must be between 100 and 50000 mAh');
      }
    }

    return errors;
  }

  /**
   * Format drone data for API submission
   */
  static formatDroneData(formData) {
    return {
      name: formData.droneName?.trim(),
      model: formData.modelNumber?.trim(),
      manufacturer: formData.manufacturer?.trim(),
      serialNumber: formData.serialNumber?.trim(),
      registrationNumber: formData.registrationNumber?.trim(),
      specifications: {
        type: formData.droneType || 'quadcopter',
        weight: formData.weight ? parseFloat(formData.weight) : undefined,
        maxPayload: formData.maxPayload ? parseFloat(formData.maxPayload) : undefined,
        maxFlightTime: formData.maxFlightTime ? parseInt(formData.maxFlightTime) : undefined,
        maxRange: formData.maxRange ? parseFloat(formData.maxRange) : undefined,
        maxAltitude: formData.maxAltitude ? parseInt(formData.maxAltitude) : undefined,
        maxSpeed: formData.maxSpeed ? parseInt(formData.maxSpeed) : undefined,
        batteryCapacity: formData.batteryCapacity ? parseInt(formData.batteryCapacity) : undefined,
        cameraResolution: formData.cameraResolution?.trim(),
        hasGimbal: formData.hasGimbal || false,
        hasGPS: formData.hasGPS !== false,
        hasObstacleAvoidance: formData.hasObstacleAvoidance || false
      },
      purchase: {
        purchaseDate: formData.purchaseDate,
        purchasePrice: formData.purchasePrice ? parseFloat(formData.purchasePrice) : 0,
        vendor: formData.vendor?.trim(),
        warrantyExpiryDate: formData.warrantyExpiryDate || null
      },
      currentLocation: {
        latitude: formData.latitude ? parseFloat(formData.latitude) : 0,
        longitude: formData.longitude ? parseFloat(formData.longitude) : 0,
        altitude: formData.altitude ? parseFloat(formData.altitude) : 0
      },
      status: formData.status || 'active',
      condition: formData.condition || 'excellent'
    };
  }

  /**
   * Generate unique drone ID for frontend display
   */
  static generateDroneId() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `DRN-${new Date().getFullYear()}-${random}-${timestamp.toString().slice(-6)}`;
  }
}

export default OrganizationPortalService;
