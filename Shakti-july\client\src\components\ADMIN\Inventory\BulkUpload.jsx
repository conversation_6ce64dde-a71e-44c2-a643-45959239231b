import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Upload,
  File,
  CheckCircle,
  AlertTriangle,
  X,
  Download,
  RefreshCw,
  FileText,
  Database,
  Eye,
  Trash2
} from 'lucide-react';
import {
  FaCloudUploadAlt,
  FaFile,
  FaCheckCircle,
  FaExclamationTriangle,
  FaTimes,
  FaDownload,
  FaFileAlt,
  FaDatabase,
  FaEye,
  FaTrash
} from 'react-icons/fa';

import AdminSidebar from '../common/AdminSidebar';

const BulkUpload = () => {
  const navigate = useNavigate();
  const fileInputRef = useRef(null);
  
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);
  const [previewData, setPreviewData] = useState([]);
  const [validationErrors, setValidationErrors] = useState([]);
  const [showPreview, setShowPreview] = useState(false);

  const supportedFormats = [
    { extension: '.csv', description: 'Comma Separated Values', icon: <FaFileAlt className="w-6 h-6 text-green-600" /> },
    { extension: '.xlsx', description: 'Excel Spreadsheet', icon: <FaFileAlt className="w-6 h-6 text-blue-600" /> },
    { extension: '.json', description: 'JSON Data Format', icon: <FaFileAlt className="w-6 h-6 text-purple-600" /> }
  ];

  const requiredFields = [
    'Drone ID',
    'Drone Name',
    'Model',
    'Serial Number',
    'Status',
    'Battery Level',
    'Last Maintenance',
    'Organization'
  ];

  // Mock data for preview
  const mockPreviewData = [
    {
      'Drone ID': 'PRYMAJ95175',
      'Drone Name': 'Falcon-X1',
      'Model': 'DJI Phantom 4',
      'Serial Number': 'SN001234',
      'Status': 'Active',
      'Battery Level': '85%',
      'Last Maintenance': '2024-01-15',
      'Organization': 'TechCorp'
    },
    {
      'Drone ID': 'PRYMAJ95176',
      'Drone Name': 'Eagle-Pro',
      'Model': 'DJI Mavic Air',
      'Serial Number': 'SN001235',
      'Status': 'Maintenance',
      'Battery Level': '45%',
      'Last Maintenance': '2024-01-20',
      'Organization': 'InnovateLab'
    },
    {
      'Drone ID': 'PRYMAJ95177',
      'Drone Name': 'Hawk-Elite',
      'Model': 'DJI Mini 2',
      'Serial Number': 'SN001236',
      'Status': 'Active',
      'Battery Level': '92%',
      'Last Maintenance': '2024-01-10',
      'Organization': 'SkyTech'
    }
  ];

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = (file) => {
    // Validate file type
    const allowedTypes = ['.csv', '.xlsx', '.json'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
      alert('Please upload a valid file format (.csv, .xlsx, or .json)');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    setUploadedFile(file);
    setUploadComplete(false);
    setValidationErrors([]);
    
    // Simulate file processing
    processFile(file);
  };

  const processFile = (file) => {
    setIsUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsUploading(false);
          setUploadComplete(true);
          
          // Simulate data processing and validation
          setTimeout(() => {
            setPreviewData(mockPreviewData);
            validateData(mockPreviewData);
          }, 500);
          
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const validateData = (data) => {
    const errors = [];
    
    // Check for required fields
    if (data.length > 0) {
      const headers = Object.keys(data[0]);
      const missingFields = requiredFields.filter(field => !headers.includes(field));
      
      if (missingFields.length > 0) {
        errors.push({
          type: 'missing_fields',
          message: `Missing required fields: ${missingFields.join(', ')}`,
          severity: 'error'
        });
      }
    }

    // Simulate some validation errors
    if (data.length > 0) {
      errors.push({
        type: 'duplicate_id',
        message: 'Row 2: Duplicate Drone ID found',
        severity: 'warning'
      });
      
      errors.push({
        type: 'invalid_status',
        message: 'Row 3: Invalid status value',
        severity: 'error'
      });
    }

    setValidationErrors(errors);
  };

  const handleUpload = () => {
    if (!uploadedFile) return;

    // Simulate final upload
    setIsUploading(true);
    setTimeout(() => {
      setIsUploading(false);
      alert('Data uploaded successfully! ' + previewData.length + ' records processed.');
      navigate('/inventory');
    }, 2000);
  };

  const handleRemoveFile = () => {
    setUploadedFile(null);
    setUploadProgress(0);
    setUploadComplete(false);
    setPreviewData([]);
    setValidationErrors([]);
    setShowPreview(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const downloadTemplate = () => {
    // Create CSV template
    const headers = requiredFields.join(',');
    const sampleRow = 'PRYMAJ95175,Falcon-X1,DJI Phantom 4,SN001234,Active,85%,2024-01-15,TechCorp';
    const csvContent = headers + '\n' + sampleRow;
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'drone-inventory-template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-[#f8f9fa] to-[#e9f2f9] text-black">
      <AdminSidebar />
      
      {/* Main Content */}
      <div className="lg:pl-[250px] w-full">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200 px-4 lg:px-6 py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/inventory')}
                className="flex items-center gap-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <ArrowLeft size={16} />
                <span className="hidden sm:inline">Back to Inventory</span>
              </button>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <FaCloudUploadAlt className="text-blue-600" />
                  Bulk Upload
                </h2>
                <p className="text-gray-600 mt-1">Import drone inventory data from CSV, Excel, or JSON files</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={downloadTemplate}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Download size={18} />
                <span className="hidden sm:inline">Download Template</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 lg:p-6 space-y-6">
          {/* Upload Area */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Upload className="w-5 h-5 text-blue-600" />
              Upload File
            </h3>
            
            {!uploadedFile ? (
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive 
                    ? 'border-blue-400 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <FaCloudUploadAlt className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  Drop your file here, or{' '}
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="text-blue-600 hover:text-blue-700 underline"
                  >
                    browse
                  </button>
                </h4>
                <p className="text-sm text-gray-500 mb-4">
                  Supports CSV, Excel (.xlsx), and JSON files up to 10MB
                </p>
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv,.xlsx,.json"
                  onChange={handleFileInput}
                  className="hidden"
                />
              </div>
            ) : (
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <FaFile className="w-8 h-8 text-blue-600" />
                    <div>
                      <h4 className="font-medium text-gray-900">{uploadedFile.name}</h4>
                      <p className="text-sm text-gray-500">
                        {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={handleRemoveFile}
                    className="text-red-600 hover:text-red-700 p-1"
                  >
                    <Trash2 size={20} />
                  </button>
                </div>
                
                {isUploading && (
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">Processing...</span>
                      <span className="text-sm text-gray-600">{uploadProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  </div>
                )}
                
                {uploadComplete && (
                  <div className="flex items-center gap-2 text-green-600 mb-4">
                    <CheckCircle size={16} />
                    <span className="text-sm">File processed successfully</span>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Supported Formats */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FileText className="w-5 h-5 text-green-600" />
              Supported Formats
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {supportedFormats.map((format, index) => (
                <div key={index} className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg">
                  {format.icon}
                  <div>
                    <div className="font-medium text-gray-900">{format.extension}</div>
                    <div className="text-sm text-gray-500">{format.description}</div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Required Fields:</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {requiredFields.map((field, index) => (
                  <span key={index} className="text-sm text-blue-700 bg-blue-100 px-2 py-1 rounded">
                    {field}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Validation Results */}
          {validationErrors.length > 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
                Validation Results
              </h3>

              <div className="space-y-3">
                {validationErrors.map((error, index) => (
                  <div
                    key={index}
                    className={`flex items-start gap-3 p-3 rounded-lg ${
                      error.severity === 'error'
                        ? 'bg-red-50 border border-red-200'
                        : 'bg-yellow-50 border border-yellow-200'
                    }`}
                  >
                    {error.severity === 'error' ? (
                      <FaExclamationTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                    ) : (
                      <FaExclamationTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                    )}
                    <div>
                      <div className={`font-medium ${
                        error.severity === 'error' ? 'text-red-900' : 'text-yellow-900'
                      }`}>
                        {error.severity === 'error' ? 'Error' : 'Warning'}
                      </div>
                      <div className={`text-sm ${
                        error.severity === 'error' ? 'text-red-700' : 'text-yellow-700'
                      }`}>
                        {error.message}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Data Preview */}
          {previewData.length > 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Database className="w-5 h-5 text-purple-600" />
                  Data Preview ({previewData.length} records)
                </h3>
                <button
                  onClick={() => setShowPreview(!showPreview)}
                  className="flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Eye size={16} />
                  {showPreview ? 'Hide Preview' : 'Show Preview'}
                </button>
              </div>

              {showPreview && (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        {Object.keys(previewData[0] || {}).map((header, index) => (
                          <th key={index} className="px-4 py-2 text-left font-medium text-gray-900">
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {previewData.slice(0, 5).map((row, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          {Object.values(row).map((value, cellIndex) => (
                            <td key={cellIndex} className="px-4 py-2 text-gray-700">
                              {value}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>

                  {previewData.length > 5 && (
                    <div className="mt-4 text-center text-sm text-gray-500">
                      Showing first 5 records of {previewData.length} total records
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Upload Actions */}
          {uploadComplete && previewData.length > 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Ready to Import</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {previewData.length} records ready for import
                    {validationErrors.filter(e => e.severity === 'error').length > 0 && (
                      <span className="text-red-600 ml-2">
                        ({validationErrors.filter(e => e.severity === 'error').length} errors must be fixed)
                      </span>
                    )}
                  </p>
                </div>

                <div className="flex items-center gap-3">
                  <button
                    onClick={handleRemoveFile}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleUpload}
                    disabled={isUploading || validationErrors.filter(e => e.severity === 'error').length > 0}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    {isUploading ? (
                      <>
                        <RefreshCw size={16} className="animate-spin" />
                        Importing...
                      </>
                    ) : (
                      <>
                        <Upload size={16} />
                        Import Data
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BulkUpload;
