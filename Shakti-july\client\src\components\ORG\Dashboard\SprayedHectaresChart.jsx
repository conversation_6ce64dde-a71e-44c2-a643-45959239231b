import React, { useEffect, useState } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
  Legend,
  ReferenceLine
} from "recharts";
import { Download, TrendingUp, BarChart3, Calendar } from "lucide-react";

// Enhanced dataset with multiple metrics
const initialData = [
  {
    month: "Jan",
    hectares: 60,
    target: 80,
    efficiency: 75,
    cost: 45000,
    rainfall: 120
  },
  {
    month: "Feb",
    hectares: 120,
    target: 100,
    efficiency: 85,
    cost: 52000,
    rainfall: 80
  },
  {
    month: "Mar",
    hectares: 140,
    target: 120,
    efficiency: 92,
    cost: 58000,
    rainfall: 60
  },
  {
    month: "Apr",
    hectares: 80,
    target: 110,
    efficiency: 68,
    cost: 48000,
    rainfall: 40
  },
  {
    month: "May",
    hectares: 145,
    target: 130,
    efficiency: 88,
    cost: 62000,
    rainfall: 20
  },
  {
    month: "Jun",
    hectares: 170,
    target: 150,
    efficiency: 95,
    cost: 68000,
    rainfall: 180
  },
  {
    month: "Jul",
    hectares: 130,
    target: 140,
    efficiency: 82,
    cost: 55000,
    rainfall: 220
  },
  {
    month: "Aug",
    hectares: 195,
    target: 160,
    efficiency: 98,
    cost: 72000,
    rainfall: 200
  },
  {
    month: "Sep",
    hectares: 65,
    target: 120,
    efficiency: 65,
    cost: 42000,
    rainfall: 150
  },
  {
    month: "Oct",
    hectares: 135,
    target: 125,
    efficiency: 87,
    cost: 58000,
    rainfall: 100
  },
  {
    month: "Nov",
    hectares: 150,
    target: 135,
    efficiency: 90,
    cost: 64000,
    rainfall: 80
  },
  {
    month: "Dec",
    hectares: 140,
    target: 130,
    efficiency: 89,
    cost: 60000,
    rainfall: 90
  },
];

const SprayedHectaresChart = () => {
  const [data, setData] = useState(initialData);
  const [viewType, setViewType] = useState('line');
  const [selectedMetric, setSelectedMetric] = useState('hectares');
  const [timeRange, setTimeRange] = useState('12m');

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      const updated = data.map((item) => ({
        ...item,
        hectares: Math.max(40, Math.min(200, item.hectares + Math.floor(Math.random() * 15 - 7))),
        efficiency: Math.max(60, Math.min(100, item.efficiency + Math.floor(Math.random() * 6 - 3))),
        cost: Math.max(30000, Math.min(80000, item.cost + Math.floor(Math.random() * 10000 - 5000)))
      }));
      setData(updated);
    }, 8000);

    return () => clearInterval(interval);
  }, [data]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800 mb-2">{`${label} 2024`}</p>
          {payload.map((entry, index) => (
            <div key={index} className="flex items-center gap-2 mb-1">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              ></div>
              <span className="text-sm text-gray-600 capitalize">
                {entry.dataKey === 'hectares' ? 'Sprayed' :
                 entry.dataKey === 'target' ? 'Target' :
                 entry.dataKey === 'efficiency' ? 'Efficiency' :
                 entry.dataKey === 'cost' ? 'Cost' : entry.dataKey}:
              </span>
              <span className="font-medium text-gray-800">
                {entry.dataKey === 'cost' ? `₹${entry.value.toLocaleString()}` :
                 entry.dataKey === 'efficiency' ? `${entry.value}%` :
                 entry.dataKey === 'rainfall' ? `${entry.value}mm` :
                 `${entry.value} ha`}
              </span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  const exportData = () => {
    const csvContent = "data:text/csv;charset=utf-8,"
      + "Month,Hectares,Target,Efficiency,Cost,Rainfall\n"
      + data.map(row => `${row.month},${row.hectares},${row.target},${row.efficiency},${row.cost},${row.rainfall}`).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "sprayed_hectares_data.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const totalHectares = data.reduce((sum, item) => sum + item.hectares, 0);
  const avgEfficiency = Math.round(data.reduce((sum, item) => sum + item.efficiency, 0) / data.length);
  const targetAchievement = Math.round((totalHectares / data.reduce((sum, item) => sum + item.target, 0)) * 100);

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-3">
        <div className="flex-1">
          <h2 className="text-base sm:text-lg font-bold text-gray-800 mb-1">Performance Analytics</h2>
          <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <TrendingUp size={14} className="text-green-600" />
              <span>Total: {totalHectares.toLocaleString()} ha</span>
            </div>
            <div>Avg: {avgEfficiency}%</div>
            <div>Target: {targetAchievement}%</div>
          </div>
        </div>

        <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
          {/* Time Range Selector */}
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-2 py-1 text-xs sm:text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="6m">6M</option>
            <option value="12m">12M</option>
            <option value="24m">24M</option>
          </select>

          {/* Chart Type Selector */}
          <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewType('line')}
              className={`p-1 rounded-md transition-colors ${
                viewType === 'line' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
              }`}
              title="Line Chart"
            >
              <TrendingUp size={14} />
            </button>
            <button
              onClick={() => setViewType('area')}
              className={`p-1 rounded-md transition-colors ${
                viewType === 'area' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
              }`}
              title="Area Chart"
            >
              <BarChart3 size={14} />
            </button>
          </div>

          {/* Export Button */}
          <button
            onClick={exportData}
            className="p-1 sm:p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            title="Export Data"
          >
            <Download size={14} />
          </button>
        </div>
      </div>

      {/* Chart Container - Fixed height to prevent overflow */}
      <div className="flex-1 w-full" style={{ height: 'calc(100% - 80px)', minHeight: '250px' }}>
        <ResponsiveContainer width="100%" height="100%">
          {viewType === 'area' ? (
            <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <defs>
                <linearGradient id="colorHectares" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="colorTarget" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="month"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Area
                type="monotone"
                dataKey="hectares"
                stroke="#3B82F6"
                strokeWidth={2}
                fillOpacity={1}
                fill="url(#colorHectares)"
                name="Sprayed Hectares"
              />
              <Area
                type="monotone"
                dataKey="target"
                stroke="#10B981"
                strokeWidth={2}
                fillOpacity={1}
                fill="url(#colorTarget)"
                name="Target"
              />
            </AreaChart>
          ) : (
            <LineChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="month"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line
                type="monotone"
                dataKey="hectares"
                stroke="#3B82F6"
                strokeWidth={3}
                dot={{ r: 4, fill: '#3B82F6' }}
                activeDot={{ r: 6, fill: '#3B82F6' }}
                name="Sprayed Hectares"
              />
              <Line
                type="monotone"
                dataKey="target"
                stroke="#10B981"
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={{ r: 3, fill: '#10B981' }}
                name="Target"
              />
              <Line
                type="monotone"
                dataKey="efficiency"
                stroke="#F59E0B"
                strokeWidth={2}
                dot={{ r: 3, fill: '#F59E0B' }}
                name="Efficiency %"
              />
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default SprayedHectaresChart;
