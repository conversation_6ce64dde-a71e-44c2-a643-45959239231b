import React, { useState } from 'react';
import QCLayout from '../common/QCLayout';
import {
  Users,
  Plus,
  Search,
  Edit,
  Trash2,
  Shield,
  Mail,
  Phone,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  EyeOff,
  Key,
  UserPlus,
  Settings,
  Crown,
  User
} from 'lucide-react';

const UserManagement = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [users, setUsers] = useState([]);

  // Initialize with sample user data
  React.useEffect(() => {
    if (users.length === 0) {
      setUsers([
        {
          id: 'USR-001',
          firstName: '<PERSON>',
          lastName: '<PERSON>',
          email: '<EMAIL>',
          phone: '+****************',
          role: 'QC Manager',
          department: 'Quality Control',
          status: 'Active',
          lastLogin: '2024-01-15 14:30',
          createdAt: '2023-06-15',
          permissions: ['read', 'write', 'delete', 'admin'],
          avatar: null,
          employeeId: 'EMP-001',
          location: 'Main Office',
          supervisor: 'Sarah Johnson',
          certifications: ['FAA Part 107', 'ISO 9001 Lead Auditor'],
          notes: 'Senior QC Manager with 10+ years experience'
        },
        {
          id: 'USR-002',
          firstName: 'Sarah',
          lastName: 'Johnson',
          email: '<EMAIL>',
          phone: '+****************',
          role: 'QC Inspector',
          department: 'Quality Control',
          status: 'Active',
          lastLogin: '2024-01-15 16:45',
          createdAt: '2023-08-20',
          permissions: ['read', 'write'],
          avatar: null,
          employeeId: 'EMP-002',
          location: 'Field Office A',
          supervisor: 'John Smith',
          certifications: ['FAA Part 107', 'Drone Inspection Certified'],
          notes: 'Specialized in field inspections and maintenance oversight'
        },
        {
          id: 'USR-003',
          firstName: 'Mike',
          lastName: 'Wilson',
          email: '<EMAIL>',
          phone: '+****************',
          role: 'Technician',
          department: 'Maintenance',
          status: 'Active',
          lastLogin: '2024-01-15 12:20',
          createdAt: '2023-09-10',
          permissions: ['read', 'write'],
          avatar: null,
          employeeId: 'EMP-003',
          location: 'Maintenance Bay',
          supervisor: 'John Smith',
          certifications: ['Drone Maintenance Certified', 'Electronics Repair'],
          notes: 'Expert in drone hardware maintenance and repairs'
        },
        {
          id: 'USR-004',
          firstName: 'Emily',
          lastName: 'Chen',
          email: '<EMAIL>',
          phone: '+****************',
          role: 'Compliance Officer',
          department: 'Compliance',
          status: 'Active',
          lastLogin: '2024-01-15 10:15',
          createdAt: '2023-07-05',
          permissions: ['read', 'write', 'audit'],
          avatar: null,
          employeeId: 'EMP-004',
          location: 'Compliance Office',
          supervisor: 'John Smith',
          certifications: ['Regulatory Compliance', 'Audit Management'],
          notes: 'Handles all regulatory compliance and audit activities'
        },
        {
          id: 'USR-005',
          firstName: 'David',
          lastName: 'Rodriguez',
          email: '<EMAIL>',
          phone: '+****************',
          role: 'Viewer',
          department: 'Operations',
          status: 'Inactive',
          lastLogin: '2024-01-10 09:30',
          createdAt: '2023-11-15',
          permissions: ['read'],
          avatar: null,
          employeeId: 'EMP-005',
          location: 'Remote',
          supervisor: 'Sarah Johnson',
          certifications: ['Basic Drone Operations'],
          notes: 'Temporary access for project review'
        }
      ]);
    }
  }, [users.length]);

  // Form state for new/edit user
  const [userForm, setUserForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: 'Viewer',
    department: '',
    employeeId: '',
    location: '',
    supervisor: '',
    certifications: '',
    notes: ''
  });

  const handleAddUser = (e) => {
    e.preventDefault();
    const newUser = {
      id: `USR-${String(users.length + 1).padStart(3, '0')}`,
      ...userForm,
      status: 'Active',
      lastLogin: 'Never',
      createdAt: new Date().toISOString().split('T')[0],
      permissions: getRolePermissions(userForm.role),
      avatar: null,
      certifications: userForm.certifications.split(',').map(cert => cert.trim()).filter(cert => cert)
    };
    
    setUsers([...users, newUser]);
    setUserForm({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      role: 'Viewer',
      department: '',
      employeeId: '',
      location: '',
      supervisor: '',
      certifications: '',
      notes: ''
    });
    setShowAddModal(false);
  };

  const handleEditUser = (e) => {
    e.preventDefault();
    setUsers(users.map(user => 
      user.id === selectedUser.id 
        ? { 
            ...user, 
            ...userForm,
            permissions: getRolePermissions(userForm.role),
            certifications: userForm.certifications.split(',').map(cert => cert.trim()).filter(cert => cert)
          }
        : user
    ));
    setShowEditModal(false);
    setSelectedUser(null);
  };

  const handleDeleteUser = (id) => {
    setUsers(users.filter(user => user.id !== id));
  };

  const handleToggleStatus = (id) => {
    setUsers(users.map(user => 
      user.id === id 
        ? { ...user, status: user.status === 'Active' ? 'Inactive' : 'Active' }
        : user
    ));
  };

  const getRolePermissions = (role) => {
    switch (role) {
      case 'QC Manager': return ['read', 'write', 'delete', 'admin'];
      case 'QC Inspector': return ['read', 'write'];
      case 'Technician': return ['read', 'write'];
      case 'Compliance Officer': return ['read', 'write', 'audit'];
      case 'Viewer': return ['read'];
      default: return ['read'];
    }
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'QC Manager': return <Crown className="w-4 h-4 text-purple-500" />;
      case 'QC Inspector': return <Shield className="w-4 h-4 text-blue-500" />;
      case 'Technician': return <Settings className="w-4 h-4 text-green-500" />;
      case 'Compliance Officer': return <CheckCircle className="w-4 h-4 text-orange-500" />;
      case 'Viewer': return <Eye className="w-4 h-4 text-gray-500" />;
      default: return <User className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    return status === 'Active' 
      ? 'bg-green-100 text-green-700 border-green-200'
      : 'bg-red-100 text-red-700 border-red-200';
  };

  const getStatusIcon = (status) => {
    return status === 'Active' 
      ? <CheckCircle className="w-4 h-4" />
      : <XCircle className="w-4 h-4" />;
  };

  const openEditModal = (user) => {
    setSelectedUser(user);
    setUserForm({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      role: user.role,
      department: user.department,
      employeeId: user.employeeId,
      location: user.location,
      supervisor: user.supervisor,
      certifications: user.certifications.join(', '),
      notes: user.notes
    });
    setShowEditModal(true);
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.employeeId.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRole = filterRole === 'all' || user.role === filterRole;
    const matchesStatus = filterStatus === 'all' || user.status.toLowerCase() === filterStatus.toLowerCase();
    return matchesSearch && matchesRole && matchesStatus;
  });

  const headerActions = (
    <div className="flex items-center justify-between gap-4">
      <div className="flex items-center gap-4 flex-1">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search users..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4 py-2 w-80 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <select
          value={filterRole}
          onChange={(e) => setFilterRole(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Roles</option>
          <option value="QC Manager">QC Manager</option>
          <option value="QC Inspector">QC Inspector</option>
          <option value="Technician">Technician</option>
          <option value="Compliance Officer">Compliance Officer</option>
          <option value="Viewer">Viewer</option>
        </select>

        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      <div className="flex items-center gap-3">
        <button
          onClick={() => setShowAddModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <UserPlus className="w-4 h-4" />
          Add User
        </button>
      </div>
    </div>
  );

  return (
    <QCLayout
      title="User Management"
      subtitle="Manage user accounts, roles, and permissions"
      actions={headerActions}
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{filteredUsers.length}</p>
                <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                  <Users className="w-3 h-3" />
                  Registered
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0f4ff'}}>
                <Users className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredUsers.filter(u => u.status === 'Active').length}
                </p>
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <CheckCircle className="w-3 h-3" />
                  Online
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#f0fdf4'}}>
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Managers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredUsers.filter(u => u.role === 'QC Manager').length}
                </p>
                <p className="text-sm text-purple-600 flex items-center gap-1 mt-1">
                  <Crown className="w-3 h-3" />
                  Admin
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#faf5ff'}}>
                <Crown className="w-6 h-6 text-purple-500" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Inactive</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredUsers.filter(u => u.status === 'Inactive').length}
                </p>
                <p className="text-sm text-red-600 flex items-center gap-1 mt-1">
                  <XCircle className="w-3 h-3" />
                  Disabled
                </p>
              </div>
              <div className="w-12 h-12 rounded-lg flex items-center justify-center" style={{backgroundColor: '#fef2f2'}}>
                <XCircle className="w-6 h-6 text-red-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">User Accounts</h3>
                <p className="text-sm text-gray-600 mt-1">Manage user accounts, roles, and permissions</p>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role & Department
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact Info
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status & Activity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50 transition-colors">
                    {/* User Details */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">
                            {user.firstName[0]}{user.lastName[0]}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {user.firstName} {user.lastName}
                          </div>
                          <div className="text-sm text-gray-500">{user.employeeId}</div>
                          <div className="text-xs text-gray-400">
                            Joined: {new Date(user.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Role & Department */}
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          {getRoleIcon(user.role)}
                          <span className="text-sm font-medium text-gray-900">{user.role}</span>
                        </div>
                        <div className="text-sm text-gray-600">{user.department}</div>
                        <div className="text-xs text-gray-500">
                          Location: {user.location}
                        </div>
                        {user.supervisor && (
                          <div className="text-xs text-gray-500">
                            Reports to: {user.supervisor}
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Contact Info */}
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm text-gray-900">
                          <Mail className="w-3 h-3 text-gray-400" />
                          <span className="truncate max-w-[200px]">{user.email}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Phone className="w-3 h-3 text-gray-400" />
                          <span>{user.phone}</span>
                        </div>
                        {user.certifications.length > 0 && (
                          <div className="text-xs text-gray-500">
                            Certs: {user.certifications.slice(0, 2).join(', ')}
                            {user.certifications.length > 2 && ` +${user.certifications.length - 2} more`}
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Status & Activity */}
                    <td className="px-6 py-4">
                      <div className="space-y-2">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(user.status)}`}>
                          {getStatusIcon(user.status)}
                          <span className="ml-1">{user.status}</span>
                        </span>

                        <div className="text-xs text-gray-500">
                          <div>Last login:</div>
                          <div>{user.lastLogin}</div>
                        </div>

                        <div className="text-xs text-gray-500">
                          Permissions: {user.permissions.length}
                        </div>
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <button
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-blue-50"
                          title="Edit User"
                          onClick={() => openEditModal(user)}
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          className={`p-2 transition-colors rounded-lg ${
                            user.status === 'Active'
                              ? 'text-gray-400 hover:text-red-600 hover:bg-red-50'
                              : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                          }`}
                          title={user.status === 'Active' ? 'Deactivate User' : 'Activate User'}
                          onClick={() => handleToggleStatus(user.id)}
                        >
                          {user.status === 'Active' ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                        <button
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                          onClick={() => handleDeleteUser(user.id)}
                          title="Delete User"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Add User Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Add New User</h3>
                <p className="text-sm text-gray-600 mt-1">Create a new user account</p>
              </div>

              <form onSubmit={handleAddUser} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    <input
                      type="text"
                      value={userForm.firstName}
                      onChange={(e) => setUserForm({...userForm, firstName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    <input
                      type="text"
                      value={userForm.lastName}
                      onChange={(e) => setUserForm({...userForm, lastName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input
                      type="email"
                      value={userForm.email}
                      onChange={(e) => setUserForm({...userForm, email: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input
                      type="tel"
                      value={userForm.phone}
                      onChange={(e) => setUserForm({...userForm, phone: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Employee ID</label>
                    <input
                      type="text"
                      value={userForm.employeeId}
                      onChange={(e) => setUserForm({...userForm, employeeId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <select
                      value={userForm.role}
                      onChange={(e) => setUserForm({...userForm, role: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="Viewer">Viewer</option>
                      <option value="Technician">Technician</option>
                      <option value="QC Inspector">QC Inspector</option>
                      <option value="Compliance Officer">Compliance Officer</option>
                      <option value="QC Manager">QC Manager</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                    <input
                      type="text"
                      value={userForm.department}
                      onChange={(e) => setUserForm({...userForm, department: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input
                      type="text"
                      value={userForm.location}
                      onChange={(e) => setUserForm({...userForm, location: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Supervisor</label>
                    <input
                      type="text"
                      value={userForm.supervisor}
                      onChange={(e) => setUserForm({...userForm, supervisor: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Certifications (comma-separated)</label>
                    <input
                      type="text"
                      value={userForm.certifications}
                      onChange={(e) => setUserForm({...userForm, certifications: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., FAA Part 107, ISO 9001"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                  <textarea
                    value={userForm.notes}
                    onChange={(e) => setUserForm({...userForm, notes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Additional notes about the user..."
                  />
                </div>

                <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Add User
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Edit User Modal */}
        {showEditModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Edit User</h3>
                <p className="text-sm text-gray-600 mt-1">Update user account information</p>
              </div>

              <form onSubmit={handleEditUser} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    <input
                      type="text"
                      value={userForm.firstName}
                      onChange={(e) => setUserForm({...userForm, firstName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    <input
                      type="text"
                      value={userForm.lastName}
                      onChange={(e) => setUserForm({...userForm, lastName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input
                      type="email"
                      value={userForm.email}
                      onChange={(e) => setUserForm({...userForm, email: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input
                      type="tel"
                      value={userForm.phone}
                      onChange={(e) => setUserForm({...userForm, phone: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Employee ID</label>
                    <input
                      type="text"
                      value={userForm.employeeId}
                      onChange={(e) => setUserForm({...userForm, employeeId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <select
                      value={userForm.role}
                      onChange={(e) => setUserForm({...userForm, role: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="Viewer">Viewer</option>
                      <option value="Technician">Technician</option>
                      <option value="QC Inspector">QC Inspector</option>
                      <option value="Compliance Officer">Compliance Officer</option>
                      <option value="QC Manager">QC Manager</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                    <input
                      type="text"
                      value={userForm.department}
                      onChange={(e) => setUserForm({...userForm, department: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input
                      type="text"
                      value={userForm.location}
                      onChange={(e) => setUserForm({...userForm, location: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Supervisor</label>
                    <input
                      type="text"
                      value={userForm.supervisor}
                      onChange={(e) => setUserForm({...userForm, supervisor: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Certifications (comma-separated)</label>
                    <input
                      type="text"
                      value={userForm.certifications}
                      onChange={(e) => setUserForm({...userForm, certifications: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., FAA Part 107, ISO 9001"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                  <textarea
                    value={userForm.notes}
                    onChange={(e) => setUserForm({...userForm, notes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="3"
                    placeholder="Additional notes about the user..."
                  />
                </div>

                <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowEditModal(false)}
                    className="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Update User
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </QCLayout>
  );
};

export default UserManagement;
